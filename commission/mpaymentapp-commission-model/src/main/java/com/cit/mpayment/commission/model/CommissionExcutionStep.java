package com.cit.mpayment.commission.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.cit.mpaymentapp.model.Enums.AmountType;
import com.cit.mpaymentapp.model.base.IBaseModel;
import com.cit.mpaymentapp.model.commission.CommissionProfile;
import com.cit.mpaymentapp.model.fees.FeeProfile;
import com.cit.mpaymentapp.model.risk.RiskProfile;
import com.cit.mpaymentapp.model.transaction.TransactionActionType;
import com.cit.mpaymentapp.model.transaction.TransactionStatus;

@Entity
@Table(name = "Commission_Exec_Steps")
public class CommissionExcutionStep implements IBaseModel, Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE)
	@Column(name = "Commission_Step_ID", nullable = false)
	private Long commissionStepID;
	@Column(name = "Reversible")
	private boolean reversible=false;
	@Column(name = "Need_Approval")
	private Boolean needApproval=false;
	@Column(name = "Account_ID", nullable = false)
	private Long accountID;
	@ManyToOne
	@JoinColumn(name = "Comm_Exec_Summary_ID", nullable = false)
	private CommissionExcutionSummary commissionExecutionSummary;
	@Column(name = "Account_Owner_Type_ID")
	private int accountOwnerType;
	@OneToOne
	@JoinColumn(name = "Step_Action_Type", nullable = false)
	private TransactionActionType stepActionType;
	@Column(name = "Amount_Type", nullable = true)
	@Enumerated
	private AmountType amountType;
	@Column(name = "Amount", nullable = false)
	private BigDecimal amount;
	@Column(name = "Transaction_Status")
	@Enumerated
	private TransactionStatus stepStatus;
	@Column(name = "Step_Start_Date", nullable = false)
	@Temporal(TemporalType.TIMESTAMP)
	private Date stepStartDate;
	@Column(name = "Step_End_Date", nullable = false)
	@Temporal(TemporalType.TIMESTAMP)
	private Date stepEndDate;
	@ManyToOne
	@JoinColumn(name = "Risk_Profile")
	private RiskProfile riskProfile;
	@ManyToOne
	@JoinColumn(name = "Fee_Profile")
	private FeeProfile feeProfile;
	@ManyToOne
	@JoinColumn(name = "Commission_Profile")
	private CommissionProfile commissionProfile;
	private boolean isCommissionStep=false;
	private boolean isFeesStep=false;
	private BigDecimal balance;
	@Column(name="wallet_Short_Code")
	private String walletShortCode;

	public Long getCommissionStepID() {
		return commissionStepID;
	}

	public void setCommissionStepID(Long transactionStepID) {
		this.commissionStepID = transactionStepID;
	}

	public boolean isReversible() {
		return reversible;
	}

	public void setReversible(boolean reversible) {
		this.reversible = reversible;
	}

	public Boolean isNeedApproval() {
		return needApproval;
	}

	public void setNeedApproval(Boolean needApproval) {
		this.needApproval = needApproval;
	}

	public Long getAccountID() {
		return accountID;
	}

	public void setAccountID(Long accountID) {
		this.accountID = accountID;
	}

	public int getAccountOwnerType() {
		return accountOwnerType;
	}

	public void setAccountOwnerType(int accountOwnerType) {
		this.accountOwnerType = accountOwnerType;
	}

	public TransactionActionType getStepActionType() {
		return stepActionType;
	}

	public void setStepActionType(TransactionActionType stepActionType) {
		this.stepActionType = stepActionType;
	}

	public AmountType getAmountType() {
		return amountType;
	}

	public void setAmountType(AmountType amountType) {
		this.amountType = amountType;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public TransactionStatus getStepStatus() {
		return stepStatus;
	}

	public void setStepStatus(TransactionStatus stepStatus) {
		this.stepStatus = stepStatus;
	}

	public Date getStepStartDate() {
		return stepStartDate;
	}

	public void setStepStartDate(Date stepStartDate) {
		this.stepStartDate = stepStartDate;
	}

	public Date getStepEndDate() {
		return stepEndDate;
	}

	public void setStepEndDate(Date stepEndDate) {
		this.stepEndDate = stepEndDate;
	}

	public RiskProfile getRiskProfile() {
		return riskProfile;
	}

	public void setRiskProfile(RiskProfile riskProfile) {
		this.riskProfile = riskProfile;
	}

	public FeeProfile getFeeProfile() {
		return feeProfile;
	}

	public void setFeeProfile(FeeProfile feeProfile) {
		this.feeProfile = feeProfile;
	}

	public CommissionProfile getCommissionProfile() {
		return commissionProfile;
	}

	public void setCommissionProfile(CommissionProfile commissionProfile) {
		this.commissionProfile = commissionProfile;
	}

	public boolean isCommissionStep() {
		return isCommissionStep;
	}

	public void setCommissionStep(boolean isCommissionStep) {
		this.isCommissionStep = isCommissionStep;
	}

	public boolean isFeesStep() {
		return isFeesStep;
	}

	public void setFeesStep(boolean isFeesStep) {
		this.isFeesStep = isFeesStep;
	}

	public BigDecimal getBalance() {
		return balance;
	}

	public void setBalance(BigDecimal balance) {
		this.balance = balance;
	}

	public String getWalletShortCode() {
		return walletShortCode;
	}

	public void setWalletShortCode(String walletShortCode) {
		this.walletShortCode = walletShortCode;
	}

	public boolean equals(Object obj) {
		if (obj instanceof CommissionExcutionStep) {
			CommissionExcutionStep other = (CommissionExcutionStep) obj;
			if (this.getCommissionStepID() != null) {
				return this.getCommissionStepID().equals(
						other.getCommissionStepID());
			}
		}
		return false;
	}

	public int hashCode() {
		if (getCommissionStepID() != null) {
			return getCommissionStepID().intValue();
		} else {
			return 1;
		}

	}

	public String toString() {
		return "CommissionExecutionStep [commissionStepID="
				+ commissionStepID + ", reversible=" + reversible
				+ ", needApproval=" + needApproval + ", accountID=" + accountID
				+ ", accountOwnerType=" + accountOwnerType
				+ ", stepActionType=" + stepActionType + ", amountType="
				+ amountType + ", amount=" + amount + ", stepStatus="
				+ stepStatus + ", stepStartDate=" + stepStartDate
				+ ", stepEndDate=" + stepEndDate + ", riskProfile="
				+ riskProfile + ", feeProfile=" + feeProfile
				+ ", commissionProfile=" + commissionProfile
				+ ", isCommissionStep=" + isCommissionStep + ", isFeesStep="
				+ isFeesStep + ", balance=" + balance + "]";
	}

	public CommissionExcutionSummary getCommissionExecutionSummary() {
		return commissionExecutionSummary;
	}

	public void setCommissionExecutionSummary(
			CommissionExcutionSummary commissionExecutionSummary) {
		this.commissionExecutionSummary = commissionExecutionSummary;
	}
}