package com.cit.mpaymentapp.model.security;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.cit.mpaymentapp.model.base.IBaseModel;
@Entity
@Table(name="users")
public class Users implements IBaseModel, Serializable {

	
	public String toString() {
		return "Users [userid=" + userid + ", paswword=" + paswword + "]";
	}
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Id
	@Column(name="userid")
	private String userid;
	@Column(name="Password")
private String paswword;
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getPaswword() {
		return paswword;
	}
	public void setPaswword(String paswword) {
		this.paswword = paswword;
	}
}
