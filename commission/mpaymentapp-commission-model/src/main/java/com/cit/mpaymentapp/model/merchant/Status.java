package com.cit.mpaymentapp.model.merchant;

import java.io.Serializable;


public class Status implements Serializable{
	public Status(){}
	public enum PaymentStatus {
		Pending, Completed, Expired, Failed, WaitingApproval
	}
	private PaymentStatus status;
	private String errorCode;
	public PaymentStatus getStatus() {
		return status;
	}
	public void setStatus(PaymentStatus status) {
		this.status = status;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
}