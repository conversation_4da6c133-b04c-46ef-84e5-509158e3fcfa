package com.cit.mpaymentapp.model.message;

import java.io.Serializable;

import com.cit.mpaymentapp.model.Enums.PaymentMethodType;

public class PaymentMethodDetail implements Serializable {
	private static final long serialVersionUID = -2818425958081896419L;
	private String garShortCode;
	private PaymentMethodType paymentMethodType;
	private BankInformation bank;
	
	public PaymentMethodDetail() {
		bank = new BankInformation();
	}
	
	public String getGarShortCode() {
		return garShortCode;
	}
	public void setGarShortCode(String garShortCode) {
		this.garShortCode = garShortCode;
	}
	public PaymentMethodType getPaymentMethodType() {
		return paymentMethodType;
	}
	public void setPaymentMethodType(PaymentMethodType paymentMethodType) {
		this.paymentMethodType = paymentMethodType;
	}
	public BankInformation getBank() {
		return bank;
	}
	public void setBank(BankInformation bank) {
		this.bank = bank;
	}
	@Override
	public String toString() {
		return "PaymentMethodDetail [garShortCode=" + garShortCode
				+ ", paymentMethodType=" + paymentMethodType + ", bank=" + bank
				+ ", card=" + "]";
	}
}
