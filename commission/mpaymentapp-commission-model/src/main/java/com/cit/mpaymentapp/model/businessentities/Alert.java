package com.cit.mpaymentapp.model.businessentities;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.ForeignKey;

import com.cit.mpaymentapp.model.Enums.AlertType;
import com.cit.mpaymentapp.model.base.IBaseModel;

@Entity
@Table(name = "Mp_Alert")
public class Alert implements Serializable, IBaseModel {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE)
	@Column(name = "Id")
	private Long id;
	@Column(name = "Name", nullable = false, length = 16)
	private String name;
	@Enumerated
	private AlertType type;
	@Column(name = "Subject", nullable = false, length = 30)
	private String subject;
	@Column(name = "Message", nullable = false, length = 500)
	private String message;
	
	@ManyToOne
	@ForeignKey(name = "RiskProfile_Wallet")
	@JoinColumn(name = "BUSINESS_ENTITY_ID", referencedColumnName = "BUSINESS_ENTITY_ID", nullable = true)
	private BusinesEntity wallet;
	@Column(name = "Send_Email")
	private boolean sendEmail = false;
	@Column(name = "Send_Sms")
	private boolean sendSms = false;

	public BusinesEntity getWallet() {
		return wallet;
	}

	public void setWallet(BusinesEntity wallet) {
		this.wallet = wallet;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public boolean isSendEmail() {
		return sendEmail;
	}

	public void setSendEmail(boolean sendEmail) {
		this.sendEmail = sendEmail;
	}

	public boolean isSendSms() {
		return sendSms;
	}

	public void setSendSms(boolean sendSms) {
		this.sendSms = sendSms;
	}

	public AlertType getType() {
		return type;
	}

	public void setType(AlertType type) {
		this.type = type;
	}

}
