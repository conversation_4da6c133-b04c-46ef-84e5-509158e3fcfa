package com.cit.mpayment.startup;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.persistence.Query;

import com.cit.mpayment.commission.model.CommissionDistrbution;
import com.cit.mpayment.commission.model.CommissionDistrbutionStep;
import com.cit.query.engine.impl.ServiceQueryEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.cit.mpayment.common.CommissionDataManager;
import com.cit.mpayment.dto.AgentCommissionData;
import com.cit.mpayment.dto.CommissionNeededData;
import com.cit.mpayment.dto.PartnerCommissionData;
import com.cit.mpayment.enums.PropertyFileName;
import com.cit.mpayment.property.PropertyLoader;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.account.BusinessAccount;
import com.cit.mpaymentapp.model.businessentities.BusinesEntity;
import com.cit.mpaymentapp.model.commission.CommissionPayment;
import com.cit.mpaymentapp.model.commission.CommissionProfile;
import com.cit.mpaymentapp.model.commission.CommissionValueModel;


@Component
public class CommissionDataManagerImpl implements CommissionDataManager {

    @Autowired
    IBaseDao baseDao;

    private static Map<String, Long> walletIdDataMap;

    static {
        walletIdDataMap = new HashMap<String, Long>();
    }

    private BusinessAccount getWalletCommissionAccount(Long businessEntityId) {

        Integer commissionAccountType = Integer.parseInt("" + PropertyLoader.loadProperty(PropertyFileName.Config, "COMMISSION_ACCOUNT"));
        List<BusinessAccount> businessAccount = baseDao.executeDynamicQuery("select model from BusinessAccount model where model.businessEnityID=" + businessEntityId + " and model.accountType.accountTypeID=" + commissionAccountType, BusinessAccount.class, false, false);
        if (businessAccount != null && businessAccount.size() > 0) {
            return businessAccount.get(0);
        }
        // handling Exception
        return null;
    }

    private BusinessAccount getWalletBalancingAccount(Long businessEntityId) {

        Integer commissionAccountType = Integer.parseInt("" + PropertyLoader.loadProperty(PropertyFileName.Config, "BALANCING_ACCOUNT"));
        List<BusinessAccount> businessAccount = baseDao.executeDynamicQuery("select model from BusinessAccount model where model.businessEnityID=" + businessEntityId + " and model.accountType.accountTypeID=" + commissionAccountType, BusinessAccount.class, false, false);
        if (businessAccount != null && businessAccount.size() > 0) {
            return businessAccount.get(0);
        }
        // handling Exception
        return null;
    }

    private BusinessAccount getRefundAccount(Long walletId) {
        Integer settlementAccountType = Integer.parseInt("" + PropertyLoader.loadProperty(PropertyFileName.Config, "SETTLEMENT_ACCOUNT"));
        Integer refundHierarchyType = Integer.parseInt("" + PropertyLoader.loadProperty(PropertyFileName.Config, "REFUND_HIERARCHY_TYPE"));
        List<BusinessAccount> businessAccount = baseDao.executeDynamicQuery("select BA from BusinessEntity BE join BE.businessAccounts BA where BA.accountType.accountTypeID=" + settlementAccountType + " and BE.businessHierarchyType=" + refundHierarchyType + " and BE.wallet.businessEntityID= " + walletId, BusinessAccount.class, false, false);
        if (businessAccount != null && businessAccount.size() > 0) {
            return businessAccount.get(0);
        }
        return null;
    }

    public Map<Long, BusinesEntity> getBusinessEntities(Long walletId) {
        String querySql = ServiceQueryEngine.getQueryStringToExecute("getBusinessEntities" , this.getClass(), String.valueOf(walletId));
        Query query = (Query) baseDao.executeNativeQuery(querySql);
        List<Object> results = query.getResultList();
        Map<Long, BusinesEntity> businessEntitiesMap = new HashMap<Long, BusinesEntity>();
        if (results != null && results.size() > 0) {
            for (Object result : results) {
                Object[] r = (Object[]) result;
                Long parentId = (Long) ((BigDecimal) r[1]).longValue();
                Long businessEntityId = (Long) ((BigDecimal) r[2]).longValue();
                Object commissionProfileID = r[3];
                int hierarchyLevel = (Integer) ((BigDecimal) r[5]).intValue();
                Object commissionDistributionId = r[6];

                CommissionProfile commissionProfile = null;
                if (commissionProfileID != null) {
                    commissionProfile = new CommissionProfile();
                    commissionProfile.setId(Long.parseLong("" + commissionProfileID));
                    commissionProfile.setLevel(Integer.parseInt("" + hierarchyLevel));
                }
                CommissionDistrbution commissionDistribution = null;
                if (commissionDistributionId != null) {
                    commissionDistribution = new CommissionDistrbution();
                    commissionDistribution.setId(Long.parseLong("" + commissionDistributionId));

                }

                BusinesEntity parentBusinessEntity = new BusinesEntity();
                parentBusinessEntity.setBusinessEntityID(parentId);
                BusinesEntity businessEntity = new BusinesEntity();
                businessEntity.setParentBusinessEntity(parentBusinessEntity);
                businessEntity.setCommissionProfileID(commissionProfile);
                businessEntity.setHierarchyLevel(hierarchyLevel);
                businessEntity.setCommissionDistributionId(commissionDistribution);
                businessEntitiesMap.put(businessEntityId, businessEntity);
            }
            return businessEntitiesMap;
        }
        // handling Exception
        return null;
    }

    public CommissionNeededData prepareNeededData(String walletShortCode) {
        Long walletId = null;
        if (!walletIdDataMap.containsKey(walletShortCode)) {
            String querySql = ServiceQueryEngine.getQueryStringToExecute("prepareNeededData" , this.getClass(),walletShortCode);
                    Query query = (Query) baseDao.executeNativeQuery(querySql);
            Object results = query.getResultList();
            if (results != null) {
                ArrayList<?> result = (ArrayList<?>) results;
                BigDecimal res = (BigDecimal) result.get(0);
                walletId = res.longValue();
                walletIdDataMap.put(walletShortCode, walletId);
            }
        } else {
            walletId = walletIdDataMap.get(walletShortCode);
        }
        CommissionNeededData commissionNeededData = new CommissionNeededData();
        commissionNeededData.setWalletCommissionAccount(getWalletCommissionAccount(walletId));
        commissionNeededData.setRefundAccount(getRefundAccount(walletId));
        commissionNeededData.setBusinessEntities(getBusinessEntities(walletId));
//		commissionNeededData.setAgentCommission(getAgentCommission(walletId));
//		commissionNeededData.setPartnerCommission(getPartnerCommission(walletId));
        commissionNeededData.setWalletBalancingAccount(getWalletBalancingAccount(walletId));
        commissionNeededData.setWalletId(walletId);
        return commissionNeededData;
    }
}