package com.cit.error.exception;

import javax.ejb.ApplicationException;

@ApplicationException(rollback = false )
public class NonTransactionalException extends Exception {

	private static final long serialVersionUID = 1L;

	private String errorCode;
	
	public String getErrorCode() {
		return this.errorCode;
	}
	

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public NonTransactionalException(String errorCode) {
		super(errorCode);
		this.errorCode = errorCode;	
	}

	public NonTransactionalException(String errorCode, Throwable init) {
		super(errorCode, init);
		this.errorCode = errorCode;
	}
}
