package com.cit.shared.error.exception;

import java.util.Map;

public class <PERSON>shException  extends GeneralFailureException {
	
	private static final long serialVersionUID = 1L;

	public static final String MISSING_DATA="VAL08401";
	public static final String REMITTER_ALREADY_EXIST="VAL08402";
	public static final String BENEFICIARY_ALREADY_EXIST="VAL08403";
	public static final String CANCEL_TRANSACTION_FAILED="EXT08404";
	public static final String CONFIRM_TRANSACTION_FAILED="EXT08405";
	public static final String CREATE_BENEFICIARY_FAILED="EXT08406";
	public static final String CREATE_REMITTER_FAILED="EXT08407";
	public static final String GET_TRANSACTION_DETAILS_FAILED="EXT08408";
	public static final String INITIATE_SESSION_FAILED="EXT08409";
	public static final String INITIATE_TRANSACTION_FAILED="EXT08410";
	public static final String INITIATE_CASH_TRANSFER_COLLECTION_FAILED="EXT08411";
	public static final String ACCEPT_TRANSFER_COLLECTION_FAILED="EXT08412";
	public static final String INVALID_DATA_FROM_AFRICASH="EXT08413";
	public static final String TRANSACTION_DOES_NOT_EXIST="VAL08414";
	public static final String AFRICASH_GENERIC_ERROR="EXT08415";
	public static final String BENEFICIARY_DOES_NOT_EXIST="VAL08416";
	public static final String REMITTER_DOES_NOT_EXIST="VAL08417";
	public static final String CHECK_TRANSFER_STATUS_FAILED="VAL08418";
	public static final String UPDATE_BENEFICIARY_FAILED="EXT08419";
	public static final String NUMBERS_NOT_ALLOWED_IN_NAMES="VAL08420";
	
	public AfricashException(String errorCode) {
		super(errorCode);
	}
	public AfricashException(String errorCode, Map<String, String> varMap) {
        super(errorCode, varMap);
    }
	
}
