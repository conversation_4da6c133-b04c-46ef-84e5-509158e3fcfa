package com.cit.shared.error.exception;

public class CommissionDistributionException extends GeneralFailureException {

	public enum Type {

		LevelMismatch,

		NoDefaultProfileForLevel;
	}

	public final static String COMMISSION_FOR_SERVICE_TYPE_NOT_CONFIGURED = "BUS205563";

	private static final long serialVersionUID = 4470475803787659380L;

	private int businessEntityLevel;

	private int profileLevel;

	private Type type;

	public CommissionDistributionException(String errorCode) {
		super(errorCode);
	}

	public CommissionDistributionException(Type type, int businessEntityLevel) {
		this.type = type;
		this.businessEntityLevel = businessEntityLevel;
	}

	public CommissionDistributionException(Type type, int businessEntityLevel, int profileLevel) {
		this.businessEntityLevel = businessEntityLevel;
		this.profileLevel = profileLevel;
		this.type = type;
	}

	public int getBusinessEntityLevel() {
		return businessEntityLevel;
	}

	public String getMessage() {
		if (type != null) {
			switch (type) {
				case LevelMismatch :
					return "Error due to Hierarchy Level mismatch , Business entity level is " + businessEntityLevel + " and profile is level" + profileLevel;
				case NoDefaultProfileForLevel :
					return "Error due to No Profile assigned to Business entity";

				default :
					return "Error During Commission Distribution";
			}
		} else {
			return super.getMessage();
		}
	}

	public int getProfileLevel() {
		return profileLevel;
	}

	public void setBusinessEntityLevel(int businessEntityLevel) {
		this.businessEntityLevel = businessEntityLevel;
	}

	public void setProfileLevel(int profileLevel) {
		this.profileLevel = profileLevel;
	}
}
