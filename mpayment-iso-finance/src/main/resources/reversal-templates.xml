<?xml version="1.0" encoding="UTF-8"?>
<tns:template xmlns:tns="http://www.cit.com/iso-template" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.cit.com/iso-template iso-template.xsd ">
  <tns:name>Reversal Messages</tns:name>
  <tns:id>04</tns:id>
  <tns:message>
    <tns:mti>0420</tns:mti>
    <tns:name>Reversal Request</tns:name>
    <tns:bitmap>
      <tns:primary>
        <tns:field name="TransmissionDateTime">
        	<tns:source>
        	java.text.DateFormat formatter = new java.text.SimpleDateFormat("MMddHHmmss");
        	formatter.format(new java.util.Date());
        	</tns:source>
        </tns:field>
        <tns:field name="SystemTraceAuditNumber">
        	<tns:source>
        	java.util.Random random = new java.util.Random();
        	random.nextInt(10000);
        	</tns:source>
        </tns:field>
        <tns:field name="AdditionalDataPrivate">
        	<tns:source>789</tns:source>
        </tns:field>
      </tns:primary>
      <tns:secondary>
      	<tns:field name="NetworkManagementInformation">
      		<tns:source>310</tns:source>
      	</tns:field>
      </tns:secondary>
    </tns:bitmap>
  </tns:message>
  <tns:message>
    <tns:mti>0430</tns:mti>
    <tns:name>Reversal Response</tns:name>
    <tns:bitmap>
      <tns:primary>
        <tns:field name="SystemTraceAuditNumber">
        	<tns:source>message.getCustomerFields().put(SystemTraceAuditNumber)</tns:source>
        </tns:field>
      </tns:primary>
    </tns:bitmap>
  </tns:message>
</tns:template>
