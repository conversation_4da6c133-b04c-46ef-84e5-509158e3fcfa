package com.cit.mpaymentapp.msisdn.inquiry;

import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.international.airtime.common.AbstractAirtimeResponse;
import com.cit.mpaymentapp.international.airtime.common.AirtimeOptions;
import com.cit.mpaymentapp.international.airtime.common.AirtimeProduct;

@JsonAutoDetect
public class MsisdnInquiryResponse extends AbstractAirtimeResponse{
	
	private static final long serialVersionUID = 1L;
	private AirtimeOptions opts;
	private List<AirtimeProduct> products;
	
	public AirtimeOptions getOpts() {
		return opts;
	}
	public void setOpts(AirtimeOptions opts) {
		this.opts = opts;
	}
	public List<AirtimeProduct> getProducts() {
		return products;
	}
	public void setProducts(List<AirtimeProduct> products) {
		this.products = products;
	}
}
