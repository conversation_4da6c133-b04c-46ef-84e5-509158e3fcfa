package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;
import java.math.BigDecimal;

import org.codehaus.jackson.annotate.JsonAutoDetect;


public class AirtimeProduct implements Serializable {

	private static final long serialVersionUID = 1L;
	private String product_type;
	private String produc_name;
	private String product_id;
	private boolean openRange;
	private BigDecimal openRangeMin;
	private BigDecimal openRangeMax;
	private Double rate;
	private String topup_currency;
	private String currency;
	private BigDecimal denomination;
	private Long data_amount;
	private Integer step;
	private String price;
	private String operator_name;
	
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public boolean isOpenRange() {
		return openRange;
	}
	public void setOpenRange(boolean openRange) {
		this.openRange = openRange;
	}
	public BigDecimal getOpenRangeMin() {
		return openRangeMin;
	}
	public void setOpenRangeMin(BigDecimal openRangeMin) {
		this.openRangeMin = openRangeMin;
	}
	public BigDecimal getOpenRangeMax() {
		return openRangeMax;
	}
	public void setOpenRangeMax(BigDecimal openRangeMax) {
		this.openRangeMax = openRangeMax;
	}
	public Double getRate() {
		return rate;
	}
	public void setRate(Double rate) {
		this.rate = rate;
	}
	public String getTopup_currency() {
		return topup_currency;
	}
	public void setTopup_currency(String topup_currency) {
		this.topup_currency = topup_currency;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public BigDecimal getDenomination() {
		return denomination;
	}
	public void setDenomination(BigDecimal denomination) {
		this.denomination = denomination;
	}
	public Long getData_amount() {
		return data_amount;
	}
	public void setData_amount(Long data_amount) {
		this.data_amount = data_amount;
	}
	public Integer getStep() {
		return step;
	}
	public void setStep(Integer step) {
		this.step = step;
	}
	public String getPrice() {
		return price;
	}
	public void setPrice(String price) {
		this.price = price;
	}
	public String getOperator_name() {
		return operator_name;
	}
	public void setOperator_name(String operator_name) {
		this.operator_name = operator_name;
	}
	public String getProduct_type() {
		return product_type;
	}
	public void setProduct_type(String product_type) {
		this.product_type = product_type;
	}
	public String getProduc_name() {
		return produc_name;
	}
	public void setProduc_name(String produc_name) {
		this.produc_name = produc_name;
	}
	@Override
	public String toString() {
		return "AirtimeProduct [product_type=" + product_type + ", produc_name=" + produc_name + ", product_id="
				+ product_id + ", openRange=" + openRange + ", openRangeMin=" + openRangeMin + ", openRangeMax="
				+ openRangeMax + ", rate=" + rate + ", topup_currency=" + topup_currency + ", currency=" + currency
				+ ", denomination=" + denomination + ", data_amount=" + data_amount + ", step=" + step + ", price="
				+ price + ", operator_name=" + operator_name + "]";
	}
	
	
}