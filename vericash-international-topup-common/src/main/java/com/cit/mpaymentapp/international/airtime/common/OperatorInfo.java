package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class OperatorInfo implements Serializable{

	
	private static final long serialVersionUID = 1L;
	private String _id;
	private String iso;
	private String country;
	private String operator_name;
	private String operator_id;
	private String code;
	private List<AirtimeProduct> products;

	public String getId() {
	return _id;
	}

	public void setId(String _id) {
	this._id = _id;
	}

	public String getIso() {
	return iso;
	}

	public void setIso(String iso) {
	this.iso = iso;
	}

	public String getCountry() {
	return country;
	}

	public void setCountry(String country) {
	this.country = country;
	}

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public String getOperator_name() {
		return operator_name;
	}

	public void setOperator_name(String operator_name) {
		this.operator_name = operator_name;
	}

	public String getOperator_id() {
		return operator_id;
	}

	public void setOperator_id(String operator_id) {
		this.operator_id = operator_id;
	}

	public String getCode() {
	return code;
	}

	public void setCode(String code) {
	this.code = code;
	}

	public List<AirtimeProduct> getProducts() {
		return products;
	}

	public void setProducts(List<AirtimeProduct> products) {
		this.products = products;
	}

	@Override
	public String toString() {
		return "OperatorInfo [_id=" + _id + ", iso=" + iso + ", country=" + country + ", operator_name=" + operator_name
				+ ", operator_id=" + operator_id + ", code=" + code + ", products=" + products + "]";
	}
	
	
	
}
