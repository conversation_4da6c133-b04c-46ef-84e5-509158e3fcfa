package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class MsisdnInquiryResult implements Serializable {

	private static final long serialVersionUID = 1L;
	private AirtimeOptions opts;
	private List<OperatorInfo> operatorsList = new ArrayList<OperatorInfo>();

	public AirtimeOptions getOpts() {
		return opts;
	}

	public void setOpts(AirtimeOptions opts) {
		this.opts = opts;
	}

	public List<OperatorInfo> getOperatorsList() {
		return operatorsList;
	}

	public void setOperatorsList(List<OperatorInfo> operatorsList) {
		this.operatorsList = operatorsList;
	}

	@Override
	public String toString() {
		return "MsisdnInquiryResult [opts=" + opts + ", operatorsList=" + operatorsList + "]";
	}
	

}
