package com.cit.mpaymentapp.international.airtime.token;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class InternationalTopupTokenRequest {

	private String username;
	private String password;

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

}
