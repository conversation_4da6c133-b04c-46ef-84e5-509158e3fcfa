package com.cit.mpaymentapp.international.airtime.token;

import java.util.Date;

public class InternationalTopupToken {

	private static String internationalTopupToken = null;
	private static Date tokenExpiryDate = null;

	public static String getInternationalTopupToken() {
		return internationalTopupToken;
	}

	public static void setInternationalTopupToken(String internationalTopupToken) {
		InternationalTopupToken.internationalTopupToken = internationalTopupToken;
	}

	public static Date getTokenExpiryDate() {
		return tokenExpiryDate;
	}

	public static void setTokenExpiryDate(Date tokenExpiryDate) {
		InternationalTopupToken.tokenExpiryDate = tokenExpiryDate;
	}
	
	public static Boolean isExpired(){
		
		if(internationalTopupToken == null){
			return true;
			
		}else if(new Date().getTime()+(24*60*60*1000) > tokenExpiryDate.getTime()){
			return true;
		}
		
		return false;
	}
}
