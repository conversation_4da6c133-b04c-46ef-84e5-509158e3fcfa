package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;


public class InternationalTopup implements Serializable {

	private static final long serialVersionUID = 1L;
	private int product_type;
	private String operator_id;
	private MsisdnInquiryResult msisdnInquiryResult;
	private List<OperatorInfo> operatorsList;
	private AirtimeProduct topupProduct;

	public MsisdnInquiryResult getMsisdnInquiryResult() {
		return msisdnInquiryResult;
	}
	public void setMsisdnInquiryResult(MsisdnInquiryResult msisdnInquiryResult) {
		this.msisdnInquiryResult = msisdnInquiryResult;
	}
	public List<OperatorInfo> getOperatorsList() {
		return operatorsList;
	}
	public void setOperatorsList(List<OperatorInfo> operatorsList) {
		this.operatorsList = operatorsList;
	}
	public AirtimeProduct getTopupProduct() {
		return topupProduct;
	}
	public void setTopupProduct(AirtimeProduct topupProduct) {
		this.topupProduct = topupProduct;
	}
	public int getProduct_type() {
		return product_type;
	}
	public void setProduct_type(int product_type) {
		this.product_type = product_type;
	}
	public String getOperator_id() {
		return operator_id;
	}
	public void setOperator_id(String operator_id) {
		this.operator_id = operator_id;
	}
	@Override
	public String toString() {
		return "InternationalTopup [product_type=" + product_type + ", operator_id=" + operator_id
				+ ", msisdnInquiryResult=" + msisdnInquiryResult + ", operatorsList=" + operatorsList
				+ ", topupProduct=" + topupProduct + "]";
	}
	
	
	
}
