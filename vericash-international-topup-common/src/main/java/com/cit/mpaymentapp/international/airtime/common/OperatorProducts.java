package com.cit.mpaymentapp.international.airtime.common;
import java.util.ArrayList;
import java.util.List;

public class OperatorProducts {
	private String operator_name;

    private String iso;

    private String operator_id;

    private String code;

    private List<Product> products=new ArrayList<Product>();

    private String country;

    public String getOperator_name ()
    {
        return operator_name;
    }

    public void setOperator_name (String operator_name)
    {
        this.operator_name = operator_name;
    }

    public String getIso ()
    {
        return iso;
    }

    public void setIso (String iso)
    {
        this.iso = iso;
    }

    public String getOperator_id ()
    {
        return operator_id;
    }

    public void setOperator_id (String operator_id)
    {
        this.operator_id = operator_id;
    }

    public String getCode ()
    {
        return code;
    }

    public void setCode (String code)
    {
        this.code = code;
    }

    public List<Product> getProducts ()
    {
        return products;
    }

    public void setProducts (List<Product> products)
    {
        this.products = products;
    }

    public String getCountry ()
    {
        return country;
    }

    public void setCountry (String country)
    {
        this.country = country;
    }

    @Override
    public String toString()
    {
        return "OperatorsProducts [operator_name = "+operator_name+", iso = "+iso+", operator_id = "+operator_id+", code = "+code+", products = "+products+", country = "+country+"]";
    }
}
