package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;

public class AirtimeOptions implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private boolean hasOpenRange;
	private String country;
	private String operator;
	private String iso;
	private boolean canOverride;
	private String msisdn;

	public boolean getHasOpenRange() {
		return hasOpenRange;
	}

	public void setHasOpenRange(boolean hasOpenRange) {
		this.hasOpenRange = hasOpenRange;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getIso() {
		return iso;
	}

	public void setIso(String iso) {
		this.iso = iso;
	}

	public boolean getCanOverride() {
		return canOverride;
	}

	public void setCanOverride(boolean canOverride) {
		this.canOverride = canOverride;
	}

	public String getMsisdn() {
		return msisdn;
	}

	public void setMsisdn(String msisdn) {
		this.msisdn = msisdn;
	}

	@Override
	public String toString() {
		return "AirtimeOptions [hasOpenRange=" + hasOpenRange + ", country=" + country + ", operator=" + operator
				+ ", iso=" + iso + ", canOverride=" + canOverride + ", msisdn=" + msisdn + "]";
	}
	

}
