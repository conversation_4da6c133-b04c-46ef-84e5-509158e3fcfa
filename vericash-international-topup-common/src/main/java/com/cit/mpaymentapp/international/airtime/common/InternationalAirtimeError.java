package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;

public class InternationalAirtimeError implements Serializable{
	private static final long serialVersionUID = 1L;
	private String name;
	private String message;
	private String code;
    private int status;
    private Inner inner;
    
    class Inner{
    	private String message;

		public String getMessage() {
			return message;
		}

		public void setMessage(String message) {
			this.message = message;
		}    	
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public Inner getInner() {
		return inner;
	}

	public void setInner(Inner inner) {
		this.inner = inner;
	}
    
    
}
