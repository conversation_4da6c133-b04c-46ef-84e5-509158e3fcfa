package com.cit.mpaymentapp.international.airtime.common;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public abstract class AbstractAirtimeResponse implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private InternationalAirtimeError error;
	private Integer status;
	private String message;
	private String code;
	
	public InternationalAirtimeError getError() {
		return error;
	}
	public void setError(InternationalAirtimeError error) {
		this.error = error;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	
}
