package com.cit.mpaymentapp.international.airtime.common;

import java.math.BigDecimal;

public class Product {
	private String topup_currency;
    private String product_id;
    private String openRangeMin;
    private boolean openRange;
    private String produc_name;
    private String product_type;
    private String step;
    private String currency;
    private String openRangeMax;
    private BigDecimal price;
    private BigDecimal denomination;
    
    public String getTopup_currency ()
    {
        return topup_currency;
    }

    public void setTopup_currency (String topup_currency)
    {
        this.topup_currency = topup_currency;
    }

    public String getProduct_id ()
    {
        return product_id;
    }

    public void setProduct_id (String product_id)
    {
        this.product_id = product_id;
    }

    public String getOpenRangeMin ()
    {
        return openRangeMin;
    }

    public void setOpenRangeMin (String openRangeMin)
    {
        this.openRangeMin = openRangeMin;
    }

    public boolean getOpenRange ()
    {
        return openRange;
    }

    public void setOpenRange (boolean openRange)
    {
        this.openRange = openRange;
    }

    public String getProduc_name ()
    {
        return produc_name;
    }

    public void setProduc_name (String produc_name)
    {
        this.produc_name = produc_name;
    }

    public String getProduct_type ()
    {
        return product_type;
    }

    public void setProduct_type (String product_type)
    {
        this.product_type = product_type;
    }

    public String getStep ()
    {
        return step;
    }

    public void setStep (String step)
    {
        this.step = step;
    }

    public String getCurrency ()
    {
        return currency;
    }

    public void setCurrency (String currency)
    {
        this.currency = currency;
    }

    public String getOpenRangeMax ()
    {
        return openRangeMax;
    }

    public void setOpenRangeMax (String openRangeMax)
    {
        this.openRangeMax = openRangeMax;
    }

    public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getDenomination() {
		return denomination;
	}

	public void setDenomination(BigDecimal denomination) {
		this.denomination = denomination;
	}

	@Override
	public String toString() {
		return "Product [topup_currency=" + topup_currency + ", product_id=" + product_id + ", openRangeMin="
				+ openRangeMin + ", openRange=" + openRange + ", produc_name=" + produc_name + ", product_type="
				+ product_type + ", step=" + step + ", currency=" + currency + ", openRangeMax=" + openRangeMax
				+ ", price=" + price + ", denomination=" + denomination + "]";
	}
}
