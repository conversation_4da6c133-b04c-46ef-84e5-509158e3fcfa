package com.cit.mpaymentapp.international.airtime.token;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class InternationalTopupTokenResponse{

	
	private String token;
	private String expires;

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getExpires() {
		return expires;
	}

	public void setExpires(String expires) {
		this.expires = expires;
	}

}
