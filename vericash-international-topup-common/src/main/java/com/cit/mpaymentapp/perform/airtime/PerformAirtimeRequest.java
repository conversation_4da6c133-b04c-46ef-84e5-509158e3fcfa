package com.cit.mpaymentapp.perform.airtime;

import java.io.Serializable;
import java.math.BigDecimal;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class PerformAirtimeRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	private String product_id;
	private BigDecimal denomination;
	private boolean send_sms = false;
	private String sms_text = "";
	private String customer_reference;

	public String getProduct_id() {
		return product_id;
	}

	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}

	public BigDecimal getDenomination() {
		return denomination;
	}

	public void setDenomination(BigDecimal denomination) {
		this.denomination = denomination;
	}

	public boolean isSend_sms() {
		return send_sms;
	}

	public void setSend_sms(boolean send_sms) {
		this.send_sms = send_sms;
	}

	public String getSms_text() {
		return sms_text;
	}

	public void setSms_text(String sms_text) {
		this.sms_text = sms_text;
	}

	public String getCustomer_reference() {
		return customer_reference;
	}

	public void setCustomer_reference(String customer_reference) {
		this.customer_reference = customer_reference;
	}
}
