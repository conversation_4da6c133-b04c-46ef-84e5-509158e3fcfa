package com.cit.mpaymentapp.perform.airtime;

import java.io.Serializable;
import java.math.BigDecimal;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class TopupProduct implements Serializable{
	private static final long serialVersionUID = 1L;
	private String product_id;
    private BigDecimal topupAmount;
	
    public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public BigDecimal getTopupAmount() {
		return topupAmount;
	}
	public void setTopupAmount(BigDecimal topupAmount) {
		this.topupAmount = topupAmount;
	}
    
    
}
