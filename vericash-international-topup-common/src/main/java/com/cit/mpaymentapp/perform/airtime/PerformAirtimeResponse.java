














package com.cit.mpaymentapp.perform.airtime;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.international.airtime.common.AbstractAirtimeResponse;

@JsonAutoDetect
public class PerformAirtimeResponse extends AbstractAirtimeResponse implements Serializable{
	private static final long serialVersionUID = 1L;
	private String reference;
	private Integer paid_amount;
	private String paid_currency;
	private Integer topup_amount;
	private String topup_currency;
	private String target;
	private String product_id;
	private String time;
	private String country;
	private String operator_name;
	private Integer completed_in;
	private String customer_reference;
	private String api_transactionid;
	private Boolean pin_based;

	
	public String getReference() {
		return reference;
	}
	public void setReference(String reference) {
		this.reference = reference;
	}
	public Integer getPaid_amount() {
		return paid_amount;
	}
	public void setPaid_amount(Integer paid_amount) {
		this.paid_amount = paid_amount;
	}
	public String getPaid_currency() {
		return paid_currency;
	}
	public void setPaid_currency(String paid_currency) {
		this.paid_currency = paid_currency;
	}
	public Integer getTopup_amount() {
		return topup_amount;
	}
	public void setTopup_amount(Integer topup_amount) {
		this.topup_amount = topup_amount;
	}
	public String getTopup_currency() {
		return topup_currency;
	}
	public void setTopup_currency(String topup_currency) {
		this.topup_currency = topup_currency;
	}
	public String getTarget() {
		return target;
	}
	public void setTarget(String target) {
		this.target = target;
	}
	public String getProduct_id() {
		return product_id;
	}
	public void setProduct_id(String product_id) {
		this.product_id = product_id;
	}
	public String getTime() {
		return time;
	}
	public void setTime(String time) {
		this.time = time;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getOperator_name() {
		return operator_name;
	}
	public void setOperator_name(String operator_name) {
		this.operator_name = operator_name;
	}
	public Integer getCompleted_in() {
		return completed_in;
	}
	public void setCompleted_in(Integer completed_in) {
		this.completed_in = completed_in;
	}
	public String getCustomer_reference() {
		return customer_reference;
	}
	public void setCustomer_reference(String customer_reference) {
		this.customer_reference = customer_reference;
	}
	public String getApi_transactionid() {
		return api_transactionid;
	}
	public void setApi_transactionid(String api_transactionid) {
		this.api_transactionid = api_transactionid;
	}
	public Boolean getPin_based() {
		return pin_based;
	}
	public void setPin_based(Boolean pin_based) {
		this.pin_based = pin_based;
	}

}