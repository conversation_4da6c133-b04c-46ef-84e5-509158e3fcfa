<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	
	<parent>
		<groupId>com.cit.mpaymentapp.vericash.international.topup</groupId>
		<artifactId>vericash-international-topup</artifactId>
		<version>1.0.0.0</version>
		<relativePath>../vericash-international-topup</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>vericash-international-topup-common</artifactId>
	<name>vericash-international-topup-common</name>
	
	<dependencies>
		<dependency>
	    	<groupId>org.codehaus.jackson</groupId>
	    	<artifactId>jackson-core-asl</artifactId>
	    	<version>1.9.2</version>
	    	<scope>provided</scope>
		</dependency>
	
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>11</source>
					<target>11</target>
				</configuration>
			</plugin>
		</plugins>
		
	</build>

</project>
