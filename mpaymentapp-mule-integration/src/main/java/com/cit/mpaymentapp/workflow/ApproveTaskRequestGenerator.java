//package com.cit.mpaymentapp.workflow;
//
//import com.cit.mpayment.vericash.portal.VericashPortalRequestGenerator;
//import com.cit.mpaymentapp.common.message.BusinessMessage;
//import com.cit.mpaymentapp.common.vericash.portal.message.VericashPortalMessage;
//import com.cit.shared.error.exception.SMEException;
//
//public class ApproveTaskRequestGenerator extends VericashPortalRequestGenerator {
//	private static final String APPROVE_FAMILY_MEMBER_TASK_SERVICE_CODE = "889082";
//
//	@Override
//	protected void generateRequest(BusinessMessage businessMessage,
//			VericashPortalMessage workflowMessage) throws SMEException {
//		if(APPROVE_FAMILY_MEMBER_TASK_SERVICE_CODE.equals(businessMessage.getServiceInfo().getCode()))
//			workflowMessage.getAdditionalData().put("userType", "FamilyCustomer");
//		else
//			workflowMessage.getAdditionalData().put("userType", "BECustomer");
//		String taskId = businessMessage.getSMEInfo() != null && businessMessage.getSMEInfo().getTaskInfo() != null ? businessMessage.getSMEInfo().getTaskInfo().getTaskId() : null;
//		if (taskId != null) {
//			workflowMessage.getPayload().put("taskId", taskId);
//		} else {
//			throw new SMEException(SMEException.INVALID_TASK_ID);
//		}
//
//	}
//
//}
