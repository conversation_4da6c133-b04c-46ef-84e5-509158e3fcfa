package com.cit.mpayment.corbanking.response.validator;

import java.util.Map;

import org.apache.xmlbeans.XmlException;

import com.cit.mpayment.corbanking.request.AbsCoreBankingResponseValidator;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.corebanking.deactivate.C24TRANRESDocument;
import com.cit.mpaymentapp.corebanking.deactivate.C24TRANRESDocument.C24TRANRES;
import com.cit.shared.error.exception.GeneralFailureException;

public class DeactivateResponseValidator extends AbsCoreBankingResponseValidator{

	@Override
	protected BusinessMessage setResponse(BusinessMessage businessMessage, String payLoadObject, Map<String,String> muleMessageProperties) throws XmlException {
		return businessMessage;
	}

	@Override
	protected BusinessMessage getResponseCode(BusinessMessage businessMessage,
			String payLoadObject) throws XmlException,
			GeneralFailureException {

		C24TRANRESDocument responseDocument = C24TRANRESDocument.Factory.parse(payLoadObject.toString());
		C24TRANRES c24tranresResponse = responseDocument.getC24TRANRES();

		businessMessage.getExternalInterfaceResponse().setErrorCode(c24tranresResponse.getACTIONCODE());
		return businessMessage;
	}
}
