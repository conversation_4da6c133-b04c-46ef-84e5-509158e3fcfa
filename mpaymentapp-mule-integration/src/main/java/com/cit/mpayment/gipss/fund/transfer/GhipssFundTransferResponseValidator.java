package com.cit.mpayment.gipss.fund.transfer;

import org.datacontract.schemas._2004._07.itftservice.TransferFundsResponse;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GhipssException;

public class GhipssFundTransferResponseValidator{
	
	private static final String SUCCESS = "0";
	
	public Object onCall(BusinessMessage businessMessage, TransferFundsResponse transferFundsResponse) throws Exception {
		
		if(!transferFundsResponse.getResponseValue().getValue().equals(SUCCESS))
			throw new GhipssException(GhipssException.GHIPSS_TRANSACTION_FAILED);
		
		return businessMessage;
	}
	
}
