package com.cit.mpayment.nonuba.account.component;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;
import com.google.common.base.Strings;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.StringTokenizer;

public class SaveBase64ImageComponent {


	public Object onCall(BusinessMessage businessMessage) throws Exception {

		if(businessMessage.getNonUbaBankAccount().getBvn() == null){
			String imageName=null;
			String imageHttpUrl=null;
			String base64EncodedImage=businessMessage.getNonUbaBankAccount().getDocumentPhoto();
			//
			imageName=saveBase64Image(base64EncodedImage);
			if(Strings.isNullOrEmpty(imageName)){//failed to save image
				throw new GeneralFailureException("VAL0099");
			}
			imageHttpUrl=System.getenv("nonUBA_image_view")+imageName;
			//
			//set image http url into businessMessage instead of base 64 encoding.
			businessMessage.getNonUbaBankAccount().setDocumentPhoto(imageHttpUrl);
			businessMessage.getPrimarySenderInfo().getPersonalDetails().setImage(imageName);
		}
		return businessMessage;
	}
	public  String saveBase64Image(String base64EncodedImage){
		try {
			//base64EncodedImage to portal
			
			StringTokenizer st=new StringTokenizer(base64EncodedImage,",");
			String type=st.nextToken();
			 
		    String imgType=type.substring(type.indexOf("/")+1, type.indexOf(";"));
			//set default imgType if not found
		    if(Strings.isNullOrEmpty(imgType)){
		    	imgType="jpeg";
		    }
			String encodedImage=st.nextToken();
			String url=System.getenv("saveBase64ImageUrl")+"?imgType="+imgType+"";
	        URL obj = new URL(url);
	        HttpURLConnection con = (HttpURLConnection) obj.openConnection();

	        //add reuqest header
	        con.setRequestMethod("POST");
	        con.setRequestProperty("Content-Type", "text/plain; charset=utf-8");
	        //con.setRequestProperty("testKey", "testValue");
	        String body =new String(encodedImage);

	        
	        // Send post request
	        con.setDoOutput(true);
	        DataOutputStream wr = new DataOutputStream(con.getOutputStream());
	        
	        wr.writeBytes(body);
	        wr.flush();
	        wr.close();

	        
	        int responseCode = con.getResponseCode();
	        BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
	        String inputLine;
	        StringBuffer response = new StringBuffer();

	        while ((inputLine = in.readLine()) != null) {
	            response.append(inputLine);
	        }
	        
	        in.close();
			
            return response.toString();//return image name if saved successfully
             
        } catch (Exception ex) {

        	return null;
        }
	}
}
