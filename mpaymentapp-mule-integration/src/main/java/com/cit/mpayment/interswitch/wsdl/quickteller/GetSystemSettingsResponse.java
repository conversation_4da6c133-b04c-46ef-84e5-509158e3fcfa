
package com.cit.mpayment.interswitch.wsdl.quickteller;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getSystemSettingsResult"
})
@XmlRootElement(name = "GetSystemSettingsResponse")
public class GetSystemSettingsResponse {

    @XmlElementRef(name = "GetSystemSettingsResult", namespace = "http://services.interswitchng.com/quicktellerservice/", type = JAXBElement.class)
    protected JAXBElement<String> getSystemSettingsResult;

    public JAXBElement<String> getGetSystemSettingsResult() {
        return getSystemSettingsResult;
    }

    public void setGetSystemSettingsResult(JAXBElement<String> value) {
        this.getSystemSettingsResult = ((JAXBElement<String> ) value);
    }

}
