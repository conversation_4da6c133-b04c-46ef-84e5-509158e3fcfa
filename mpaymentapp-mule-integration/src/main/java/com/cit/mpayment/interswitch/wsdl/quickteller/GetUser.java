
package com.cit.mpayment.interswitch.wsdl.quickteller;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "xmlParams"
})
@XmlRootElement(name = "GetUser")
public class GetUser {

    @XmlElementRef(name = "xmlParams", namespace = "http://services.interswitchng.com/quicktellerservice/", type = JAXBElement.class)
    protected JAXBElement<String> xmlParams;

    public JAXBElement<String> getXmlParams() {
        return xmlParams;
    }

    public void setXmlParams(JAXBElement<String> value) {
        this.xmlParams = ((JAXBElement<String> ) value);
    }

}
