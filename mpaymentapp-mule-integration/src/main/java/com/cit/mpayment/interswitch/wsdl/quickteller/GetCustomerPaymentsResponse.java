
package com.cit.mpayment.interswitch.wsdl.quickteller;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getCustomerPaymentsResult"
})
@XmlRootElement(name = "GetCustomerPaymentsResponse")
public class GetCustomerPaymentsResponse {

    @XmlElementRef(name = "GetCustomerPaymentsResult", namespace = "http://services.interswitchng.com/quicktellerservice/", type = JAXBElement.class)
    protected JAXBElement<String> getCustomerPaymentsResult;

    public JAXBElement<String> getGetCustomerPaymentsResult() {
        return getCustomerPaymentsResult;
    }

    public void setGetCustomerPaymentsResult(JAXBElement<String> value) {
        this.getCustomerPaymentsResult = ((JAXBElement<String> ) value);
    }

}
