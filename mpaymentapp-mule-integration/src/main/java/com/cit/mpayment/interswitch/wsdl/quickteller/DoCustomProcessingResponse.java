
package com.cit.mpayment.interswitch.wsdl.quickteller;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "doCustomProcessingResult"
})
@XmlRootElement(name = "DoCustomProcessingResponse")
public class DoCustomProcessingResponse {

    @XmlElementRef(name = "DoCustomProcessingResult", namespace = "http://services.interswitchng.com/quicktellerservice/", type = JAXBElement.class)
    protected JAXBElement<String> doCustomProcessingResult;

    public JAXBElement<String> getDoCustomProcessingResult() {
        return doCustomProcessingResult;
    }

    public void setDoCustomProcessingResult(JAXBElement<String> value) {
        this.doCustomProcessingResult = ((JAXBElement<String> ) value);
    }

}
