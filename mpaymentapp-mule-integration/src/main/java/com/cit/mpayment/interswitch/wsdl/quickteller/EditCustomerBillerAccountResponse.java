
package com.cit.mpayment.interswitch.wsdl.quickteller;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "editCustomerBillerAccountResult"
})
@XmlRootElement(name = "EditCustomerBillerAccountResponse")
public class EditCustomerBillerAccountResponse {

    @XmlElementRef(name = "EditCustomerBillerAccountResult", namespace = "http://services.interswitchng.com/quicktellerservice/", type = JAXBElement.class)
    protected JAXBElement<String> editCustomerBillerAccountResult;

    public JAXBElement<String> getEditCustomerBillerAccountResult() {
        return editCustomerBillerAccountResult;
    }

    public void setEditCustomerBillerAccountResult(JAXBElement<String> value) {
        this.editCustomerBillerAccountResult = ((JAXBElement<String> ) value);
    }

}
