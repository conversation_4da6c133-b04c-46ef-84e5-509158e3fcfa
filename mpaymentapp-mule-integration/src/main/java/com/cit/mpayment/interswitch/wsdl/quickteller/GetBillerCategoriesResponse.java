
package com.cit.mpayment.interswitch.wsdl.quickteller;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "getBillerCategoriesResult"
})
@XmlRootElement(name = "GetBillerCategoriesResponse")
public class GetBillerCategoriesResponse {

    @XmlElementRef(name = "GetBillerCategoriesResult", namespace = "http://services.interswitchng.com/quicktellerservice/", type = JAXBElement.class)
    protected JAXBElement<String> getBillerCategoriesResult;

    public JAXBElement<String> getGetBillerCategoriesResult() {
        return getBillerCategoriesResult;
    }

    public void setGetBillerCategoriesResult(JAXBElement<String> value) {
        this.getBillerCategoriesResult = ((JAXBElement<String> ) value);
    }

}
