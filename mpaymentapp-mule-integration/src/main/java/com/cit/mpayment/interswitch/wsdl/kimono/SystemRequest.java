
package com.cit.mpayment.interswitch.wsdl.kimono;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SystemRequest", propOrder = {
        "queries/service",
    "routeId"
})
@XmlSeeAlso({
    KMsgRequest.class
})
public class SystemRequest {

    @XmlElement(name = "Service")
    protected String service;
    @XmlElement(name = "RouteId")
    protected String routeId;

    public String getService() {
        return service;
    }

    public void setService(String value) {
        this.service = value;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String value) {
        this.routeId = value;
    }

}
