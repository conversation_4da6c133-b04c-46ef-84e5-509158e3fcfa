package com.cit.mpayment.component;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ResourceBundle;

import com.cit.shared.error.exception.GeneralFailureException;

public class HashingManager {
	
	private static ResourceBundle bundle = ResourceBundle.getBundle("hash-conf");

	public static String hash(String saltString) throws GeneralFailureException {
		String digestOutput = null;

		MessageDigest digest = null;
		try {
			digest = MessageDigest.getInstance(bundle.getString("hashing.algorithm"));
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			throw new GeneralFailureException(GeneralFailureException.HASHING_ERROR);
		}
		byte[] digestBytes = null;
		try {
			digestBytes = digest.digest(saltString
					.getBytes(bundle.getString("charset.name")));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			throw new GeneralFailureException(GeneralFailureException.HASHING_ERROR);
		}
		digestOutput = convertToHex(digestBytes);
		
		return digestOutput;
	}
	
	public static String hash(String saltString, String algorithm) throws GeneralFailureException {

		String digestOutput = null;

		MessageDigest digest = null;
		try {
			digest = MessageDigest.getInstance(algorithm);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
			throw new GeneralFailureException();
		}
		byte[] digestBytes = null;
		try {
			digestBytes = digest.digest(saltString
					.getBytes(bundle.getString("charset.name")));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			throw new GeneralFailureException();
		}
		digestOutput = convertToHex(digestBytes);
		
		return digestOutput;
	
	}
	
	public static boolean verify(String message, String digest) throws GeneralFailureException{
		boolean isVerified = false;
		try {
			isVerified = MessageDigest.isEqual(hash(message).getBytes(bundle.getString("charset.name")),digest.getBytes(bundle.getString("charset.name")));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			throw new GeneralFailureException();
		}
		return isVerified;
	}

	private static String convertToHex(byte[] source) {
		StringBuffer hexString = new StringBuffer();
		for (int i = 0; i < source.length; i++) {
			String hex = Integer.toHexString(0xff & source[i]);
			if (hex.length() == 1)
				hexString.append('0');
			hexString.append(hex);
		}
		return hexString.toString();
	}
}
