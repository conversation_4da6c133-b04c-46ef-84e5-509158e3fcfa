package com.cit.mpayment.exception;

import java.util.Map;

import mpayment.logging.ServerLoggerInf;

import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.shared.error.util.ExceptionResolver;
import com.cit.shared.error.util.ExceptionUtil;

public class IntegrationExceptionHandlerComponent {

	public static StatusType handleException(String errorCode)
	{
		StatusType statusType =new StatusType();
		statusType.setErrorFlag(true);
		statusType.setStatusCode(errorCode);
		resolveException(errorCode);
		return statusType;
	}

	public static StatusType handleException(String errorCode,Map<String, String> varMap)
	{
		StatusType statusType =new StatusType();
		statusType.setErrorFlag(true);
		statusType.setStatusCode(errorCode);
		statusType.setRuleNames(varMap);
		resolveException(errorCode,varMap);
		return statusType;
	}

	private static void resolveException(String errorCode)
	{
		ExceptionResolver resolver = ExceptionUtil.handle(errorCode);
		String description = resolver.getDescription();
	}
	private static void resolveException(String errorCode,Map<String,String> varMap)
	{
		ExceptionResolver resolver = ExceptionUtil.handle(errorCode,varMap);
		String description = resolver.getDescription();
	}
}
