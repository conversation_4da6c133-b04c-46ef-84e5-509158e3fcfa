<?xml version="1.0" encoding="utf-8"?>
<Configuration>

    <Appenders>
        <RollingFile name="file" fileName="${sys:mule.home}${sys:file.separator}logs${sys:file.separator}mpaymentapp-mule-integration.log" 
                 filePattern="${sys:mule.home}${sys:file.separator}logs${sys:file.separator}test-%i.log">
            <PatternLayout pattern="%-5p %d [%t] [event: %X{correlationId}] %c: %m%n" />
            <SizeBasedTriggeringPolicy size="10 MB" />
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        <Console name="Console" target="SYSTEM_OUT">
        	<PatternLayout pattern="%-5p %d [%t] [event: %X{correlationId}] %c: %m%n" />
        </Console>
    </Appenders>
    <Loggers>
        <AsyncLogger name="org.mule.service.http" level="WARN"/>
        <AsyncLogger name="org.mule.extension.http" level="WARN"/>
    	<AsyncLogger name="org.mule.service.http.impl.service.HttpMessageLogger" level="DEBUG" />
        <AsyncLogger name="org.mule.runtime.core.internal.processor.LoggerMessageProcessor" level="INFO"/>
 
        <AsyncRoot level="INFO">
            <AppenderRef ref="file" />
            <AppenderRef ref="Console"/>
        </AsyncRoot>
    </Loggers>
</Configuration>
