package com.citglobal.mpayment.common.middleware;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Repository;

import com.citglobal.mpayment.common.middleware.dto.RequestInfo;
import com.citglobal.mpayment.common.middleware.dto.RequestParserObj;
import com.citglobal.mpayment.common.middleware.dto.RequestServiceObj;
import com.citglobal.mpayment.common.middleware.dto.RequestValidationObj;
import com.citglobal.mpayment.common.middleware.dto.RequestValidationResultObj;
import com.citglobal.mpayment.common.middleware.exception.ParameterValidationException;
import com.citglobal.mpayment.common.middleware.exception.RequestParsingException;
import com.citglobal.mpayment.common.middleware.exception.RequestServiceException;
import com.citglobal.mpayment.common.middleware.exception.RequestValidationException;
import com.citglobal.mpayment.common.middleware.model.Service;
import com.citglobal.mpayment.common.middleware.parser.RequestParserInf;
import com.citglobal.mpayment.common.middleware.service.ServiceDirectoryInf;
import com.citglobal.mpayment.common.middleware.validation.ValidationRuleProcessorIfc;
import com.citglobal.mpayment.common.middleware.validation.rule.RequestValidationImpl;

@Repository
public class AbstractRequestProcessingImpl {
	
	private static final Logger transformerLog = Logger.getLogger("transformerLog");

	private ServiceDirectoryInf serviceDirectoryInf;
	
	private RequestParserInf requestParserInf;
	
	private RequestValidationImpl requestValidationImpl;
	
	public void setServiceDirectoryInf(ServiceDirectoryInf serviceDirectoryInf) {
		this.serviceDirectoryInf = serviceDirectoryInf;
	}
	
	public void setRequestParserInf(RequestParserInf requestParserInf) {
		this.requestParserInf = requestParserInf;
	}
	
	public void setRequestValidationImpl(
			RequestValidationImpl requestValidationImpl) {
		this.requestValidationImpl = requestValidationImpl;
	}
	
	public Service getService(RequestServiceObj requestServiceObj) throws RequestServiceException, ParameterValidationException {
		transformerLog.trace(requestServiceObj);

		
		transformerLog.info("Provided ServiceDirectory is [" + serviceDirectoryInf.getClass().getName() + "]");
		transformerLog.info("Calling Service Directory.");
		Service service = serviceDirectoryInf.getServiceDefinition(requestServiceObj);
		transformerLog.debug(service.toString());
		
		transformerLog.trace(service.toString());
		return service;
	}

	public List<RequestValidationObj> getRequestDataElements(Service service) throws RequestServiceException {
		return serviceDirectoryInf.getRequestValidations(service);
	}

	public Map<String, Object> parseRequest(RequestParserObj requestParserObj) throws RequestParsingException, RequestServiceException, ParameterValidationException {
		transformerLog.trace(requestParserObj);
		Map<String, Object> mapParse = new HashMap<String, Object>();
		
		transformerLog.info("Provided Parser is [" + requestParserInf.getClass().getName() + "]");
		transformerLog.info("Calling Request parser.");
		mapParse = requestParserInf.getRequestParser(requestParserObj);
		transformerLog.debug(mapParse);
		
		transformerLog.trace(mapParse);
		return mapParse;
	}

	public RequestValidationResultObj validateRequestParameter(RequestValidationObj requestValidationObj) throws RequestValidationException, ParameterValidationException {
		List<ValidationRuleProcessorIfc> requestValidationInfs = requestValidationImpl.getRequestValidation(requestValidationObj);
		RequestValidationResultObj result = new RequestValidationResultObj();
		result.setValid(true);
		for (Iterator<ValidationRuleProcessorIfc> iterator = requestValidationInfs.iterator(); iterator.hasNext();) {
			ValidationRuleProcessorIfc requestValidation = (ValidationRuleProcessorIfc) iterator.next();
			RequestValidationResultObj requestValidationResultObj = requestValidation.validate(requestValidationObj.getValue());
			if(!requestValidationResultObj.isValid()){
				result.setValid(false);
				break;
			}
			if(requestValidationResultObj.getPattern() != null){
				result.setPattern( requestValidationResultObj.getPattern() );
			}
		}
		
		return result;
	}

	public Map<String, RequestInfo> execute(String request) throws RequestServiceException, RequestParsingException, RequestValidationException, ParameterValidationException {
		transformerLog.trace("Json request [" + request + "].");
		Map<String, RequestInfo> result = new HashMap<String, RequestInfo>();
		RequestParserObj requestParserObj = new RequestParserObj();
		requestParserObj.setRequest(request);
		transformerLog.info("1) parsing request.");
		Map<String, Object> mapRequest = parseRequest(requestParserObj);
		transformerLog.debug(mapRequest);
		RequestServiceObj requestServiceObj = new RequestServiceObj();
		
		transformerLog.info("Getting Service Id and Wallet shortcode from request to define the type of the service and Location.");
		requestServiceObj.setServiceId( requestParserInf.getServiceID(mapRequest) );
		requestServiceObj.setShortCode( requestParserInf.getWalletShortcode(mapRequest) );
		transformerLog.debug(requestServiceObj.toString());
		transformerLog.info("2) Getting service.");
		Service service = getService(requestServiceObj);
		transformerLog.debug(service.toString());
		transformerLog.info("3) Getting DataElement.");
		List<RequestValidationObj> dataElementTypes = getRequestDataElements(service);
		transformerLog.info("Number of Element is [" + dataElementTypes.size() + "]");
		transformerLog.debug(dataElementTypes);
		
		RequestInfo requestInfo = null; 

		transformerLog.info("4) Loop over the Elements to check validation of the value.");
		for (RequestValidationObj requestValidationObj : dataElementTypes) {
			transformerLog.info("Getting value of Element [" + requestValidationObj.getDataElementType().getBMAttributeName() + "]");
			requestValidationObj.setValue( requestParserInf.getValue(requestValidationObj, mapRequest));
			transformerLog.info("Value of element [" + requestValidationObj.getDataElementType().getBMAttributeName() + "] is [" + requestValidationObj.getValue() + "]");
			transformerLog.debug(requestValidationObj.toString());
			transformerLog.info("Validate value depend on the provided validation rules.");
			RequestValidationResultObj valedationResult = validateRequestParameter(requestValidationObj);
			transformerLog.debug("ValedationResult=[" + valedationResult + "].");
			if(!valedationResult.isValid()){
				transformerLog.error("DataElement value is [not] valid.");
				throw new RequestValidationException(RequestValidationException.REQUEST_DATA_ELEMENT_IS_NOT_VALID,  RequestValidationException.getVarMapNameAndValue(requestValidationObj.getDataElementType().getName(), requestValidationObj.getValue()));
			}
			transformerLog.info("Setting Object with the value.");
			requestInfo = new RequestInfo(requestValidationObj.getValue(), requestValidationObj.getDataElementType().getBMAttributeName());
			requestInfo.setDateFormat(valedationResult.getPattern());
			
			transformerLog.info("Add Key and value to result Map.");
			result.put(requestValidationObj.getParentNameAsStringSeparated( requestValidationObj.getDataElementType().getName() ), requestInfo);
		}
		
		transformerLog.trace(result);
		return result;
	}

}
