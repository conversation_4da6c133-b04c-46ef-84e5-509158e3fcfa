<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:spring="http://www.springframework.org/schema/beans" xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:https="http://www.mulesoft.org/schema/mule/https" xmlns:servlet="http://www.mulesoft.org/schema/mule/servlet"
	xmlns:file="http://www.mulesoft.org/schema/mule/file" xmlns:jms="http://www.mulesoft.org/schema/mule/jms"
	xmlns:tcp="http://www.mulesoft.org/schema/mule/tcp" xmlns:vm="http://www.mulesoft.org/schema/mule/vm"
	xmlns:scripting="http://www.mulesoft.org/schema/mule/scripting"
	xsi:schemaLocation="
          http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/3.4/mule.xsd
          hhttp://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
          http://www.mulesoft.org/schema/mule/file http://www.mulesoft.org/schema/mule/file/3.4/mule-file.xsd
          http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/3.4/mule-http.xsd
          http://www.mulesoft.org/schema/mule/https http://www.mulesoft.org/schema/mule/https/3.4/mule-https.xsd
          http://www.mulesoft.org/schema/mule/servlet http://www.mulesoft.org/schema/mule/servlet/3.4/mule-servlet.xsd
          http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/3.4/mule-jms.xsd
          http://www.mulesoft.org/schema/mule/tcp http://www.mulesoft.org/schema/mule/tcp/3.4/mule-tcp.xsd
		  http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/3.4/mule-scripting.xsd
          http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/3.4/mule-vm.xsd">

	<jms:activemq-connector name="jmsConnector"  validateConnections="true" persistentDelivery="false"  numberOfConsumers="50" cacheJmsSessions="true"  brokerURL="${ACTIVEMQ_BROKER_URL}" specification="1.1">
		<receiver-threading-profile maxThreadsActive="100" /> 
		<reconnect-forever frequency="1000"></reconnect-forever>
	</jms:activemq-connector>

	<vm:connector name="vm.connector" numberOfConcurrentTransactedReceivers="100">
        <receiver-threading-profile doThreading="true" maxBufferSize="20" maxThreadsActive="100" maxThreadsIdle="10" threadWaitTimeout="12000" poolExhaustedAction="WAIT" />
        <dispatcher-threading-profile doThreading="true" maxBufferSize="20" maxThreadsActive="100" maxThreadsIdle="10" threadWaitTimeout="12000" poolExhaustedAction="WAIT" />
   </vm:connector>


	<tcp:connector name="tcpConnector" validateConnections="true"
		keepAlive="true">
		<tcp:direct-protocol payloadOnly="true" />
	</tcp:connector>
	<flow name="Echo Service Mule1" processingStrategy="synchronous">
		<http:inbound-endpoint host="0.0.0.0" port="9696"
			path="mpaymentapp-mule-1/echo/service" />
		<log-component />
	</flow>

	<asynchronous-processing-strategy name="AsynchronousProcessingStrategy" maxThreads="150" minThreads="10" threadTTL="30000" poolExhaustedAction="WAIT" threadWaitTimeout="20000" maxBufferSize="50"/>
	
	<configuration doc:name="Default Threading Profile Configuration">
        <default-dispatcher-threading-profile
                maxThreadsActive="100"
                maxThreadsIdle="20"/>

        <default-receiver-threading-profile
                maxThreadsActive="100"
                maxThreadsIdle="20"/>
    </configuration>

	<flow name="VERiCASH Mobile Synch Proxy" processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.vericash.synch.proxy.queue}" connector-ref="jmsConnector"
			responseTimeout="120000"  exchange-pattern="one-way"/>
		<logger level="ERROR"  message="------------Starting Mule processing  for message ID #[message.rootId]-----AT--  #[server.dateTime.format(&quot;yyyy.MM.dd G 'at' HH:mm:ss z&quot;)]"/>
		<set-variable value="#[correlationId]" variableName="correlationId"  />
		<flow-ref name="Request Information Before ProxyFlow" />
		<flow-ref name="ProxyFlow" />
		
		<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
		<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
		<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		<jms:outbound-endpoint queue="${outbound.vericash.synch.proxy.queue}" connector-ref="jmsConnector" exchange-pattern="one-way"/>
				
		<exception-strategy ref="ErrorHandlerAndReplier" />
	</flow>

	<flow name="UBA USSD Synch Proxy"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.uba.ussd.synch.proxy.queue}" connector-ref="jmsConnector"
			responseTimeout="120000" />
		<flow-ref name="USSDProxyFlow" />
		<wire-tap>
			<jms:outbound-endpoint queue="${outbound.uba.ussd.synch.proxy.queue}"
				connector-ref="jmsConnector"  exchange-pattern="one-way"/>
		</wire-tap>
		<exception-strategy ref="UBAUssdErrorHandlerAndReplier" />
	</flow>

	<sub-flow name="Request Information Before ProxyFlow">
		<choice>
			<when
				expression="#[payload.getServiceInfo().getId()==${service.interswitch.voucher.redeem.code}]">
				<flow-ref name="PopulateRedemptionServiceData"/>
			</when>
			<otherwise>
				<logger level="INFO" message="Routing to Proxy Flow" />
			</otherwise>
		</choice>
	</sub-flow>

	<flow name="External Services Proxy"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="jms/queue/externalServicesQueue"
			connector-ref="jmsConnector" exchange-pattern="one-way" />
		<flow-ref name="External Partner Services Proxy"></flow-ref>
		<exception-strategy ref="errorHandler" />
	</flow>
	<flow name="Payment to Merchant Authorization Externally"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.customer.merchant.authorization}"
			connector-ref="jmsConnector" exchange-pattern="one-way" />
		<flow-ref name="ProxyFlow" />
		<exception-strategy ref="errorHandler" />
	</flow>
	<flow name="InterSwitch Mobile Synch Proxy"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.interswitch.synch.proxy.queue}"	exchange-pattern="one-way" connector-ref="jmsConnector" responseTimeout="60000" />
		<flow-ref name="ProxyFlow" />
		<jms:outbound-endpoint queue="${oubound.interswitch.synch.proxy.queue}" connector-ref="jmsConnector" />
		<exception-strategy ref="errorHandler" />
	</flow>
	<flow name="VERiCASH Mobile Asynch Proxy"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.vericash.asynch.proxy.queue}"
			exchange-pattern="one-way" connector-ref="jmsConnector" />
		<flow-ref name="ProxyFlow" />
		<exception-strategy ref="errorHandler" />
	</flow>
	<flow name="Resume Transaction"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="jms/queue/resumeTransactionQueue"
			connector-ref="jmsConnector" />
		<flow-ref name="Resume Transaction Execution" />
		<exception-strategy ref="errorHandler" />
	</flow>
	<flow name="Settlement" processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.settlemet.queue}"
			connector-ref="jmsConnector" />
		<flow-ref name="ProxyFlow" />
	</flow>
	<flow name="CTB"  processingStrategy="AsynchronousProcessingStrategy">
		<http:inbound-endpoint host="${inbound.ctb.host}"
			port="${inbound.ctb.port}" path="services/CTB" exchange-pattern="one-way"
			transformer-refs="ctb-transformer" />
		<flow-ref name="CTB Flow" />
		<default-exception-strategy>
			<processor-chain>
				<component class="com.cit.mpayment.components.ErrorHandlerComponent"></component>
				<set-payload value="#[bean:textMessage]"></set-payload>
			</processor-chain>
		</default-exception-strategy>
	</flow>
	<flow name="Redeem Voucher From ATM" processingStrategy="AsynchronousProcessingStrategy">
		<http:inbound-endpoint host="localhost" port="9292"
			path="services/redeem" exchange-pattern="one-way"
			transformer-refs="redeem-transformer" responseTransformer-refs="authorize-transformer" />
		<flow-ref name="ProxyFlow" />
		<catch-exception-strategy>
			<transformer ref="authorize-transformer" />
		</catch-exception-strategy>
	</flow>
	<flow name="NI Customer Services" processingStrategy="AsynchronousProcessingStrategy">
		<http:inbound-endpoint host="localhost" port="9998"
			path="services/nicustomer" exchange-pattern="one-way"
			transformer-refs="JsonToNiCustomerRequest" responseTransformer-refs="objectToJson" />
		<flow-ref name="NI Customer Purchase" />
	</flow>
	<flow name="Payment to Merchant Authorization Internally"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.portal.merchant.authorization}"
			connector-ref="jmsConnector"
			transformer-refs="objectToJson  paymentToMerchantRequestTransformer"
			responseTransformer-refs="paymentToMerchantResponseTransformer objectToJson" exchange-pattern="one-way"/>
		<flow-ref name="ProxyFlow" />
		<exception-strategy ref="p2mHandler" />
	</flow>

	<flow name="CustomerInformationRequest" processingStrategy="AsynchronousProcessingStrategy">
		<http:inbound-endpoint host="localhost" port="9292"
			path="services/quickteller/customerInformation" exchange-pattern="one-way"
			transformer-refs="CustomerInfoRequestTransformer"
			responseTransformer-refs="CustomerInfoResponseTransformer" />
		<flow-ref name="ProxyFlow" />
	</flow>

	<flow name="Customer Refund Closed Account" processingStrategy="AsynchronousProcessingStrategy">
		<http:inbound-endpoint host="${inbound.customer.refund.closed.host}"
			port="${inbound.customer.refund.closed.port}" method="POST"
			path="mpaymentapp-mule-1/customerService/refundClosedAccount"
			exchange-pattern="one-way"
			transformer-refs="objectToStringTransformer  customerRequestToBusinessMessage" />
		<flow-ref name="ProxyFlow" />
		<exception-strategy ref="errorHandler" />
	</flow>
	<flow name="IVR Communicator" >
		<tcp:inbound-endpoint host="${inbound.ivr.host}"
			connector-ref="tcpConnector" port="${inbound.ivr.port}"
			transformer-refs="bytesToString ivrRequest" responseTransformer-refs="StringToBytes" />
		<flow-ref name="IVR Flow" />
		<catch-exception-strategy>
			<pooled-component>
				<method-entry-point-resolver>
					<include-entry-point method="prepareExceptionHandling" />
				</method-entry-point-resolver>
				<spring-object bean="ivrOutputComponent"></spring-object>
			</pooled-component>
		</catch-exception-strategy>
	</flow>
	<sub-flow name="CDA Communicator">
		<choice>
			<when expression="(serviceInfo/code) = '3102' " evaluator="bean">
				<enricher target="#[variable:cardQuery]">
					<http:outbound-endpoint name="CallCDA"
						host="${outbound.cda.host}" port="${outbound.cda.port}" encoding="UTF-8"
						path="CardDetailsPCI/GetAllCards?MSISDN=%252b#[bean:primarySenderInfo/msisdn]"
						responseTransformer-refs="objectToStringTransformer" />
				</enricher>
				<transformer ref="queryCustomerCards" />
				<transformer ref="fillCardDetailsTransformer" />
			</when>
			<otherwise>
				<enricher>
					<http:outbound-endpoint host="${outbound.cda.host}"
						port="${outbound.cda.port}" encoding="UTF-8"
						path="CardDetailsPCI/GetCard?MSISDN=%252b#[bean:primarySenderInfo/msisdn]&amp;Code=#[bean:primarySenderInfo/paymentMethod/garShortCode]"
						exchange-pattern="request-response" contentType="application/x-www-form-urlencoded"
						method="GET" />
					<enrich target="#[variable:cardPAN]" source="#[json:cardPAN]" />
					<enrich target="#[variable:cardExpiryDateMonth]" source="#[json:cardExpiryDateMonth]" />
					<enrich target="#[variable:cardExpiryDateYear]" source="#[json:cardExpiryDateYear]" />
				</enricher>
				<transformer ref="readCardDetails" />
			</otherwise>
		</choice>
	</sub-flow>
	<flow name="Balance Inquiry Request Service" processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="jms/queue/balanceInquiryRequestQueue"
			exchange-pattern="one-way" connector-ref="jmsConnector" />

		<message-properties-transformer>
			<delete-message-property key="MULE_REPLYTO"/>
	    </message-properties-transformer>

		<pooled-component>
			<method-entry-point-resolver>
					<include-entry-point method="prepareBalanceRequestMessage" />
			</method-entry-point-resolver>
			<spring-object bean="balanceInquiryRequestComponent" />
		</pooled-component>

		<logger level="ERROR" message="before send to bank #[message]" />

		<transformer ref="ObjectToJaxbXml1" />

		<transformer ref="objectToStringTransformer" />

		<request-reply timeout="60000">
			<jms:outbound-endpoint queue="jms/queue/balanceInquiryUBARequestQueue" connector-ref="jmsConnector"
									exchange-pattern="one-way" >
				<message-properties-transformer>
					<delete-message-property key="MULE_REPLYTO"/>
	    		</message-properties-transformer>
			</jms:outbound-endpoint>

			<jms:inbound-endpoint queue="jms/queue/balanceInquiryUBAResponseQueue" connector-ref="jmsConnector"
								 exchange-pattern="one-way" />
		</request-reply>

		<logger level="ERROR" message="after return #[message]" />

		<transformer ref="XmlToBalanceInquiryResponse" />

		<logger level="ERROR" message="final #[payload]" />

	</flow>

	<flow name="Balance Inquiry Response Service" processingStrategy="AsynchronousProcessingStrategy">

		<jms:inbound-endpoint queue="jms/queue/balanceInquiryUBARequestQueue"
			exchange-pattern="one-way" connector-ref="jmsConnector" />

		<transformer ref="XmlToBalanceInquiryRequest" />

		<pooled-component>
			<method-entry-point-resolver>
					<include-entry-point method="processBalanceInquiryResponse" />
			</method-entry-point-resolver>
			<spring-object bean="balanceInquiryRequestComponent" />
		</pooled-component>

		<logger level="ERROR" message="inside bank flow #[message]" />

		<transformer ref="ObjectToJaxbXml2" />

		<transformer ref="objectToStringTransformer" />

		<message-properties-transformer scope="outbound">
        	<add-message-property key="MULE_CORRELATION_ID" value="#[header:INBOUND:MULE_CORRELATION_ID]" />
    	</message-properties-transformer>

		<jms:outbound-endpoint queue="jms/queue/balanceInquiryUBAResponseQueue" exchange-pattern="one-way"
								connector-ref="jmsConnector" />
	</flow>
<flow name="Validate UBA Customer Pin"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.customer.validate.pin.queue}" exchange-pattern="one-way" connector-ref="jmsConnector" />
	    	<logger level="INFO" message="#[payload]" />
	    	<vm:outbound-endpoint path="log/service" exchange-pattern="one-way">
				<message-properties-transformer scope="session">
					<add-message-property key="logTarget" value="SERVICE" />
					<add-message-property key="logStatus" value="Initiated" />
				</message-properties-transformer>
			</vm:outbound-endpoint>
		<flow-ref name="10000551" />
		<flow-ref name="save-service-log" />
		<jms:outbound-endpoint queue="${outbound.customer.validate.pin.queue}"
			connector-ref="jmsConnector" />
		<exception-strategy ref="defaultErrorHandler" />
	</flow>

 	 <flow name="UBA USSD Asynch Flow"  processingStrategy="AsynchronousProcessingStrategy">
                  <jms:inbound-endpoint queue="${inbound.uba.ussd.asynch.queue}" connector-ref="jmsConnector"
                              responseTimeout="120000" exchange-pattern="one-way"/>
    			<set-session-variable variableName="msisdn" value="#[payload.getPrimarySenderInfo().getMsisdn()]"/>
				 <flow-ref name="USSDAsynchFlow" />
                  <exception-strategy ref="UBAUssdAsynchErrorHandlerAndReplier" />
      </flow>


	<flow name="Remote Registration Customer"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="${inbound.remote.registration.customer.queue}"
			exchange-pattern="one-way" connector-ref="jmsConnector" />
	    	<logger level="INFO" message="#[payload]" />
	    	<set-variable value="#[correlationId]" variableName="correlationId"  />

			<vm:outbound-endpoint path="log/service" exchange-pattern="request-response">
				<message-properties-transformer scope="session">
					<add-message-property key="logTarget" value="SERVICE" />
					<add-message-property key="logStatus" value="Initiated" />
				</message-properties-transformer>
			</vm:outbound-endpoint>
		<vm:outbound-endpoint path="services/#[payload.getServiceInfo().getId()]" exchange-pattern="request-response"/>
		<processor ref="statusCodeFilter" />
		<choice>
				<when expression="payload.getServiceInfo().getCode()=='13001'" evaluator="groovy">
					<expression-component>#[payload.getServiceInfo().setId('13301')]</expression-component>
					<expression-component>#[payload.getServiceInfo().setCode('13301')]</expression-component>
				</when>
				<otherwise>
					<logger message="############ No switching ######" level="INFO" />
				</otherwise>
		</choice>
		
		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="getBusinessServiceConfigIdByTypeID" />
			</method-entry-point-resolver>
			<spring-object bean="walletManagerComponent"/>
		</pooled-component>
				
		<flow-ref name="Run Service Steps" />
		<flow-ref name="save-service-log" />
		<jms:outbound-endpoint queue="${outbound.remote.registration.customer.queue}"
			connector-ref="jmsConnector" />
		<catch-exception-strategy>
			<component>
				<spring-object bean="errorComponent" />
			</component>
			
			<flow-ref name="failed-service-log" />
			
			<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
			<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
			<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		 	<scripting:component doc:name="Add correlationId">
		    <scripting:script engine="Groovy"><![CDATA[
				message.correlationId = flowVars.correlationId;
				return message]]>
				</scripting:script>
        	</scripting:component>

			<jms:outbound-endpoint queue="${outbound.remote.registration.customer.queue}" connector-ref="jmsConnector"/>
		</catch-exception-strategy>
	</flow>

	<sub-flow name="USSDAsynchFlow">
		<description>This is the asynch flow for all services. Any service
			request shall pass to this proxy
		</description>

		<pooled-component>
			<spring-object bean="counterComponent"></spring-object>
		</pooled-component>
		<vm:outbound-endpoint path="log/service"
			exchange-pattern="request-response">
			<message-properties-transformer scope="session">
				<add-message-property key="logTarget" value="SERVICE" />
				<add-message-property key="logStatus" value="Initiated" />
			</message-properties-transformer>
		</vm:outbound-endpoint>
		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="saveTransactionHistoryProfile" />
			</method-entry-point-resolver>
			<spring-object bean="customerProfileComponent"></spring-object>
		</pooled-component>
		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="retrieveAndCheckService" />
			</method-entry-point-resolver>
			<spring-object bean="walletManagerComponent"></spring-object>
		</pooled-component>

		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="prepareReceiverData" />
			</method-entry-point-resolver>
			<spring-object bean="walletManagerComponent"/>
		</pooled-component>
		<flow-ref name="Fill Users Data" />
		<flow-ref name="Run Service Steps" />
		<set-payload value="#[variable:newMessage]"></set-payload>

		<vm:outbound-endpoint path="log/service"
			exchange-pattern="request-response">
			<message-properties-transformer scope="session">
				<add-message-property key="logTarget" value="SERVICE" />
				<add-message-property key="logStatus" value="Succeeded" />
			</message-properties-transformer>
		</vm:outbound-endpoint>
		<flow-ref name="save-service-log" />
		<choice>
		<when expression="payload.getStatus().getStatusCode()!=null" evaluator="groovy">
		<expression-component>#[payload.getStatus().setErrorFlag(true)]</expression-component>
		</when>
		<otherwise><logger message="There is no failure code" level="INFO" /></otherwise>
		</choice>
	</sub-flow>

		<sub-flow name="Validate Receiver Sub Flow">
		<description>This is the main Flow For Validate Reciver</description>
		<logger level="INFO" message="INSIDE VALIDATE RECEIVER CODITION MULE-CONFIG MULE" />
		
		<vm:outbound-endpoint path="log/service"
			exchange-pattern="request-response">
			<message-properties-transformer scope="session">
				<add-message-property key="logTarget" value="SERVICE" />
				<add-message-property key="logStatus" value="Initiated" />
			</message-properties-transformer>
		</vm:outbound-endpoint>
		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="checkSecurity" />
			</method-entry-point-resolver>
			<spring-object bean="securityManagerComponent"></spring-object>
		</pooled-component>
		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="saveTransactionHistoryProfile" />
			</method-entry-point-resolver>
			<spring-object bean="customerProfileComponent"></spring-object>
		</pooled-component>
		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="retrieveAndCheckService" />
			</method-entry-point-resolver>
			<spring-object bean="walletManagerComponent"></spring-object>
		</pooled-component>

		<pooled-component>
			<method-entry-point-resolver>
				<include-entry-point method="prepareReceiverData" />
			</method-entry-point-resolver>
			<spring-object bean="walletManagerComponent"/>
		</pooled-component>
		<flow-ref name="Fill Users Data" />
		<flow-ref name="Run Service Steps" />
		<set-payload value="#[variable:newMessage]"></set-payload>

		<vm:outbound-endpoint path="log/service"
			exchange-pattern="request-response">
			<message-properties-transformer scope="session">
				<add-message-property key="logTarget" value="SERVICE" />
				<add-message-property key="logStatus" value="Succeeded" />
			</message-properties-transformer>
		</vm:outbound-endpoint>
		<flow-ref name="save-service-log" />
		<choice>
		<when expression="payload.getStatus().getStatusCode()!=null" evaluator="groovy">
		<expression-component>#[payload.getStatus().setErrorFlag(true)]</expression-component>
		</when>
		<otherwise><logger message="There is no failure code" level="INFO" /></otherwise>
		</choice>
	</sub-flow>
	<flow name="NIBSS Remote Registration Customer"  processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="jms/queue/nibssremoteRegistrationCustomerQueue"
			exchange-pattern="one-way" connector-ref="jmsConnector" />
	    	<logger level="INFO" message="Nibss Remote Rigistration #[payload]" />
	    	<vm:outbound-endpoint path="log/service" exchange-pattern="request-response">
				<message-properties-transformer scope="session">
					<add-message-property key="logTarget" value="SERVICE" />
					<add-message-property key="logStatus" value="Initiated" />
				</message-properties-transformer>
			</vm:outbound-endpoint>
		<vm:outbound-endpoint path="services/#[payload.getServiceInfo().getId()]" exchange-pattern="request-response"/>

		<flow-ref name="save-service-log" />
		<jms:outbound-endpoint queue="jms/queue/nibssremoteRegistrationCustomerOutputQueue"
			connector-ref="jmsConnector" />
		<catch-exception-strategy>
			<component>
				<spring-object bean="errorComponent" />
			</component>
			
			<flow-ref name="failed-service-log" />
			
			<logger level="INFO" message="Set Property "/>
			<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
			<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
			<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		 	<scripting:component doc:name="Add correlationId">
		    <scripting:script engine="Groovy"><![CDATA[
				message.correlationId = flowVars.correlationId;
				return message]]>
				</scripting:script>
        	</scripting:component>

			<jms:outbound-endpoint queue="jms/queue/nibssremoteRegistrationCustomerOutputQueue" connector-ref="jmsConnector"/>
		</catch-exception-strategy>
	</flow>
						
	<flow name="Main-Proxy-Flow" processingStrategy="AsynchronousProcessingStrategy">
		<jms:inbound-endpoint queue="jms/uba/service/request/synch/input" connector-ref="jmsConnector" exchange-pattern="one-way"/>
		<set-variable value="#[correlationId]" variableName="correlationId"  />
		<set-variable variableName="responseQueue" value="jms/uba/service/response/synch/output"/>
			
			<flow-ref name="The Main flow for Transactions" />
		
		<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
		<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
		<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		<jms:outbound-endpoint queue="#[flowVars.responseQueue]" connector-ref="jmsConnector" exchange-pattern="one-way"/>
		
		<exception-strategy ref="muleChannelsExceptionStrategy"/>
	</flow>
	
	<flow name="Main-Proxy-Flow-Vericash-API" processingStrategy="AsynchronousProcessingStrategy">
	
		<jms:inbound-endpoint queue="jms/uba/service/api/request/synch/input" connector-ref="jmsConnector" exchange-pattern="one-way"/>
		<set-variable value="#[correlationId]" variableName="correlationId"  />
		<set-variable variableName="responseQueue" value="jms/uba/service/api/response/synch/output"/>
		
		<flow-ref name="The Main flow for Transactions" />
		
		<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
		<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
		<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		
		<flow-ref name="Response for portalrevamp batchActions" />
		<logger level="INFO" message="################ log payload before outbound  #[payload]" />
		
		<jms:outbound-endpoint queue="#[flowVars.responseQueue]" connector-ref="jmsConnector" exchange-pattern="one-way"/>
		
		<exception-strategy ref="muleChannelsExceptionStrategy"/>
		
	</flow>
	
	<sub-flow name="Response for portalrevamp batchActions">
          	<set-property propertyName="TRANSACTION_ID" value="#[payload.getTransactionInfo().getTransactionId()]" />
            <set-property propertyName="ERROR_FLAG" value="#[payload.getStatus().getErrorFlag()]" />
            <set-property propertyName="ERROR_MESSAGE" value="#[payload.getStatus().getStatusMsg()]" />
            <set-property propertyName="ERROR_STATUS_CODE" value="#[payload.getStatus().getStatusCode()]" />
       </sub-flow>
	
	
	
	<sub-flow name="DynamicProxySubFlow">
	  <flow-ref name="DynamicProxyFlow" />
	</sub-flow>
	
	<flow name="DynamicProxyFlow" processingStrategy="AsynchronousProcessingStrategy">
		<set-variable value="#[correlationId]" variableName="correlationId"  />
		<set-variable variableName="responseQueue" value="jms/uba/service/response"/>
			
			<flow-ref name="The Main flow for Transactions" />
		
		<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
		<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
		<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		<jms:outbound-endpoint queue="#[flowVars.responseQueue]" connector-ref="jmsConnector" exchange-pattern="one-way"/>
		
		<exception-strategy ref="muleChannelsExceptionStrategy"/>
	</flow>

	<catch-exception-strategy name="muleChannelsExceptionStrategy">
			<logger level="ERROR" message="######### Mule Channels ErrorHandler ##########"></logger>
			<component>
				<spring-object bean="errorComponent" />
			</component>
			
			<flow-ref name="failed-service-log" />
		 	
		 	<set-property propertyName="MULE_CORRELATION_ID" value="#[variable:correlationId]" />
			<set-property propertyName="MULE_CORRELATION_GROUP_SIZE" value="-1" />
			<set-property propertyName="MULE_CORRELATION_SEQUENCE" value="-1" />
		 	<scripting:component doc:name="Add correlationId">
		    <scripting:script engine="Groovy"><![CDATA[
				message.correlationId = flowVars.correlationId;
				return message]]>
				</scripting:script>
        	</scripting:component>
			<flow-ref name="Reverse Handler" />
			<jms:outbound-endpoint queue="#[flowVars.responseQueue]" connector-ref="jmsConnector" exchange-pattern="one-way"/>
		</catch-exception-strategy>
		
		<sub-flow name="save-service-log">
					<pooled-component>
							<method-entry-point-resolver>
								<include-entry-point method="saveServiceLog" />
							</method-entry-point-resolver>
							<spring-object bean="serviceLogComponent"></spring-object>
					</pooled-component>
		</sub-flow>
		
		<sub-flow name="failed-service-log">
					<pooled-component>
							<method-entry-point-resolver>
								<include-entry-point method="failedServiceLog" />
							</method-entry-point-resolver>
							<spring-object bean="serviceLogComponent"></spring-object>
					</pooled-component>
		</sub-flow>
		
		<sub-flow name="enroll-risk-limit">
 			    <wire-tap>
 					<pooled-component>
 						<method-entry-point-resolver>
 							<include-entry-point method="setExpiryDateForRiskLimit" />
 						</method-entry-point-resolver>
 						<spring-object bean="customerManagerComponent" />
 					</pooled-component>
 				</wire-tap>
		</sub-flow>
</mule>
