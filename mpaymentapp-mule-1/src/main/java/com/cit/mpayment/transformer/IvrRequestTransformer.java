package com.cit.mpayment.transformer;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import org.mule.api.transformer.TransformerException;
import org.mule.transformer.AbstractTransformer;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class IvrRequestTransformer extends AbstractTransformer{
	private enum IvrRequest {CI,BI,MS,P2P,RV,LI};
	
	protected Object doTransform(Object src, String enc)
			throws TransformerException {
		BusinessMessage message = null;
		String source = (String) src;
		IvrRequest ivrMessage = IvrRequest.valueOf(source.substring(0, 2));
		Map<String,String> parameters = buildParametersMap(source,ivrMessage);
		switch(ivrMessage) {
		case CI:
			message = buildCashInRequest(parameters);
			break;
		case BI:
			message = buildBalanceInquiryRequest(parameters);
			break;
		case MS:
			message = buildMiniStatementRequest(parameters);
			break;
		case P2P:
			message = buildP2PRequest(parameters);
			break;
		case RV:
			message = buildVoucherRequest(parameters);
			break;
		case LI:
			message = buildCheckPinRequest(parameters);
			break;
		}
		return message;
	}

	private BusinessMessage buildCashInRequest(Map<String,String> parameters){
		BusinessMessage message = new BusinessMessage();
		message.getServiceInfo().setCode("1400");
		message.getPrimarySenderInfo().setMsisdn(parameters.get("sender.msisdn"));
		message.getPrimaryReceiverInfo().setMsisdn(parameters.get("receiver.msisdn"));
		message.getTransactionInfo().setTransactionAmount(new BigDecimal(parameters.get("amount")));
		return message;
	}
	private BusinessMessage buildBalanceInquiryRequest(Map<String,String> parameters){
		BusinessMessage message = new BusinessMessage();
		message.getServiceInfo().setCode("10");
		message.getPrimarySenderInfo().setMsisdn(parameters.get("sender.msisdn"));
		message.getPrimarySenderInfo().setPin(parameters.get("pin"));
		return message;
	}
	private BusinessMessage buildMiniStatementRequest(Map<String,String> parameters){
		BusinessMessage message = new BusinessMessage();
		message.getServiceInfo().setCode("17");
		message.getPrimarySenderInfo().setMsisdn(parameters.get("sender.msisdn"));
		message.getPrimarySenderInfo().setPin(parameters.get("pin"));
		return message;
	}
	private BusinessMessage buildP2PRequest(Map<String,String> parameters){
		BusinessMessage message = new BusinessMessage();
		message.getServiceInfo().setCode("11");
		message.getPrimarySenderInfo().setMsisdn(parameters.get("sender.msisdn"));
		message.getPrimarySenderInfo().setPin(parameters.get("pin"));
		message.getPrimaryReceiverInfo().setMsisdn(parameters.get("receiver.msisdn"));
		message.getTransactionInfo().setTransactionAmount(new BigDecimal(parameters.get("amount")));
		return message;
	}
	private BusinessMessage buildVoucherRequest(Map<String,String> parameters){
		BusinessMessage message = new BusinessMessage();
		message.getServiceInfo().setCode("19");
		message.getPrimarySenderInfo().setMsisdn(parameters.get("sender.msisdn"));
		message.getPrimarySenderInfo().setPin(parameters.get("pin"));
		message.getPrimaryReceiverInfo().setMsisdn(parameters.get("receiver.msisdn"));
		message.getTransactionInfo().setTransactionAmount(new BigDecimal(parameters.get("amount")));
		return message;
	}
	private BusinessMessage buildCheckPinRequest(Map<String,String> parameters){
		BusinessMessage message = new BusinessMessage();
		message.getServiceInfo().setCode("00");
		message.getPrimarySenderInfo().setMsisdn(parameters.get("sender.msisdn"));
		message.getPrimarySenderInfo().setPin(parameters.get("pin"));
		return message;
	}
	
	private Map<String,String> buildParametersMap(String source,IvrRequest requestType){
		Map<String,String> parameters = new HashMap<String,String>();
		String[] sourceArray = source.split(" ");
		parameters.put("message.type", sourceArray[0]);
		switch(requestType) {
		case CI:
			parameters.put("sender.msisdn", sourceArray[1]);
			parameters.put("pin", sourceArray[2]);
			parameters.put("receiver.msisdn", sourceArray[3]);
			parameters.put("amount", sourceArray[4]);
			break;
		case BI:
			parameters.put("sender.msisdn", sourceArray[1]);
			parameters.put("pin", sourceArray[2]);
			break;
		case MS:
			parameters.put("sender.msisdn", sourceArray[1]);
			parameters.put("pin", sourceArray[2]);
			break;
		case P2P:
			parameters.put("sender.msisdn", sourceArray[1]);
			parameters.put("amount", sourceArray[2]);
			parameters.put("receiver.msisdn", sourceArray[3]);
			parameters.put("pin", sourceArray[4]);			
			break;
		case RV:
			parameters.put("sender.msisdn", sourceArray[1]);
			parameters.put("amount", sourceArray[2]);
			parameters.put("receiver.msisdn", sourceArray[3]);
			parameters.put("pin", sourceArray[4]);
			break;
		case LI:
			parameters.put("sender.msisdn", sourceArray[1]);
			parameters.put("pin", sourceArray[2]);
			break;
		}
		return parameters;
	}
}
