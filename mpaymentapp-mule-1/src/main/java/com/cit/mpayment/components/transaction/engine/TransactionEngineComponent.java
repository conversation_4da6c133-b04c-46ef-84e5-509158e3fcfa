package com.cit.mpayment.components.transaction.engine;

import java.util.Date;

import org.apache.log4j.Logger;

import com.cit.mpaymentapp.common.counters.UserCounterManagerRemote;
import com.cit.mpaymentapp.common.facade.TransactionEngineManagerRemote;
import com.cit.mpaymentapp.common.facade.TransactionEngineRemote;
import com.cit.mpaymentapp.common.fees.CalculateFeeServiceRemote;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.risk.RiskCountersServiceRemote;
import com.cit.mpaymentapp.model.Enums.AmountType;
import com.cit.mpaymentapp.model.transaction.PaymentOrder;
import com.cit.mpaymentapp.model.transaction.PaymentOrderContainer;
import com.cit.mpaymentapp.mongo.counters.CountersManager;
import com.cit.shared.error.exception.CommissionException;
import com.cit.shared.error.exception.FeesException;
import com.cit.shared.error.exception.RiskException;
import com.cit.shared.error.exception.TransactionException;

public class TransactionEngineComponent {
	
	private final static Logger serviceLog = Logger.getLogger("serviceLog");
	
	private TransactionEngineRemote transactionEngine;
	
	private TransactionEngineManagerRemote transEngineManagerRef;
	
	private CalculateFeeServiceRemote calculateFee;
	
	private RiskCountersServiceRemote riskCounterService;
	
	private UserCounterManagerRemote counterManagerRef;
	

	public BusinessMessage executeTransaction(BusinessMessage message) throws RiskException, FeesException, TransactionException, CommissionException {
		Long transId = null ;
		if (message.getTransactionInfo() != null ){
			transId = message.getTransactionInfo().getTransactionId();
		}
		serviceLog.getRootLogger().info("########## Start executeTransaction: "+ transId +" : " +new Date()+"##########");
		PaymentOrderContainer paymentOrderContainer = transactionEngine.resolveTransactionSteps(message);
		message.getSoftFields().put("PMT_ORDR_CONTNR", paymentOrderContainer);
		
		message = transEngineManagerRef.adjustPmtContainer(message);
		message = riskCounterService.riskCountersCheck(message);
		message = calculateFee.calculateFee(message);
		message = transEngineManagerRef.calculateSimpleCommission(message);
		message = transEngineManagerRef.runTransactionSteps(message);
		message = saveCounters(message);
		message = transEngineManagerRef.saveCrossWalletTransactionsInfo(message);
		serviceLog.getRootLogger().info("########## End executeTransaction: "+ transId +" : " +new Date()+"##########");
		return message;
	}
	
	public BusinessMessage adjustPaymentContainer(BusinessMessage message) {
		
		Long transId = message.getTransactionInfo().getTransactionId();
		PaymentOrderContainer paymentOrderContainer = (PaymentOrderContainer)message.getSoftFields().get("PMT_ORDR_CONTNR");
		transactionEngine.adjustPaymentContainer(paymentOrderContainer, transId);
		return message;
	}
	
	public BusinessMessage saveCrossWalletTransactionsInfo(BusinessMessage message) {
		
		transactionEngine.saveCrossWalletTransactionsInfo(message);
		return message;
	}
	
	
	public BusinessMessage saveCounters(BusinessMessage message ) {
		PaymentOrderContainer paymentOrderContainer = (PaymentOrderContainer)message.getSoftFields().get("PMT_ORDR_CONTNR");
		serviceLog.trace(message + "," + paymentOrderContainer);
		CountersManager countersManager = new CountersManager();
		Long transId = null ;
		if (message.getTransactionInfo() != null ){
			transId = message.getTransactionInfo().getTransactionId();
		}
		serviceLog.getRootLogger().info("########## Start Loop saveCounters: "+ transId +" : " +new Date()+"##########");
		for (PaymentOrder paymentOrder : paymentOrderContainer.getPaymentOrders()) {
			try {
				if (paymentOrder.getAmountType() == AmountType.TRANSACTION) {
					
					getCounterManagerRef().saveCounters(paymentOrder.getGeneralAccountRef(), (long) paymentOrder.getOwnerType(), message.getServiceInfo().getCode(), paymentOrder.getActionType(),
							paymentOrder.getAmount().doubleValue(),message.getPrimarySenderInfo().getPaymentMethodType());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		serviceLog.getRootLogger().info("########## End Loop saveCounters: "+ transId +" : " +new Date()+"##########");
		return message;
	}
	
	
	public CalculateFeeServiceRemote getCalculateFee() {
		return calculateFee;
	}

	public void setCalculateFee(CalculateFeeServiceRemote calculateFee) {
		this.calculateFee = calculateFee;
	}
	
	public TransactionEngineRemote getTransactionEngine() {
		return transactionEngine;
	}

	public void setTransactionEngine(TransactionEngineRemote transactionEngine) {
		this.transactionEngine = transactionEngine;
	}

	public RiskCountersServiceRemote getRiskCounterService() {
		return riskCounterService;
	}

	public void setRiskCounterService(RiskCountersServiceRemote riskCounterService) {
		this.riskCounterService = riskCounterService;
	}

	public TransactionEngineManagerRemote getTransEngineManagerRef() {
		return transEngineManagerRef;
	}

	public void setTransEngineManagerRef(
			TransactionEngineManagerRemote transEngineManagerRef) {
		this.transEngineManagerRef = transEngineManagerRef;
	}

	public UserCounterManagerRemote getCounterManagerRef() {
		return counterManagerRef;
	}

	public void setCounterManagerRef(UserCounterManagerRemote counterManagerRef) {
		this.counterManagerRef = counterManagerRef;
	}

}
