package com.cit.mpayment.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;

public class TransactionManagerValidatorComponent {
	 
	private static final String AUTH_INSUFF_FUND_Error = "51";
	public BusinessMessage validateBalanceCoverTransAmount(BusinessMessage message) {
			
			StatusType status = new StatusType();
			
			try{
				
				if(message!=null && message.getPrimarySenderInfo()!=null && message.getPrimarySenderInfo().getBalance()!=null
						&& message.getTransactionInfo()!=null && message.getTransactionInfo().getTransactionAmount()!=null){
					
					if(message.getPrimarySenderInfo().getBalance().doubleValue() < message.getTransactionInfo().getTransactionAmount().doubleValue()){
						status.setErrorFlag(true);
					    status.setStatusCode(AUTH_INSUFF_FUND_Error);
					    message.setStatus(status);
					}
					
				}else{
					status.setErrorFlag(true);
				    status.setStatusCode(AUTH_INSUFF_FUND_Error);
				    message.setStatus(status);
				}
				
			}catch(Exception e){
				status.setErrorFlag(true);
			    status.setStatusCode(AUTH_INSUFF_FUND_Error);
			    message.setStatus(status);
			}
	        return message;
	    }
}
