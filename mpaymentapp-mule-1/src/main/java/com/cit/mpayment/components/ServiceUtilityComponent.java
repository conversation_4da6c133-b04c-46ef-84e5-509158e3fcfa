package com.cit.mpayment.components;

import java.util.HashMap;
import java.util.Map;

import org.mule.api.MuleEventContext;
import org.mule.api.lifecycle.Callable;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.service.utilities.ServiceUtililyEnums.ServiceUtilityType;
import com.cit.mpaymentapp.common.service.utilities.ServiceUtilityExecuterRemote;
import com.cit.mpaymentapp.common.service.utilities.ServiceUtilityRequest;
import com.cit.mpaymentapp.model.service.utilities.ServiceUtility;
import com.cit.shared.error.exception.GeneralFailureException;

public class ServiceUtilityComponent implements Callable {
	private final String DisableBusinessServiceErrorCode = "SYS0050";
	private ServiceUtilityExecuterRemote serviceUtilityExecuter;
	public static Map<String,ServiceUtility> serviceUtilitiesMap = new HashMap<String, ServiceUtility>();

	@Override
	public Object onCall(MuleEventContext eventContext) throws Exception {
		ServiceUtilityRequest request = (ServiceUtilityRequest) eventContext.getMessage().getPayload();

		return serviceUtilityExecuter.executeRequest(request);
	}

	public void checkServiceAvailability(BusinessMessage message) throws GeneralFailureException {
	
			if (isUtilityEnabled(ServiceUtilityType.DBS.name(),
					message.getServiceInfo().getCode(), message.getWalletInfo().getCountryIso2())) {
				throw new GeneralFailureException(DisableBusinessServiceErrorCode);
			}

	}
	
	public Boolean isLoggingRequestResponseEnabled(BusinessMessage message) {
		return !isUtilityEnabled(ServiceUtilityType.SLRR.name(),
				message.getServiceInfo().getCode(), message.getWalletInfo().getCountryIso2());
	}
	
	public ServiceUtilityExecuterRemote getServiceUtilityExecuter() {
		return serviceUtilityExecuter;
	}

	public void setServiceUtilityExecuter(ServiceUtilityExecuterRemote serviceUtilityExecuter) {
		this.serviceUtilityExecuter = serviceUtilityExecuter;
	}

	public boolean isUtilityEnabled(String utilityId, String serviceId, String countryIsoCode2) {

		try {
			if(serviceUtilitiesMap.isEmpty()){
				refreshCashe();
			}
			if(utilityId !=null && serviceId!=null && countryIsoCode2!=null) {
				ServiceUtility serviceUtility = serviceUtilitiesMap.get(getKey(serviceId,countryIsoCode2,utilityId));
				if(serviceUtility != null){
					return serviceUtility.isEnabled();
				}
			}else {
				return false;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	private String getKey( String serviceId, String countryIsoCode2, String utilityId){
		return serviceId.concat("-").concat(countryIsoCode2)
		.concat("-").concat(utilityId);
	}
	
	public void refreshCashe() {
		synchronized (serviceUtilitiesMap) {
			try {
				serviceUtilitiesMap = serviceUtilityExecuter.getUtilityMap();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		
	}
}
