<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">
    <changeSet id="childServicesForRejectBlockApi" author="AbdullahKhames">
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="*********"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Reject or Block Money Request(reject)"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="CATEGORY_ID" valueNumeric="NULL"/>
            <column name="ORGANIZATION_ID" valueNumeric="NULL"/>
            <column name="SERVICE_TYPE" valueNumeric="NULL"/>
            <column name="SERVICE_CODE" value="*********"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY_ID" valueNumeric="NULL"/>
            <column name="HOME_SCREEN_CONFIG" valueNumeric="NULL"/>
            <column name="ENABLED_PROFILES" valueNumeric="NULL"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="*********"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
        <insert tableName="SERVICE_CONFIG_MAP">
            <column name="SERVICE_TYPE_ID" valueNumeric="*********"/>
            <column name="IS_GENERIC_SERVICE" valueNumeric="0"/>
            <column name="SERVICE_TYPE_NAME" value="Reject or Block Money Request(block and reject)"/>
            <column name="SERVICE_MODE" valueNumeric="0"/>
            <column name="CATEGORY_ID" valueNumeric="NULL"/>
            <column name="ORGANIZATION_ID" valueNumeric="NULL"/>
            <column name="SERVICE_TYPE" valueNumeric="NULL"/>
            <column name="SERVICE_CODE" value="*********"/>
            <column name="IS_INDEMNITY" valueNumeric="1"/>
            <column name="SEPARATE_QUEUE" valueNumeric="0"/>
            <column name="SEPARATE_FRAMEWORK_QUEUE" valueNumeric="0"/>
            <column name="HAS_WORKFLOW" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY" valueNumeric="0"/>
            <column name="SERVICE_CATEGORY_ID" valueNumeric="NULL"/>
            <column name="HOME_SCREEN_CONFIG" valueNumeric="NULL"/>
            <column name="ENABLED_PROFILES" valueNumeric="NULL"/>
            <column name="PARENT_SERVICE_CODE" valueNumeric="*********"/>
            <column name="ROLE_BASED" valueNumeric="0"/>
        </insert>
    </changeSet>
    <changeSet id="childServicesMappingForRejectBlockApiForReject" author="AbdullahKhames">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" valueComputed="(select max(ID) +1 from BUSINESS_SERVICE_CONFIG)"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="Reject or Block Money Request(reject)"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" valueNumeric="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" valueNumeric="NULL"/>
            <column name="NARRATION" valueNumeric="NULL"/>
        </insert>
    </changeSet>
    <changeSet id="childServicesMappingForRejectBlockApiForBlockAndReject" author="AbdullahKhames">
        <insert tableName="BUSINESS_SERVICE_CONFIG">
            <column name="ID" valueComputed="(select max(ID) +1 from BUSINESS_SERVICE_CONFIG)"/>
            <column name="BANKING_AGENT" valueNumeric="0"/>
            <column name="BUSINESSSERVICECATEGORY" valueNumeric="0"/>
            <column name="ISDEFAULTSERVICE" valueNumeric="1"/>
            <column name="IS_EXPOSABLE" valueNumeric="0"/>
            <column name="IS_MULTI_WALLET" valueNumeric="0"/>
            <column name="NAME" value="Reject or Block Money Request(block and reject)"/>
            <column name="BUSINESS_SERVICE_TYPE" valueNumeric="*********"/>
            <column name="SEGMENTATIONTYPE_ID" valueNumeric="3"/>
            <column name="ORGANIZATION_ID" valueNumeric="2421"/>
            <column name="VERSION" valueNumeric="NULL"/>
            <column name="IS_ENABLED" valueNumeric="1"/>
            <column name="CATEGORY" valueNumeric="NULL"/>
            <column name="NARRATION" valueNumeric="NULL"/>
        </insert>
    </changeSet>

</databaseChangeLog>