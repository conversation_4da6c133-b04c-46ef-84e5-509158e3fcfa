<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns="http://www.mulesoft.org/schema/mule/core"
	  xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  xmlns:jms="http://www.mulesoft.org/schema/mule/jms"
	  xmlns:java="http://www.mulesoft.org/schema/mule/java"
	  xmlns:vm="http://www.mulesoft.org/schema/mule/vm"
	  xmlns:script="http://www.mulesoft.org/schema/mule/scripting"
	  xmlns:validation="http://www.mulesoft.org/schema/mule/validation"
	  xmlns:http="http://www.mulesoft.org/schema/mule/http"
	  xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
						http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
						http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
						http://www.mulesoft.org/schema/mule/vm http://www.mulesoft.org/schema/mule/vm/current/mule-vm.xsd
						http://www.mulesoft.org/schema/mule/validation http://www.mulesoft.org/schema/mule/validation/current/mule-validation.xsd
						http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
						http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">

	<jms:config name="JMS_Config" >
		<jms:active-mq-connection>
			<reconnection>
				<reconnect-forever frequency="1000" />
			</reconnection>
			<jms:factory-configuration  brokerUrl="${ACTIVEMQ_BROKER_URL}" />
		</jms:active-mq-connection>
	</jms:config>

	<vm:config name="vm.connector">
		<vm:connection />
		<vm:queues>
			<vm:queue queueName="services/${serivce.customer.cutomerprofile.transaction.history.delete}" queueType="TRANSIENT" />
			<vm:queue queueName="services/${serivce.customer.cutomerprofile.transaction.history.retrieve}" queueType="TRANSIENT" />
			<vm:queue queueName="services/${service.customer.voucher.create.p2p}" queueType="TRANSIENT" />
			<vm:queue queueName="services/100005045" queueType="TRANSIENT" />
			<vm:queue queueName="services/auto/registration/service" queueType="TRANSIENT" />
			<vm:queue queueName="calculate fees external step" queueType="TRANSIENT" />
		</vm:queues>
	</vm:config>

	<flow name="Main-Proxy-Flow">

		<jms:listener config-ref="JMS_Config" destination="jms/uba/service/request/synch/input" />
		<set-variable variableName="responseQueue" value="jms/uba/service/response/synch/output" />
		<flow-ref name="The Main flow for Transactions" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="muleChannelsExceptionStrategy" />
	</flow>

	<flow name="Main-Proxy-Flow-workflow">
		<jms:listener config-ref="JMS_Config" destination="jms/uba/service/request/synch/input/workflow"/>

		<set-variable variableName="responseQueue" value="jms/uba/service/response/synch/output/workflow"/>

		<flow-ref name="The Main flow for Transactions" />


		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="muleChannelsExceptionStrategy" />
	</flow>

	<flow name="UBA-To_Other_Flow">

		<jms:listener config-ref="JMS_Config" destination="jms/uba/service/request/4272" />

		<set-variable variableName="responseQueue" value="jms/uba/service/response" />

		<flow-ref name="The Main flow for Transactions" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="muleChannelsExceptionStrategy" />
	</flow>

	<flow name="UBA-To_Other_Auto_Validation_Flow">

		<jms:listener config-ref="JMS_Config" destination="jms/uba/service/request/42720" />

		<set-variable variableName="responseQueue" value="jms/uba/service/response" />

		<flow-ref name="The Main flow for Transactions" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="muleChannelsExceptionStrategy" />
	</flow>

	<error-handler name="muleChannelsExceptionStrategy">
		<on-error ref="filterHandler" />
		<on-error ref="timeoutHandler" />
		<on-error-continue>
			<logger level="ERROR" message="######### Mule Channels ErrorHandler ##########"></logger>
			<logger level="ERROR" message="######### Mule Channels message ########## #[message]"></logger>
			<logger level="ERROR" message="######### Mule Channels payload ########## #[payload]"></logger>
			<logger level="ERROR" message="######### Mule Channels errorType ########## #[error.errorType]"></logger>
			<logger level="ERROR" message="######### Mule Channels cause ########## #[error.cause]"></logger>
			<flow-ref name="Handle_General_Exceptions"/>

			<flow-ref name="failed-service-log" />
			<!--			<flow-ref name="Reverse Handler"/>-->
			<flow-ref name="Remove Payment Order Container and Transaction Service Steps from BM"/>
			<logger message="Payload type (via class loader): #[payload.^class]" level="INFO" />

			<logger level="INFO" message="before convertToJson payload #[payload]" />

			<java:invoke
					class="com.cit.mpaymentapp.interceptors.BusinessMsgTransformer"
					method="convertToJson(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="businessMsgTransformer">
				<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
			</java:invoke>

			<logger level="INFO" message="after convertToJson payload #[payload]" />

			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
				<jms:message correlationId="#[correlationId]">
					<jms:body>#[payload]</jms:body>
					<jms:properties>
						#[{
						MULE_CORRELATION_ID: vars.correlationId,
						MULE_CORRELATION_GROUP_SIZE: '-1',
						MULE_CORRELATION_SEQUENCE:'-1'
						}]</jms:properties>
				</jms:message>
			</jms:publish>
		</on-error-continue>

	</error-handler>


	<error-handler name="recurringChannelsExceptionStrategy">
		<on-error ref="filterHandler" />
		<on-error ref="timeoutHandler" />
		<on-error-continue>
			<logger level="ERROR" message="######### Mule Channels ErrorHandler ##########"></logger>
			<logger level="ERROR" message="######### Mule Channels message ########## #[message]"></logger>
			<logger level="ERROR" message="######### Mule Channels payload ########## #[payload]"></logger>
			<logger level="ERROR" message="######### Mule Channels errorType ########## #[error.errorType]"></logger>
			<logger level="ERROR" message="######### Mule Channels cause ########## #[error.cause]"></logger>
			<flow-ref name="Handle_General_Exceptions"/>

			<flow-ref name="failed-service-log" />
			<!--			<flow-ref name="Reverse Handler"/>-->
			<flow-ref name="Remove Payment Order Container and Transaction Service Steps from BM"/>
			<jms:publish destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
				<jms:message correlationId="#[correlationId]">
					<jms:body>#[payload]</jms:body>
					<jms:properties>
						#[{
						JMSCorrelationID: vars.correlationID,
						MULE_CORRELATION_ID: vars.correlationId,
						MULE_CORRELATION_GROUP_SIZE: '-1',
						MULE_CORRELATION_SEQUENCE:'-1'
						}]</jms:properties>
				</jms:message>
			</jms:publish>

			<java:invoke
					class="com.cit.mpayment.components.recurring.RecurringExecutionStatusUpdaterComponent"
					method="process(com.cit.mpaymentapp.common.message.BusinessMessage, java.lang.String, java.lang.String)"
					instance="recurringExecutionStatusUpdaterComponent">
				<java:args><![CDATA[#[{arg0: payload, arg1: vars.correlationID, arg2: vars.sentToRecurring}]]]></java:args>
			</java:invoke>

		</on-error-continue>

	</error-handler>


	<on-error-continue type="VALIDATION"
					   name="filterHandler">
		<logger level="ERROR"
				message="######### Mule Channels filterHandler ##########"></logger>
		<flow-ref name="Handle_Filter_Exceptions" />
		<flow-ref name="failed-service-log" />
		<flow-ref name="Reverse Handler" />
		<flow-ref
				name="Remove Payment Order Container and Transaction Service Steps from BM" />
		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]"
					 config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>

	</on-error-continue>

	<on-error-continue when="#[error.errorType.identifier == 'TIMEOUT']" name="timeoutHandler">
		<logger level="ERROR" message="######### Mule Channels timeoutHandler ##########"></logger>
		<flow-ref name="Handle_Timeout_Exceptions"/>
		<flow-ref name="failed-service-log" />
		<flow-ref name="Remove Payment Order Container and Transaction Service Steps from BM"/>

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
	</on-error-continue>

	<sub-flow name="save-service-log">
		<java:invoke class="com.cit.mpayment.components.ServiceRequestLoggerComponent" method="saveServiceLog(com.cit.mpaymentapp.common.message.BusinessMessage)" instance="serviceLogComponent">
			<java:args><![CDATA[#[{
			'arg0': payload}]]]></java:args>
		</java:invoke>
	</sub-flow>


	<sub-flow name="Execute Reward">
		<java:invoke
				class="com.cit.mpayment.components.reward.RewardProgramExecutionComponent"
				method="executeReward(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="rewardProgramExecutionComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>
		<set-variable value="#[payload]" variableName="newMessage"></set-variable>

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />
	</sub-flow>

	<flow name="Reward Service Execution">
		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />
		<flow-ref name="Run Service Steps" />
		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />
	</flow>


	<sub-flow name="failed-service-log">
		<java:invoke
				class="com.cit.mpayment.components.ServiceRequestLoggerComponent"
				method="failedServiceLog(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="serviceLogComponent">
			<java:args><![CDATA[#[{
			'arg0': payload}]]]></java:args>
		</java:invoke>
	</sub-flow>

	<sub-flow name="enroll-risk-limit">
		<async doc:name="Async">
			<java:invoke class="com.cit.mpayment.components.CustomerManagerComponent" method="setExpiryDateForRiskLimit(com.cit.mpaymentapp.common.message.BusinessMessage)" instance="customerManagerComponent">
				<java:args><![CDATA[#[{
				'arg0': payload}]]]></java:args>
			</java:invoke>
		</async>
	</sub-flow>

	<flow name="Remote Registration Customer">
		<jms:listener config-ref="JMS_Config" destination="${inbound.remote.registration.customer.queue}" />
		<logger level="INFO" message="#[payload]" />
		<set-variable value="#[correlationId]" variableName="RegcorrelationId" />

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger"></flow-ref>
		<script:execute engine="groovy">
			<script:code>
				payload.getSoftFields().put('serviceFlow', 'services_'+java.lang.String.valueOf(payload.serviceInfo.id))
				return payload
			</script:code>
		</script:execute>
		<flow-ref name="#[payload.softFields.'serviceFlow']"></flow-ref>

		<validation:is-false expression="#[payload.status.errorFlag]" />
		<choice>
			<when expression="payload.serviceInfo.code == '13001'">
				<script:execute engine="groovy">
					<script:code>
						payload.getServiceInfo().setId('13301')
						payload.getServiceInfo().setCode('13301')
						return payload
					</script:code>
				</script:execute>
			</when>
			<otherwise>
				<logger message="############ No switching ######" level="INFO" />
			</otherwise>
		</choice>

		<java:invoke
				class="com.cit.mpayment.components.WalletManagerComponent"
				method="getBusinessServiceConfigIdByTypeID(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="walletManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<flow-ref name="Run Service Steps" />
		<flow-ref name="Set SME Flags" />
		<flow-ref name="save-service-log" />
		<set-variable variableName="MULE_REPLYTO_STOP" value="true" doc:name="MULE_REPLYTO_STOP" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${outbound.remote.registration.customer.queue}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[vars.RegcorrelationId]">
				<jms:body>#[payload]</jms:body>
			</jms:message>
		</jms:publish>
		<error-handler>
			<on-error-continue>

				<flow-ref name="Handle_General_Exceptions"/>

				<flow-ref name="failed-service-log" />

				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${outbound.remote.registration.customer.queue}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
					<jms:message correlationId="#[correlationId]">
						<jms:body>#[payload]</jms:body>
						<jms:properties>
							#[{
							MULE_CORRELATION_ID: vars.correlationId,
							MULE_CORRELATION_GROUP_SIZE: '-1',
							MULE_CORRELATION_SEQUENCE:'-1'
							}]</jms:properties>
					</jms:message>
				</jms:publish>
			</on-error-continue>
		</error-handler>

	</flow>

	<flow name="VERiCASH Mobile Synch Proxy">
		<jms:listener config-ref="JMS_Config" destination="${inbound.vericash.synch.proxy.queue}" />
		<async>
			<set-variable value="#[correlationId]" variableName="correlationId" />
			<flow-ref name="Request Information Before ProxyFlow" />
			<flow-ref name="ProxyFlow" />

			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${outbound.vericash.synch.proxy.queue}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
				<jms:message correlationId="#[correlationId]">
					<jms:body>#[payload]</jms:body>
					<jms:properties>
						#[{
						MULE_CORRELATION_ID: vars.correlationId,
						MULE_CORRELATION_GROUP_SIZE: '-1',
						MULE_CORRELATION_SEQUENCE:'-1'
						}]</jms:properties>
				</jms:message>
			</jms:publish>
		</async>
		<error-handler ref="ErrorHandlerAndReplier" />
	</flow>

	<flow name="UBA USSD Synch Proxy">
		<jms:listener config-ref="JMS_Config" destination="${inbound.uba.ussd.synch.proxy.queue}" />
		<flow-ref name="USSDProxyFlow" />
		<async>
			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${outbound.uba.ussd.synch.proxy.queue}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
				<jms:message correlationId="#[correlationId]">
					<jms:body>#[payload]</jms:body>
					<jms:properties>
						#[{
						MULE_CORRELATION_ID: vars.correlationId,
						MULE_CORRELATION_GROUP_SIZE: '-1',
						MULE_CORRELATION_SEQUENCE:'-1'
						}]</jms:properties>
				</jms:message>
			</jms:publish>
		</async>
		<error-handler ref="UBAUssdErrorHandlerAndReplier" />
	</flow>

	<sub-flow name="Request Information Before ProxyFlow">
		<choice>
			<when expression="#[payload.serviceInfo.id == ${service.interswitch.voucher.redeem.code}]">
				<flow-ref name="PopulateRedemptionServiceData"/>
			</when>
			<otherwise>
				<logger level="INFO" message="Routing to Proxy Flow" />
			</otherwise>
		</choice>
	</sub-flow>

	<flow name="Payment to Merchant Authorization Externally">
		<jms:listener config-ref="JMS_Config" destination="${inbound.customer.merchant.authorization}" />
		<async>
			<flow-ref name="ProxyFlow" />
		</async>
		<error-handler ref="errorHandler" />
	</flow>

	<flow name="InterSwitch Mobile Synch Proxy">
		<jms:listener config-ref="JMS_Config" destination="${inbound.interswitch.synch.proxy.queue}" />
		<async>
			<flow-ref name="ProxyFlow" />
			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${oubound.interswitch.synch.proxy.queue}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
				<jms:message correlationId="#[correlationId]">
					<jms:body>#[payload]</jms:body>
					<jms:properties>
						#[{
						MULE_CORRELATION_ID: vars.correlationId,
						MULE_CORRELATION_GROUP_SIZE: '-1',
						MULE_CORRELATION_SEQUENCE:'-1'
						}]</jms:properties>
				</jms:message>
			</jms:publish>
		</async>
		<error-handler ref="errorHandler" />
	</flow>

	<flow name="VERiCASH Mobile Asynch Proxy">
		<jms:listener config-ref="JMS_Config" destination="${inbound.vericash.asynch.proxy.queue}" />
		<async>
			<flow-ref name="ProxyFlow" />
		</async>
		<error-handler ref="errorHandler" />
	</flow>

	<flow name="Settlement">
		<jms:listener config-ref="JMS_Config" destination="${inbound.settlemet.queue}" />
		<async>
			<flow-ref name="ProxyFlow" />
		</async>
	</flow>

	<!--	<http:listener-config name="Scheduler_Commission_RollUp_Config" >-->
	<!--		<http:listener-connection host="localhost" port="8081" />-->
	<!--	</http:listener-config>-->

	<!--	<flow name="schedulerCommissionRollUp" >-->
	<!--		<http:listener-->
	<!--				config-ref="Scheduler_Commission_RollUp_Config" path="/test" allowedMethods="GET">-->
	<!--		</http:listener>-->
	<!--		<logger message="eh el kalam #[attributes.queryParams.commission_roll_up_id] and #[attributes.queryParams.is_real_time]  " level="INFO" />-->
	<!--		<java:invoke-->
	<!--				class="com.cit.mpayment.components.commission.CommissionRollUpComponent"-->
	<!--				method="getConfigForSchedulerCommission(java.lang.Integer, boolean)"-->
	<!--				instance="commissionRollUpComponent">-->
	<!--			<java:args>-->
	<!--				<![CDATA[#[{ arg0: attributes.queryParams.commission_roll_up_id-->
	<!--							, arg1: attributes.queryParams.is_real_time }]]]>-->
	<!--			</java:args>-->
	<!--			<flow-ref name="Commission Asynch Flow" />-->
	<!--		</java:invoke>-->
	<!--	</flow>-->

	<http:listener-config name="Redeem_Voucher_HTTP_Listener_config">
		<http:listener-connection host="localhost" port="9292" />
	</http:listener-config>

	<flow name="Redeem Voucher From ATM">
		<http:listener path="services/redeem" config-ref="Redeem_Voucher_HTTP_Listener_config"/>
		<async>
			<flow-ref name="ProxyFlow" />

			<java:invoke
					class="com.cit.mpayment.transformer.AuthorizationTransformer"
					method="doTransform(java.lang.Object)"
					instance="authorizationTransformer">
				<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
			</java:invoke>
		</async>

		<error-handler>
			<on-error-continue>
				<java:invoke
						class="com.cit.mpayment.transformer.AuthorizationTransformer"
						method="doTransform(java.lang.Object)"
						instance="authorizationTransformer">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>
			</on-error-continue>
		</error-handler>
	</flow>

	<flow name="Validate UBA Customer Pin">
		<jms:listener config-ref="JMS_Config" destination="${inbound.customer.validate.pin.queue}" />
		<async>
			<set-variable value="SERVICE" variableName="logTarget"></set-variable>
			<set-variable value="Initiated" variableName="logStatus"></set-variable>
			<flow-ref name="Service-Logger" />

			<flow-ref name="10000551" />
			<flow-ref name="save-service-log" />

			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${outbound.customer.validate.pin.queue}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
				<jms:message correlationId="#[correlationId]">
					<jms:body>#[payload]</jms:body>
					<jms:properties>
						#[{
						MULE_CORRELATION_ID: vars.correlationId,
						MULE_CORRELATION_GROUP_SIZE: '-1',
						MULE_CORRELATION_SEQUENCE:'-1'
						}]</jms:properties>
				</jms:message>
			</jms:publish>
		</async>

		<error-handler ref="defaultErrorHandler" />
	</flow>

	<flow name="UBA USSD Asynch Flow">
		<jms:listener config-ref="JMS_Config" destination="${inbound.uba.ussd.asynch.queue}" />
		<async>
			<set-variable variableName="msisdn" value="#[payload.primarySenderInfo.msisdn]" />
			<flow-ref name="USSDAsynchFlow" />
		</async>
		<error-handler ref="UBAUssdAsynchErrorHandlerAndReplier" />
	</flow>

	<sub-flow name="USSDAsynchFlow">

		<java:invoke
				class="com.cit.mpayment.components.CountersComponent"
				method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="counterComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />

		<java:invoke
				class="com.cit.mpayment.components.SecurityManagerComponent"
				method="checkSecurity(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="securityManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<java:invoke
				class="com.cit.mpayment.components.WalletManagerComponent"
				method="retrieveAndCheckService(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="walletManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<java:invoke
				class="com.cit.mpayment.components.WalletManagerComponent"
				method="prepareReceiverData(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="walletManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<flow-ref name="Fill Users Data" />

		<flow-ref name="Run Service Steps" />

		<set-payload value="#[vars.newMessage]"></set-payload>

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Succeeded" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />

		<flow-ref name="save-service-log" />

		<choice>
			<when expression="#[payload.status.statusCode !=null]" >
				<script:execute engine="groovy">
					<script:code>
						payload.getStatus().setErrorFlag(true)
						return payload
					</script:code>
				</script:execute>
			</when>
			<otherwise>
				<logger message="There is no failure code" level="INFO" />
			</otherwise>
		</choice>
	</sub-flow>

	<sub-flow name="Validate Receiver Sub Flow">
		<logger level="INFO" message="INSIDE VALIDATE RECEIVER CODITION MULE-CONFIG MULE" />

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />

		<java:invoke
				class="com.cit.mpayment.components.SecurityManagerComponent"
				method="checkSecurity(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="securityManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<java:invoke
				class="com.cit.mpayment.components.CustomerProfileComponent"
				method="saveTransactionHistoryProfile(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="customerProfileComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<java:invoke
				class="com.cit.mpayment.components.WalletManagerComponent"
				method="retrieveAndCheckService(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="walletManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<java:invoke
				class="com.cit.mpayment.components.WalletManagerComponent"
				method="prepareReceiverData(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="walletManagerComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<flow-ref name="Fill Users Data" />

		<flow-ref name="Run Service Steps" />

		<set-payload value="#[vars.newMessage]"></set-payload>

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Succeeded" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger"></flow-ref>

		<flow-ref name="save-service-log" />

		<choice>
			<when expression="#[payload.status.statusCode !=null]" >
				<script:execute engine="groovy">
					<script:code>
						payload.getStatus().setErrorFlag(true)
						return payload
					</script:code>
				</script:execute>
			</when>
			<otherwise>
				<logger message="There is no failure code" level="INFO" />
			</otherwise>
		</choice>
	</sub-flow>

	<flow name="NIBSS Remote Registration Customer">

		<jms:listener config-ref="JMS_Config" destination="jms/queue/nibssremoteRegistrationCustomerQueue" />
		<logger level="INFO" message="Nibss Remote Rigistration #[payload]" />

		<set-variable value="SERVICE" variableName="logTarget"></set-variable>
		<set-variable value="Initiated" variableName="logStatus"></set-variable>
		<flow-ref name="Service-Logger" />

		<flow-ref name="services_44448"></flow-ref>

		<flow-ref name="save-service-log" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="jms/queue/nibssremoteRegistrationCustomerOutputQueue" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>

		<error-handler>
			<on-error-continue>
				<flow-ref name="Handle_General_Exceptions"/>

				<flow-ref name="failed-service-log" />

				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="jms/queue/nibssremoteRegistrationCustomerOutputQueue" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
					<jms:message correlationId="#[correlationId]">
						<jms:body>#[payload]</jms:body>
						<jms:properties>
							#[{
							MULE_CORRELATION_ID: vars.correlationId,
							MULE_CORRELATION_GROUP_SIZE: '-1',
							MULE_CORRELATION_SEQUENCE:'-1'
							}]</jms:properties>
					</jms:message>
				</jms:publish>
			</on-error-continue>
		</error-handler>
	</flow>

	<flow name="Main-Proxy-Flow-Vericash-API">

		<jms:listener config-ref="JMS_Config" destination="jms/uba/service/api/request/synch/input"  numberOfConsumers="100" />
		<set-variable variableName="responseQueue" value="jms/uba/service/api/response/synch/output" />
		<logger level="INFO" message="Main-Proxy-Flow-Vericash-API ##correlationId: #[correlationId] --------------- BusinessMessage main proxy flow #[payload]" />

		<logger level="INFO" message="Before transformation #[payload]--------------- " />

		<!-- Deserialize JSON payload into BusinessMessage -->
		<java:invoke
				class="com.cit.mpaymentapp.interceptors.BusinessMsgTransformer"
				method="convertToBusinessMessage(java.lang.String)"
				instance="businessMsgTransformer">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<logger level="INFO" message="After transformation #[payload]--------------- " />
		<!--		<script:execute engine="groovy">-->
		<!--			<script:code>-->
		<!--				payload.getSoftFields().put('tracingId', vars.correlationId)-->
		<!--				return payload-->
		<!--			</script:code>-->

		<!--		</script:execute>-->
		<choice>
			<when expression="#[payload.softFields != null and payload.softFields.'SME_SCREEN' == true]">
				<logger message="inside SME screen" level="INFO" />
				<!--		<java:invoke-->
				<!--				class="com.cit.mpayment.components.sme.SMEManagerComponent"-->
				<!--				method="validateSME(com.cit.mpaymentapp.common.message.BusinessMessage)"-->
				<!--				instance="sMEManagerComponent">-->
				<!--			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>-->
				<!--		</java:invoke>-->
			</when>
			<otherwise>
				<logger message="it is not SME screen" level="INFO" />
			</otherwise>
		</choice>
		<flow-ref name="The Main flow for Transactions" />
		<logger level="INFO" message="Main-Flow-After returning ##correlationId: #[correlationId] --------------- " />
		<flow-ref name="Remove Payment Order Container and Transaction Service Steps from BM"></flow-ref>
		<logger level="INFO" message="#[payload]" />
		<logger level="INFO" message="7ataha fel goal ya reyad abl el publish"></logger>

		<logger level="INFO" message="before convertToJson payload #[payload]" />

		<java:invoke
				class="com.cit.mpaymentapp.interceptors.BusinessMsgTransformer"
				method="convertToJson(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="businessMsgTransformer">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>

		<logger level="INFO" message="after convertToJson payload #[payload]" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS" >
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<logger level="INFO" message="7ataha fel goal ya reyad b3d el publish"></logger>
		<error-handler ref="muleChannelsExceptionStrategy" />
	</flow>
	<flow name="Remove Payment Order Container and Transaction Service Steps from BM">
		<logger level="INFO" message="Removing Payment Order Container and transaction service steps (different hibernate versions
	cause response failures)"></logger>
		<java:invoke
				class="com.cit.mpayment.components.CustomerProfileComponent"
				method="RemovePOCandTrxServiceSteps(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="customerProfileComponent">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>
		<script:execute engine="groovy">
			<script:code>
				payload.getSoftFields().remove("PMT_ORDR_CONTNR")
				return payload
			</script:code>
		</script:execute>

		<logger level="INFO" message="Removing Payment Order Container and transaction service steps successfully executed"></logger>


	</flow>

	<flow name="Recurring-Flow">

		<jms:listener config-ref="JMS_Config"
					  destination="jms/sanlam/service/recurring/mule/asynch/input"
					  ackMode="AUTO">
		</jms:listener>

		<set-variable variableName="responseQueue" value="jms/sanlam/service/recurring/mule/asynch/output" />
		<set-variable variableName="correlationID" value="#[correlationId]" />
		<set-variable variableName="consumedByMule" value="2" />
		<set-variable variableName="sentToRecurring" value="3" />

		<java:invoke
				class="com.cit.mpayment.components.recurring.RecurringExecutionStatusUpdaterComponent"
				method="process(com.cit.mpaymentapp.common.message.BusinessMessage, java.lang.String, java.lang.String)"
				instance="recurringExecutionStatusUpdaterComponent">
			<java:args><![CDATA[#[{arg0: payload, arg1: vars.correlationID, arg2: vars.consumedByMule}]]]></java:args>
		</java:invoke>

		<flow-ref name="The Main flow for Transactions" />

		<jms:publish config-ref="JMS_Config"
					 destination="#[vars.responseQueue]" sendCorrelationId="ALWAYS">
			<jms:message>
				<jms:body><![CDATA[#[payload]]]></jms:body>
				<jms:properties>
					#[{
					JMSCorrelationID: vars.correlationID,
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>

		<java:invoke
				class="com.cit.mpayment.components.recurring.RecurringExecutionStatusUpdaterComponent"
				method="process(com.cit.mpaymentapp.common.message.BusinessMessage, java.lang.String, java.lang.String)"
				instance="recurringExecutionStatusUpdaterComponent">
			<java:args><![CDATA[#[{arg0: payload, arg1: vars.correlationID, arg2: vars.sentToRecurring}]]]></java:args>
		</java:invoke>

		<error-handler ref="recurringChannelsExceptionStrategy" />
	</flow>
	<!--<flow name="Main-Proxy-Flow-for-testing">


            <jms:listener config-ref="JMS_Config" destination="jms/uba/service/api/request/synch/inputs" />
           <set-variable variableName="responseQueue" value="jms/uba/service/api/response/synch/outputs" />
            <logger level="INFO" message="HHHTTTTTTPPP"/>
            <set-variable variableName="intChannel"
                    value="FrameWork2" />
                            <set-variable variableName="intChannel"
                    value="FrameWork2" />
        <set-variable variableName="SERVICE_ID"
                    value="PCI-UNRegister"/>
        <set-variable variableName="WALLET_SHORT_CODE"
                    value="2020"/>
            <set-variable variableName="INSTANCE_ID"
                    value="1234"/>
        <set-variable variableName="STACK_ID"
                    value="555555"/>
        <set-variable variableName="LOG_ID"
                    value="66666"/>
            <logger level="INFO" message="#[vars.SERVICE_ID]"/>
            <logger level="INFO" message="#[vars.WALLET_SHORT_CODE]"/>
            <logger level="INFO" message="#[vars.INSTANCE_ID]"/>
            <logger level="INFO" message="#[vars.STACK_ID]"/>
            <logger level="INFO" message="#[vars.LOG_ID]"/>
             <set-variable variableName="businessMessage" value="#[payload]" />
                <set-variable variableName="oldSoftFields" value="#[payload.softFields]" />
        <java:invoke
                class="com.cit.mpayment.components.integration.BusinessMessageComponent"
                method="convertBusinessMessage(com.cit.mpaymentapp.common.message.BusinessMessage)"
                instance="businessMessageComponent">
                <java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
            </java:invoke>

            <logger message="#######################send queue  " level="INFO" />

            <flow-ref name="vc.int.http.flow"/>



                <set-variable variableName="businessMessage" value="#[payload]" />

                <logger level="INFO" message="#[payload]"/>

            <java:invoke
                class="com.cit.mpayment.components.integration.BusinessMessageComponent"
                method="onCall(java.lang.String,java.lang.Object,java.util.Map)"
                instance="businessMessageComponent">
                <java:args><![CDATA[#[{ 'arg0': payload.^raw as String,
                                        'arg1': vars.businessMessage,
                                        'arg2': vars.oldSoftFields }]]]></java:args>
            </java:invoke>

            <script:execute engine="groovy">
                <script:code>
                    payload.getSoftFields().remove('INTEGRATION_LOGS')
                    return payload
                </script:code>
            </script:execute>


            <jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS" >
                <jms:message correlationId="#[correlationId]">
                    <jms:body>#[payload]</jms:body>
                    <jms:properties>
                        #[{
                        MULE_CORRELATION_ID: vars.correlationId,
                        MULE_CORRELATION_GROUP_SIZE: '-1',
                        MULE_CORRELATION_SEQUENCE:'-1'
                        }]</jms:properties>
                </jms:message>
            </jms:publish>
            <error-handler ref="muleChannelsExceptionStrategy" />
        </flow>-->

</mule>



