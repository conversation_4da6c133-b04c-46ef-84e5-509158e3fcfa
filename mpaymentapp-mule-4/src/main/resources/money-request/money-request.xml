<mule xmlns:dw="http://www.mulesoft.org/schema/mule/ee/dw"
      xmlns:http="http://www.mulesoft.org/schema/mule/http"
      xmlns:java="http://www.mulesoft.org/schema/mule/java"
      xmlns="http://www.mulesoft.org/schema/mule/core"
      xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="
        http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
        http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
        http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
        http://www.mulesoft.org/schema/mule/ee/dw http://www.mulesoft.org/schema/mule/ee/dw/current/dw.xsd">

    <http:listener-config name="View_Request_Details_MoneyRequest">
        <http:listener-connection host="localhost" port="9999" connectionIdleTimeout="600000"/>
    </http:listener-config>

    <flow name="viewMoneyRequest">
        <http:listener config-ref="View_Request_Details_MoneyRequest" path="/muleApi" allowedMethods="POST">
        </http:listener>
        <logger level="INFO" message="teeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeestoooo"/>

        <!-- Transform the incoming HTTP payload to DynamicPayload -->
        <dw:transform-message doc:name="Transform HTTP Payload to DynamicPayload">
            <dw:input-payload mimeType="application/json"/>
            <dw:set-payload><![CDATA[%dw 2.0
                output application/java
                import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload
                ---
                DynamicPayload {
                    // Define properties of DynamicPayload based on the incoming payload
                    // For example:
                    payload: payload
                }]]></dw:set-payload>
        </dw:transform-message>

        <!-- Invoke the Java service with the transformed payload -->
        <java:invoke class="com.cit.moneyrequest.services.viewingMoneyRequests.ViewRequestDetailsManager"
                     method="process(com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload)"
                     instance="viewRequestDetailsMoneyRequest">
            <java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
        </java:invoke>

        <logger level="INFO" message="teeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeestuuuuu"/>
    </flow>
</mule>
