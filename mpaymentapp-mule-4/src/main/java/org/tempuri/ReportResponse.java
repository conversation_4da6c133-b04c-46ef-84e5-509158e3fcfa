
package org.tempuri;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReportResponse", propOrder = {
    "mailboxedDate",
    "paidDate",
    "fees",
    "amount",
    "officeName",
    "officeCode",
    "areaName",
    "receiverName",
    "senderName",
    "postNum"
})
public class ReportResponse {

    @XmlElement(name = "MailboxedDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar mailboxedDate;
    @XmlElement(name = "PaidDate", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar paidDate;
    @XmlElement(name = "Fees", required = true)
    protected BigDecimal fees;
    @XmlElement(name = "Amount", required = true)
    protected BigDecimal amount;
    @XmlElement(name = "OfficeName")
    protected String officeName;
    @XmlElement(name = "OfficeCode")
    protected String officeCode;
    @XmlElement(name = "AreaName")
    protected String areaName;
    @XmlElement(name = "ReceiverName")
    protected String receiverName;
    @XmlElement(name = "SenderName")
    protected String senderName;
    @XmlElement(name = "PostNum")
    protected String postNum;

    public XMLGregorianCalendar getMailboxedDate() {
        return mailboxedDate;
    }

    public void setMailboxedDate(XMLGregorianCalendar value) {
        this.mailboxedDate = value;
    }

    public XMLGregorianCalendar getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(XMLGregorianCalendar value) {
        this.paidDate = value;
    }

    public BigDecimal getFees() {
        return fees;
    }

    public void setFees(BigDecimal value) {
        this.fees = value;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal value) {
        this.amount = value;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String value) {
        this.officeName = value;
    }

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String value) {
        this.officeCode = value;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String value) {
        this.areaName = value;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String value) {
        this.receiverName = value;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String value) {
        this.senderName = value;
    }

    public String getPostNum() {
        return postNum;
    }

    public void setPostNum(String value) {
        this.postNum = value;
    }

}
