package com.cit.mpayment.components;

import com.cit.mpaymentapp.common.email.Email;
import com.cit.mpaymentapp.common.email.template.EmailTemplate;
import com.cit.mpaymentapp.common.gar.GarManager;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.ExternalTransactionInformation;
import com.cit.mpaymentapp.common.transaction.TransactionExecutionManager;
import com.cit.mpaymentapp.model.alert.*;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.mpaymentapp.model.customer.CustomerGar;
import com.cit.mpaymentapp.model.transaction.TransactionExecutionSummary;
import com.cit.shared.error.exception.GeneralFailureException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

@Component
@RequiredArgsConstructor(onConstructor = @__({@Autowired, @Lazy}))
public class EMailComponent {

    private final Email eMailSender;
    private final EmailTemplate emailTemplate;
    private final TransactionExecutionManager transactionExecutionManager;
    private final GarManager garManager;
    private final Properties prop = new Properties();
    private final static String EMAIL_MESSAGE_RECEIVER_PK_ID = "1";
    private static final String TESTING_ENV_EMAIL = System.getenv("DISPUTE_TRX_EMAIL");

    @PostConstruct
    public void init(){
        try {
            prop.load(this.getClass().getClassLoader()
                    .getResourceAsStream("mule-app.properties"));

        }catch(IOException e){
            e.printStackTrace();
        }
    }

    public BusinessMessage sendDisputedTransactionEmail(BusinessMessage businessMessage)
            throws GeneralFailureException {
    	
        Customer customer = (Customer)businessMessage.getSoftFields().get("DISPUTE_TRX_CUSTOMER");

        List<EmailMessageTemplate> emailMessageTemplates = this.emailTemplate.getEMailTemplate(EmailMessageTemplateType.DISPUTE_TRANSACTION);

        if (!emailMessageTemplates.isEmpty()) {

            EmailMessage emailMessage = new EmailMessage();
            List<EmailMessageReceiver> emailMessageReceivers = new ArrayList<EmailMessageReceiver>();
            EmailMessageReceiver emailMessageReceiver = new EmailMessageReceiver();
            EmailMessageReceiverPK emailMessageReceiverPK = new EmailMessageReceiverPK();

            emailMessageReceiverPK.setId(EMAIL_MESSAGE_RECEIVER_PK_ID);
            String recepientEmail;
            if(!StringUtils.isEmpty(TESTING_ENV_EMAIL))
            	recepientEmail = TESTING_ENV_EMAIL;
            else
            	recepientEmail = prop.getProperty("dispute.transaction.add.email");
            
            if (recepientEmail == null) {
                emailMessageReceiver.setEmail(customer.getEmail());
            } else {
                emailMessageReceiver.setEmail(recepientEmail);
            }
            
            emailMessageReceiver.setEmailMessage(emailMessage);
            emailMessageReceiver.setId(emailMessageReceiverPK);
            emailMessageReceivers.add(emailMessageReceiver);

            emailMessage.setAgentAccount(null);

            String body = formatEmail(customer, businessMessage, (EmailMessageTemplate) emailMessageTemplates.get(0));
            emailMessage.setBody(body);
            emailMessage.setCustomerID(customer.getCustomerID().longValue());

            emailMessage.setEmailMessageReceivers(emailMessageReceivers);

            emailMessage.setMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());
            emailMessage.setSubject(((EmailMessageTemplate) emailMessageTemplates.get(0)).getSubject());
            this.eMailSender.sendEmail(emailMessage);
        }
        return businessMessage;
    }

    private String formatEmail(Customer customer, BusinessMessage businessMessage, EmailMessageTemplate emailMessageTemplate) {
        String body = emailMessageTemplate.getBody();
        
        String fName = customer.getFirstName() == null ? "" : customer.getFirstName();
        String mName = customer.getMiddleName() == null ? "" : customer.getMiddleName();
        String lName = customer.getLastName() == null ? "" : customer.getLastName();
        
        String customerFullName = fName + " " + mName + " " + lName;
        String customerMsisdn = customer.getMsisdn();
        long senderId = customer.getCustomerID().longValue();
        String tranStatus = "", sourcePaymentType = "", sourcePaymentId = "", destinationId = "", destinationType = "", tranNarration = "";

        ExternalTransactionInformation externalTransactionInformation = (ExternalTransactionInformation) businessMessage.getExternalTransactionInformations().get(0);
        
        Long tranId = extractVCTransactionIdFromNarration(externalTransactionInformation.getNarration(), externalTransactionInformation.isMobileBanking());
        String tranAmt = externalTransactionInformation.getTranAmt();
        String tranDate = formatDate(externalTransactionInformation.getTranDate());
        String tranType = formatTransactionType(externalTransactionInformation.getTranSubtype());
        tranNarration = externalTransactionInformation.getNarration();
        if(tranId != null) {
        
        	TransactionExecutionSummary transactionExecutionSummary = this.transactionExecutionManager.findTransactionSummaryByID(tranId);
	        
	        if (transactionExecutionSummary != null) {
	        	tranStatus = transactionExecutionSummary.getTransactionStatus().toString();
	        	destinationType = transactionExecutionSummary.getServiceName();
	        	
	        	String KYCReceiver = transactionExecutionSummary.getKYC_Receiver() == null ? "" : transactionExecutionSummary.getKYC_Receiver();
	        	String KYCReceiverAccount = transactionExecutionSummary.getKYC_ReciverAccount() == null ? "" : transactionExecutionSummary.getKYC_ReciverAccount();

	        	sourcePaymentId = transactionExecutionSummary.getKYC_SenderAccount();
	        	destinationId = KYCReceiver + " / " + KYCReceiverAccount;
	        }
	        
	        if (!StringUtils.isEmpty(sourcePaymentId)) {
	        	CustomerGar senderCustomerGar = this.garManager.getCustomerGarWithAccNum(senderId, Long.parseLong(sourcePaymentId));
	            if (senderCustomerGar != null) {
		            sourcePaymentType = senderCustomerGar.getSourcePaymentMethods().getName();
	            }
	        } 

        }
                
        body = String.format(body, new Object[]{customerFullName, customerMsisdn, tranDate, tranAmt, tranType, sourcePaymentType, sourcePaymentId, destinationType, destinationId, tranNarration, tranStatus});
        return body;
    }

    private Long extractVCTransactionIdFromNarration(String narration, boolean isMobileBanking) {
    	
    	Long trxId = null;
    	String VCTransaction = null;
    	if(isMobileBanking || isVERiCASHTransaction(narration)) {
    		
    		String[] narrationItems = narration.split("/");
    		if(isACHTransaction(narration) && narrationItems.length > 4)
    			VCTransaction = narrationItems[3];
    		
    		else if(isAirlinesTransaction(narration))
    			VCTransaction = null;
    		
    		else if(narrationItems.length > 3)
    			VCTransaction = narrationItems[2];
    		
    		if(NumberUtils.isNumber(VCTransaction))
    			trxId = Long.parseLong(VCTransaction);
    	}
    	
    	return trxId;
    }
    
    private boolean isVERiCASHTransaction(String narration) {
    	return narration.contains("MOB") || narration.contains("VC");
    }
    
    private boolean isACHTransaction(String narration) {
    	return narration.contains("ACH");
    }
    
    private boolean isAirlinesTransaction(String narration) {
    	return narration.contains("FBP");
    }
    
    private String formatDate(String sourceDate) {
    	String targetDate = sourceDate;
    	
    	SimpleDateFormat sourceDateFormatter = new SimpleDateFormat("yyyyMMdd");
    	SimpleDateFormat targetDateFormatter = new SimpleDateFormat("dd-MMM-yyyy");

    	try {
			Date date = sourceDateFormatter.parse(sourceDate);
			targetDate = targetDateFormatter.format(date);
		} catch (ParseException e) {
			e.printStackTrace();
		}
    	
    	return targetDate;
    }
    
    private String formatTransactionType(String transactionType) {
    	String trxTType = transactionType;
    	
    	if(transactionType != null) {
	    	if(transactionType.equals("D"))
	    		trxTType = "Debit";
	    	else if(transactionType.equals("C"))
	    		trxTType = "Credit";
    	}
    	
    	return trxTType;
    }
}
