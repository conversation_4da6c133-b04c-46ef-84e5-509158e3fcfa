package com.cit.mpayment.bill.payment.vas;

import java.math.BigDecimal;

import org.activiti.engine.impl.util.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import com.cit.mpayment.components.BusinessEntityManagerComponent;
import com.cit.mpaymentapp.common.billPayment.vas.VASService;
import com.cit.mpaymentapp.common.format.MsisdnFormatterImpl;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.model.Enums.PaymentMethodType;
import com.cit.shared.error.exception.BusinessEntityException;
import com.cit.shared.error.exception.GeneralFailureException;

public class VASComponent implements VASService{

	private BusinessEntityManagerComponent businessEntityManagerComponent;

	@Override
	public BusinessMessage updateTotalTransactionAmountWithVASFees(BusinessMessage businessMessage) {
		
		BigDecimal VASFees = businessMessage.getTransactionInfo().getTransactionFeeAmount();
		BigDecimal trxTotal = businessMessage.getTransactionInfo().getTransactionAmount().add(VASFees);
		
		businessMessage.getTransactionInfo().setTransactionTotalAmount(trxTotal);
		businessMessage.getTransactionInfo().setTransactionVatAmount(BigDecimal.ZERO);
		
		return businessMessage;
	}
	
	@Override
	public BusinessMessage backupVASFees(BusinessMessage businessMessage) {
		
		businessMessage.getSoftFields().put("vasFees", businessMessage.getTransactionInfo().getTransactionFeeAmount());
		
		return businessMessage;
	}
	
	@Override
	public BusinessMessage restoreVASFees(BusinessMessage businessMessage) {
		
		businessMessage.getTransactionInfo().setTransactionFeeAmount((BigDecimal)businessMessage.getSoftFields().get("vasFees"));
		
		return businessMessage;
	}
	
	@Override
	public BusinessMessage getVisaFloatAccountForGetFeesService(BusinessMessage businessMessage) throws BusinessEntityException {
		if(businessMessage.getPrimarySenderInfo().getPaymentMethod().getPaymentMethodType() == PaymentMethodType.PREPAID_CARD.ordinal()) {
			businessEntityManagerComponent.prepareSenderVericashPrepaidFloatAccount(businessMessage);
		}
		
		return businessMessage;
	}
	
	@Override
	public BusinessMessage formatBenefeciaryMsisdn(BusinessMessage businessMessage) throws GeneralFailureException {
		String benefeciaryMsisdn = businessMessage.getPrimarySenderInfo().getMsisdn();
		String extraFieldsReceiverMsisdn;
		
		JSONObject extraFieldsObject = new JSONObject(businessMessage.getExtraFields());
		JSONObject billPaymentRequestObject = getExtraFieldsRequestObject(extraFieldsObject);
		
		if(!StringUtils.isEmpty(extraFieldsReceiverMsisdn = getExtraFieldsReceiverMsisdn(billPaymentRequestObject))) {
			benefeciaryMsisdn = extraFieldsReceiverMsisdn;
		}
		
		String countryIso2 = businessMessage.getWalletInfo().getCountryIso2();
		
		if(!StringUtils.isEmpty(benefeciaryMsisdn) && !StringUtils.isEmpty(countryIso2)) {
			MsisdnFormatterImpl msisdnFormatterImpl = MsisdnFormatterImpl.getInstance();
			String localFormat = msisdnFormatterImpl.internationalMsisdnToLocalMsisdn(benefeciaryMsisdn, countryIso2);
			if(!StringUtils.isEmpty(localFormat)) {
				businessMessage.setExtraFields(updateExtraFieldsWithFormattedMsisdn(extraFieldsObject, localFormat));
			}else
				throw new GeneralFailureException("VAL05033");
		}
		
		
		return businessMessage;
	} 
	
	private JSONObject getExtraFieldsRequestObject(JSONObject extraFieldsObject) {
		JSONObject req = null;
		
		if(extraFieldsObject.has("request")) 
			req = extraFieldsObject.getJSONObject("request");
			 
		return req;
	}
	
	private String getExtraFieldsReceiverMsisdn(JSONObject billPaymentRequestObject) {
		String msisdn = "";
		
		if(billPaymentRequestObject != null)
			msisdn = billPaymentRequestObject.optString("msisdn");
		
		return msisdn;
	}
	
	private String updateExtraFieldsWithFormattedMsisdn(JSONObject extraFieldsObject, String formattedMsisdn) {
		extraFieldsObject.getJSONObject("request").put("msisdn", formattedMsisdn);
		return extraFieldsObject.toString();
	}
	
	public static void main(String[] args) throws GeneralFailureException {
		VASComponent vasComponent = new VASComponent();

		String extraFields = "{\"request\":{\"utilityref\":\"***********\",\"amount\":\"1001\", \"msisdn\": \"+255786005000\"},\"moreData\":{},\"operatorId\":\"LUKU Prepaid Electricity\"}";

		BusinessMessage businessMessage = new BusinessMessage();
		businessMessage.getWalletInfo().setCountryIso2("TZ");
		businessMessage.getPrimarySenderInfo().setMsisdn("+255786005001");
		businessMessage.setExtraFields(extraFields);
		
		businessMessage = vasComponent.formatBenefeciaryMsisdn(businessMessage);
		System.out.println(businessMessage);
	}
	
	public void setBusinessEntityManagerComponent(BusinessEntityManagerComponent businessEntityManagerComponent) {
		this.businessEntityManagerComponent = businessEntityManagerComponent;
	}

}
