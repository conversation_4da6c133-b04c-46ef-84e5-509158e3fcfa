package com.cit.mpayment.config.configuration;

import com.cit.moneyrequest.services.FactoryMoneyRequest.CollectMoneyGenerator;
import com.cit.moneyrequest.services.FactoryMoneyRequest.PayForMeGenerator;
import com.cit.moneyrequest.services.MoneyRequestLoader;
import com.cit.moneyrequest.services.expirationComponent.ExpirationDateManager;
import com.cit.mpayment.components.*;
import com.cit.mpayment.factory.PaymentFactory;
import com.cit.mpayment.factory.TransactionEngineFactory;
import com.cit.mpaymentapp.common.account.BusinessAccountManager;
import com.cit.mpaymentapp.common.account.SvaAccountManager;
import com.cit.mpaymentapp.common.agent.AgentManagerHelper;
import com.cit.mpaymentapp.common.audit.AuditManager;
import com.cit.mpaymentapp.common.bank.account.types.BankAccountTypesManager;
import com.cit.mpaymentapp.common.billers.BillersLoaderManager;
import com.cit.mpaymentapp.common.blocking.BlockingService;
import com.cit.mpaymentapp.common.businessservice.BusinessServiceManager;
import com.cit.mpaymentapp.common.businessservice.ServiceManager;
import com.cit.mpaymentapp.common.commission.CommissionDistributionLocal;
import com.cit.mpaymentapp.common.commission.commissionsettlement.CommissionRollUpService;
import com.cit.mpaymentapp.common.counters.UserCounterManager;
import com.cit.mpaymentapp.common.customer.devices.CustomerDevicesManager;
import com.cit.mpaymentapp.common.customer.security.rules.SecurityRulesManager;
import com.cit.mpaymentapp.common.customer.unique.identifier.UserIdManager;
import com.cit.mpaymentapp.common.facade.CustomerServiceRemote;
import com.cit.mpaymentapp.common.facade.TransactionEngineRemote;
import com.cit.mpaymentapp.common.facade.agent.AgentManager;
import com.cit.mpaymentapp.common.fees.CalculateFeeService;
import com.cit.mpaymentapp.common.fees.FeesService;
import com.cit.mpaymentapp.common.gar.GarManager;
import com.cit.mpaymentapp.common.integration.settlement.CrossWalletTransactionsInfoManager;
import com.cit.mpaymentapp.common.log.MessageLogManager;
import com.cit.mpaymentapp.common.message.MessageService;
import com.cit.mpaymentapp.common.notification.InAppNotification;
import com.cit.mpaymentapp.common.notification.NotificationManagerLocal;
import com.cit.mpaymentapp.common.payment.PaymentMethodService;
import com.cit.mpaymentapp.common.pvoucher.PVoucherManager;
import com.cit.mpaymentapp.common.registration.BusinessEntityManager;
import com.cit.mpaymentapp.common.registration.CustomerManager;
import com.cit.mpaymentapp.common.remote.registration.CustomerRemoteRegistration;
import com.cit.mpaymentapp.common.risk.RiskService;
import com.cit.mpaymentapp.common.scheduler.SchedulerManager;
import com.cit.mpaymentapp.common.scheduler.TimeTriggeredActionsManager;
import com.cit.mpaymentapp.common.security.EncryptionDecryptionManager;
import com.cit.mpaymentapp.common.service.ServiceLogManager;
import com.cit.mpaymentapp.common.sme.LoadingActorsStrategy;
import com.cit.mpaymentapp.common.sme.SMEManager;
import com.cit.mpaymentapp.common.transaction.*;
import com.cit.mpaymentapp.common.util.LazyInitialization;
import com.cit.mpaymentapp.common.utility.general.lookups.component.GeneralLookupsComponent;
import com.cit.mpaymentapp.common.wallet.WalletManager;
import com.cit.mpaymentapp.common.workflow.WorkflowManager;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.interceptors.ExceptionInterceptor;
import com.cit.mpaymentapp.service.account.BusinessAccountManagerImpl;
import com.cit.mpaymentapp.service.alias.*;
import com.cit.mpaymentapp.service.commission.CommissionDistibution;
import com.cit.mpaymentapp.service.commission.CommissionServiceImpl;
import com.cit.mpaymentapp.service.commission.commissionsettlementmanager.CommissionRollUpServiceImpl;
import com.cit.mpaymentapp.service.commission.commissionsettlementmanager.factory.CommissionTypeFactory;
import com.cit.mpaymentapp.service.commission.commissionsettlementmanager.impl.RealTimeCommissionRollUpType;
import com.cit.mpaymentapp.service.commission.commissionsettlementmanager.impl.SchedulerCommissionRollUpType;
import com.cit.mpaymentapp.service.commission.commissionsettlementmanager.inteface.CommissionRollUpType;
import com.cit.mpaymentapp.service.customer.remote.registration.CustomerRemoteRegistrationManagerImpl;
import com.cit.mpaymentapp.service.facade.NonActiveAccountTransactionEngine;
import com.cit.mpaymentapp.service.facade.TransactionEngineImpl;
import com.cit.mpaymentapp.service.fees.CalculateFeeServiceImpl;
import com.cit.mpaymentapp.service.fees.FeesServiceImpl;
import com.cit.mpaymentapp.service.gar.GarManagerImpl;
import com.cit.mpaymentapp.service.payment.PaymentMethodManager;
import com.cit.mpaymentapp.service.registration.CustomerManagerImpl;
import com.cit.mpaymentapp.service.scheduler.TimeTriggeredActionsManagerImpl;
import com.cit.mpaymentapp.service.security.rules.SecurityRulesManagerImpl;
import com.cit.mpaymentapp.service.sme.SMEManagerImpl;
import com.cit.mpaymentapp.service.util.ProjectionsLoaderUtil;
import com.cit.mpaymentapp.service.util.SplitUtils;
import com.cit.service.commons.codeMapping.NewImplementationMappingComponent;
import com.cit.service.commons.codeMapping.PropertyLoaderImpl;
import com.cit.service.commons.codeMapping.PropertyLoaderInt;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadTransformer;
import com.cit.vericash.backend.commons.util.JsonMapperConverterUtil;
import com.cit.vericash.backend.commons.util.PropertyLoader;
import com.cit.mpaymentapp.service.currencyexchange.ExchangeRateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.Transactional;

import javax.interceptor.Interceptors;
import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@ComponentScan(basePackages = {
        "com.cit.mpaymentapp.*",
        "com.cit.service.commons.*",
        "com.citglobal.notifications.*"
})
@Transactional
@EnableAsync
@Slf4j
public class appConfigurations {
    @Autowired
    private MoneyRequestLoader moneyRequestLoader;

    @Autowired
    private SplitUtils splitUtils;
    public static final String APPLICATION = "application";
    public static final String MONEY_REQUEST = "money-request";
    @Autowired
    private IBaseDao baseDao;
    @Autowired
    private Environment environment;
    @Autowired
    private SchedulerManager schedulerManager;

    @Autowired
    private ProjectionsLoaderUtil projectionsLoaderUtil;
    @Autowired
    private SvaAccountManager svaAccountManager;

    @Autowired
    private MessageService messageManager;

    @Autowired
    private PropertyLoaderInt propertyLoader;

    @Autowired
    private AuditManager auditManager;

    @Autowired
    private BlockingService blockingManager;

    @Autowired
    private PaymentMethodManager paymentMethodManager;
    @Autowired
    private PropertyLoader commonPropertyLoader;
    @Autowired
    private NotificationManagerLocal notificationManager;

    @Autowired
    private ExchangeRateComponent exchangeRateComponent;

    @Autowired
    @Qualifier("bankAccountTypesManagerImpl")
    private BankAccountTypesManager bankAccountTypesManager;

    @Autowired
    private AgentManagerHelper agentManager;

    @Autowired
    private EncryptionDecryptionManager encryptionDecryptionMgr;

    @Autowired
    private RiskService riskService;

    @Autowired
    private FeesService feesService;

    @Autowired
    private TransactionDefManager transactionDefManager;

    @Autowired
    private TransactionExecutionManager transactionExecutionManager;

    @Autowired
    private BillersLoaderManager billersLoaderLocal;

    @Autowired
    private BusinessEntityManager businessEntityManager;

    @Autowired
    private SMEManager smeManager;
    @Autowired
    private CustomerDevicesManager customerDevicesManager;
    @Autowired
    private UserIdManager userIdManager;
    @Autowired
    private SecurityRulesManager SecurityRulesManager;
    @Autowired
    private SecurityRulesManagerImpl securityRulesManagerImpl;
    @Autowired
    private NewImplementationMappingComponent newImplementationMappingComponent;

    @Autowired
    private PropertyLoaderImpl propertyLoaderInt;

    @Autowired
    private BusinessServiceManager businessServiceManager;

    @Autowired
    private WalletManager walletManager;
    @Autowired
    @Qualifier("txnManager")
    private TransactionManager transactionManager;

    @Qualifier("loadingBECustomer")
    @Autowired
    private LoadingActorsStrategy loadingActorsForBeCustomer;

    @Qualifier("loadingBusinessUser")
    @Autowired
    private LoadingActorsStrategy loadingActorsForBusinessUser;
    @Autowired
    @Qualifier("reversalOrchestrationComponent")
    private ReversalOrchestrationComponent reversalOrchestrationComponent;
    @Autowired
    private PaymentFactory factory;
    //    @Autowired
//    @Qualifier("businessAccountManager")
//    private BusinessAccountManager businessUserAccountManager;
    @Autowired
    @Qualifier("lazyinitializationManager")
    private LazyInitialization lazyInitializationRemote;
    @Autowired
    @Qualifier("serviceLogManager")
    private ServiceLogManager serviceLogManagerImpl;
    @Autowired
    @Qualifier("commissionDistibution")
    private CommissionDistributionLocal commissionDistribution;
    @Autowired
    @Qualifier("commissionRollupTransactionResolver")
    private TransactionParameterBasedResolver transactionResolver;
    @Autowired
    @Qualifier("workflowManagerImpl")
    private WorkflowManager workflowManager;
    @Autowired
    @Qualifier("svaAccountManagerImpl")
    private SvaAccountManager svAccountManager;
    @Autowired
    @Qualifier("crossWalletTransactionsInfoManagerImpl")
    private CrossWalletTransactionsInfoManager crossWalletTransactionsInfoManager;
    @Autowired
    @Qualifier("userCounterManagerImpl")
    private UserCounterManager counterService;
    @Autowired
    @Qualifier("feesServiceImpl")
    private FeesService feeService;

    @Autowired
    @Qualifier("transactionEngineFactory")
    private TransactionEngineFactory transactionEngineFactory;

    @Autowired
    @Qualifier("txnNonActiveAccountManager")
    private TransactionManager nonActiveAccountTransactionManager;
    @Autowired
    @Qualifier("messageLogManager")
    private MessageLogManager messageLogManager;

    @Autowired
    @Qualifier("serviceManagerImpl")
    private ServiceManager serviceManager;
    @Autowired
    @Qualifier("calculateFeeRef")
    private CalculateFeeService calculateFeeSvcRef;

    @Autowired
    @Qualifier("txnStepsManager")
    private TransactionStepsManager txnStepsManager;

    @Autowired
    @Qualifier("generalLookupsComponent")
    private GeneralLookupsComponent generalLookupsComponent;
    @Autowired
    private DynamicPayloadTransformer dynamicPayloadTransformer;

    @Autowired
    @Qualifier("timetriggeredactionsmanagerImpl")
    private TimeTriggeredActionsManager timeTriggeredActionsManager;

    @Autowired
    @Qualifier(value = "feesCycleTransactionResolver")
    private TransactionParameterBasedResolver feesCycleTransactionResolver;
    @Autowired
    @Qualifier("paymentMethodManager")
    private PaymentMethodService paymentMethodService;
    @Autowired
    @Qualifier("customerService")
    private CustomerServiceRemote customerService;
    @Autowired
    @Qualifier("subscribersInboxMessagingManager")
    private com.cit.mpaymentapp.common.inbox.******************************** ********************************;
    @Autowired
    @Qualifier("agentManager")
    private AgentManager agentManagerr;
    @Autowired
    @Qualifier("inAppNotificationManager")
    private InAppNotification inAppNotification;
    @Autowired
    @Qualifier("pVoucherManager")
    private PVoucherManager pVoucherManager;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ExpirationDateManager expirationDateManager;


    //ya raaaaaaaaaaaaaaaaaaaaaaaaaaaab
    private GarManager garManagerRef;
    private CustomerManager customerManagerRef;
    private CustomerRemoteRegistration customerRemoteRegistrationRef;
    private CommissionRollUpService commissionRollUpServiceRef;
    private CommissionTypeFactory commissionTypeFactoryRef;
    @Transactional
    @Bean(name = "garManager")
    public GarManager garManager() {
        System.out.println("appConfigurations garManager");
        return new GarManagerImpl(
                baseDao, svaAccountManager, messageManager, propertyLoader, blockingManager, paymentMethodManager,
                notificationManager, bankAccountTypesManager, agentManager
        );
    }
    @Transactional
    @Bean(name ="aliasManagementTemplateService")
    public AliasManagementTemplateService aliasManagementTemplateService(AliasCheck aliasCheck, ManualAlias manualAlias, AutomaticAlias automaticAlias,  DynamicPayloadTransformer  dynamicPayloadTransformer){
        System.out.println("aliasManagementTemplateService");
        return new AliasManagementTemplateService(baseDao,aliasCheck , manualAlias, automaticAlias,  dynamicPayloadTransformer);
    }
    @Transactional
    @Bean(name ="automaticAlias")
    public AutomaticAlias automaticAlias(){
        System.out.println("automaticAlias");
        return new AutomaticAlias();
    }
    @Transactional
    @Bean(name ="manualAlias")
    public ManualAlias manualAlias(){
        System.out.println("automaticAlias");
        return new ManualAlias();
    }
    @Transactional
    @Bean(name ="aliasCheck")
    public AliasCheck aliasCheck(){
        System.out.println("automaticAlias");
        return new AliasCheck();
    }
    @Transactional
    @Bean(name ="numberStrategy")
    public NumberStrategy numberStrategy(){
        System.out.println("numberStrategy");
        return new NumberStrategy();
    }
    @Transactional
    @Bean(name ="characterStrategy")
    public CharacterStrategy characterStrategy(){
        System.out.println("characterStrategy");
        return new CharacterStrategy();
    }
    @Transactional
    @Bean(name ="underscoreStrategy")
    public UnderscoreStrategy underscoreStrategy(){
        System.out.println("underscoreStrategy");
        return new UnderscoreStrategy();
    }
    @Transactional
    @Bean(name ="garStrategy")
    public GarStrategy garStrategy(){
        System.out.println("garStrategy");
        return new GarStrategy(garManager());
    }
    @Transactional
    @Bean(name ="exchangeRateService")
    public ExchangeRateService exchangeRateService(){
        System.out.println("exchangeRateService");
        return new ExchangeRateService(commonPropertyLoader, baseDao);
    }


    @Transactional
    @Bean(name = "customerManager")
    public CustomerManager customerManager() {
        System.out.println("appConfigurations customerManager");

        return new CustomerManagerImpl(
                garManagerRef, baseDao, encryptionDecryptionMgr, auditManager, riskService, feesService,
                transactionDefManager, blockingManager, transactionExecutionManager, messageManager,
                propertyLoader, notificationManager, schedulerManager, billersLoaderLocal, businessEntityManager, smeManager, splitUtils,
                projectionsLoaderUtil,securityRulesManagerImpl);

    }

    @Transactional
    @Bean(name = "customerRemoteRegistrationManagerBean")
    public CustomerRemoteRegistration customerRemoteRegistration() {
        System.out.println("appConfigurations customerRemoteRegistration");
        garManagerRef = garManager();
        customerManagerRef = customerManager();

        CustomerRemoteRegistration customerRemoteRegistration = new CustomerRemoteRegistrationManagerImpl(
                baseDao, encryptionDecryptionMgr, notificationManager, blockingManager,
                customerDevicesManager, businessEntityManager, userIdManager, SecurityRulesManager, propertyLoader
        );
        customerRemoteRegistration.setGarManager(garManagerRef);
        customerRemoteRegistration.setCustomerManager(customerManagerRef);
        customerManagerRef.setCustomerRemoteRegistration(customerRemoteRegistration);
        customerManagerRef.setGarManager(garManagerRef);
        garManagerRef.setCustomerManager(customerManagerRef);

        customerRemoteRegistrationRef = customerRemoteRegistration;
        return customerRemoteRegistration;
    }

    @Transactional
    @Bean(name = "errorComponent")
    public ErrorHandlerComponent errorHandlerComponent() {
        System.out.println("appConfigurations ErrorHandlerComponent");
        return new ErrorHandlerComponent(transactionManager);
    }

    @Transactional
    @Bean(name = "smeManager")
    public SMEManager smeManager() {
        System.out.println("appConfigurations smeManager");
        return new SMEManagerImpl(baseDao, customerRemoteRegistrationRef, notificationManager,
                loadingActorsForBeCustomer, loadingActorsForBusinessUser);
    }

    @Transactional
    @Bean(name = "garComponent")
    public GarManagerComponent garComponent() {
        System.out.println("appConfigurations garComponent");
        return new GarManagerComponent(garManagerRef, customerManagerRef, reversalOrchestrationComponent,
                businessEntityManager, paymentMethodManager, smeManager(), factory);
    }

    @Transactional
    @Bean(name = "externalAccountComponentReader")
    public ExternalAccountComponentReader externalAccountComponentReaderComponent() {
        System.out.println("appConfigurations ExternalAccountComponentReader");
        return new ExternalAccountComponentReader(transactionManager);
    }

    @Transactional
    @Bean(name = "commissionDistibution")
    public CommissionDistibution commissionDistibutionComponent() {
        System.out.println("appConfigurations CommissionDistibution");
        return new CommissionDistibution(baseDao, transactionManager);
    }

    @Transactional(timeout = 10000)
    @Interceptors(ExceptionInterceptor.class)
    @Bean(name = "commissionServiceImpl")
    public CommissionServiceImpl commissionServiceImplComponent() {
        System.out.println("appConfigurations CommissionServiceImpl");
        return new CommissionServiceImpl(propertyLoader, baseDao, businessAccountManagerComponent(), messageManager, auditManager, lazyInitializationRemote
                , serviceLogManagerImpl, commissionDistribution, transactionManager, transactionResolver);
    }

    @Transactional
    @Bean(name = "transactionEngine")
    public TransactionEngineRemote transactionEngineComponent() {
        System.out.println("appConfigurations TransactionEngineImpl");
        return new TransactionEngineImpl(propertyLoader, customerManagerRef, businessEntityManager, transactionManager, baseDao, businessAccountManagerComponent(),
                workflowManager, riskService, feesService, svAccountManager, garManagerRef, crossWalletTransactionsInfoManager,
                commissionServiceImplComponent(), counterService);
    }

    @Transactional
    @Bean(name = "calculateFeeRef")
    public CalculateFeeServiceImpl calculateFeeRefComponent() {
        System.out.println("appConfigurations CalculateFeeServiceImpl");
        return new CalculateFeeServiceImpl(baseDao, feeService, transactionManager, customerManagerRef, businessEntityManager, businessAccountManagerComponent());
    }

    @Transactional
    @Bean(name = "nonActiveAccountTransactionEngine")
    public NonActiveAccountTransactionEngine NonActiveAccountTransactionEngineComponent() {
        System.out.println("appConfigurations NonActiveAccountTransactionEngine");
        return new NonActiveAccountTransactionEngine(
                propertyLoader,
                customerManagerRef,
                businessEntityManager,
                transactionManager,
                baseDao,
                businessAccountManagerComponent(),
                workflowManager,
                riskService,
                feesService,
                svAccountManager,
                garManagerRef,
                crossWalletTransactionsInfoManager,
                commissionServiceImplComponent()
        );
    }

    @Transactional
    @Bean(name = "txnManagerComponent")
    public TxnManagerComponent TxnManagerComponentComponent() {
        System.out.println("appConfigurations TxnManagerComponent");
        return new TxnManagerComponent(
                transactionEngineFactory,
                nonActiveAccountTransactionManager,
                messageLogManager,
                transactionEngineComponent(),
                serviceManager,
                calculateFeeSvcRef,
                txnStepsManager,
                generalLookupsComponent,
                smeManager(),
                baseDao,
                dynamicPayloadTransformer
        );
    }

    @Bean(name = "feesServiceImpl")
    @Interceptors(ExceptionInterceptor.class)
    public FeesService feesServiceImplComponent() {
        System.out.println("appConfigurations feesServiceImplComponent");
        return new FeesServiceImpl(
                baseDao,
                TimeTriggeredActionsManagerComponent(),
                auditManager,
                propertyLoader,
                messageManager,
                businessAccountManagerComponent(),
                feesCycleTransactionResolver,
                transactionManager,
                projectionsLoaderUtil,
                dynamicPayloadTransformer
        );
    }


    @Bean(name = "timetriggeredactionsmanagerImpl")
    public TimeTriggeredActionsManager TimeTriggeredActionsManagerComponent() {
        System.out.println("appConfigurations TimeTriggeredActionsManager");
        return new TimeTriggeredActionsManagerImpl(
                baseDao,
                propertyLoader,
                messageManager
        );
    }

    @Bean(name = "businessAccountManager")
    public BusinessAccountManager businessAccountManagerComponent() {
        System.out.println("appConfigurations BusinessAccountManager");
        return new BusinessAccountManagerImpl(
                baseDao,
                blockingManager
        );
    }

    @Bean(name = "customerManagerComponent")
    public CustomerManagerComponent CustomerManagerComponent() {
        System.out.println("appConfigurations CustomerManagerComponentCustomerManagerComponent");
        return new CustomerManagerComponent(
                paymentMethodService,
                customerManagerRef,
                customerService,
                businessEntityManager,
                ********************************,
                customerDevicesManager,
                garManagerRef,
                SecurityRulesManager,
                transactionManager,
                agentManagerr,
                userIdManager,
                smeManager(),
                inAppNotification,
                propertyLoader,
                pVoucherManager
        );
    }

    @Transactional
    @Bean(name = "commissionRollUpServiceImpl")
    public CommissionRollUpService commissionRollUpServiceImpl() {
        System.out.println("appConfigurations commissionRollUpServiceImpl");
        commissionTypeFactoryRef = CommissionTypeFactory();

        commissionRollUpServiceRef = new CommissionRollUpServiceImpl(businessEntityManager, garManagerRef, commissionTypeFactoryRef, newImplementationMappingComponent, propertyLoaderInt, businessServiceManager,walletManager);

        commissionTypeFactoryRef.getRealTimeCommissionRollUpType().setCommissionRollUpService(commissionRollUpServiceRef);
        commissionTypeFactoryRef.getSchedulerCommissionRollUpType().setCommissionRollUpService(commissionRollUpServiceRef);


        return commissionRollUpServiceRef;
    }

    @Bean(name = "commissionTypeFactory")
    public CommissionTypeFactory CommissionTypeFactory() {
        System.out.println("appConfigurations commissionTypeFactory");
        return new CommissionTypeFactory(realTimeCommissionRollUpType(),schedulerCommissionRollUpType());
    }

    @Bean(name = "realTimeCommissionRollUpType")
    public CommissionRollUpType realTimeCommissionRollUpType() {
        System.out.println("appConfigurations RealTimeCommissionRollUpType");
        return new RealTimeCommissionRollUpType();
    }


    @Bean(name = "schedulerCommissionRollUpType")
    public CommissionRollUpType schedulerCommissionRollUpType() {
        System.out.println("appConfigurations SchedulerCommissionRollUpType");
        return new SchedulerCommissionRollUpType();
    }
    @Bean(name = "collectMoneyService")
    public CollectMoneyGenerator CollectMoneyServiceComponent() {
        System.out.println("appConfigurations CollectMoneyGenerator");
        return new CollectMoneyGenerator(
                baseDao,
                expirationDateManager
        );
    }
    @Bean(name = "payForMeService")
    public PayForMeGenerator PayForMeServiceComponent() {
        System.out.println("appConfigurations PayForMeGenerator");
        return new PayForMeGenerator(
                baseDao,
                expirationDateManager,
                moneyRequestLoader
        );
    }

    @Bean
    public PropertyLoader propertyLoader() {
        System.out.println("appConfigurations propertyLoader");
        return new PropertyLoader();
    }

    @Bean
    public JsonMapperConverterUtil jsonMapperConverterUtil() {
        System.out.println("appConfigurations JsonMapperConverterUtil");
        return new JsonMapperConverterUtil();
    }

}