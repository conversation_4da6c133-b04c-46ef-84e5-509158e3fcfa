getBeCustomerStatus = SELECT bc.STATUS FROM BE_CUSTOMER bc JOIN CUSTOMER c ON \
  c.BE_CUSTOMER_ID = bc.ID WHERE c.MSISDN = '%1$s'
activateBeCustomer = UPDATE BE_CUSTOMER SET STATUS = %1$s WHERE ID = %2$s
getBeCustomerId = SELECT bc.ID FROM BE_CUSTOMER bc INNER JOIN BUSINESS_ENTITY_HIERARCHY beh \
  ON bc.CORPERATE_ID = beh.BUSINESS_ENTITY_ID \
  INNER JOIN CUSTOMER c \
  ON bc.ID = c.BE_CUSTOMER_ID \
  WHERE c.MSISDN = '%1$s' AND beh.REGISTRATION_NUMBER = %2$s
setPasswordAndPin = Update CUSTOMER SET PIN = '%1$s', HANDSET_PASSWORD= '%2$s' , USER_NAME='%3$s' \
  WHERE MSISDN='%4$s'
getBusinessEntityStatus = SELECT BUSINESS_ENTITY_STATUS FROM BUSINESS_ENTITY_HIERARCHY WHERE REGISTRATION_NUMBER = %1$s
getBusinessEntityId = SELECT BUSINESS_ENTITY_ID FROM BUSINESS_ENTITY_HIERARCHY WHERE REGISTRATION_NUMBER = %1$s