package com.cit.vericash.sme.service;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.registration.BusinessEntityManager;
import com.cit.mpaymentapp.common.registration.CustomerManager;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.businessentities.BusinessEntity;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.*;
import com.cit.shared.error.exception.CustomerException;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.shared.error.exception.IntegrationException;
import com.cit.shared.error.exception.SMEException;

import com.cit.vericash.jetty.JettyHttpClient;
import com.cit.vericash.sme.dto.BECustomer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Query;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import com.cit.vericash.backend.commons.dynamicpayload.Header;
import com.cit.vericash.backend.commons.dynamicpayload.Message;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.cit.vericash.backend.commons.dynamicpayload.AdditionalData;
@Transactional
@Service("BECustomerRegistrationManager")
public class BECustomerRegistrationManager extends VericashManager {

    private static final String NOTIFICATION_MESSAGE_EXISTING_CUSTOMER = "addBeCustomer-oldCustomer";
    private static final String NOTIFICATION_MESSAGE_NEW_CUSTOMER = "addBeCustomer-newCustomer";

    @Autowired
    CustomerManager customerManager;

    @Autowired
    BusinessEntityManager businessEntityManager;
    @Autowired
    NotificationHelper notificationHelper;
    @Autowired
    IBaseDao baseDao;
    @Autowired
    PropertyLoaderInt propertyLoader;
    public  void checkCustomerAlreadyExistService (BECustomer beCustomer) throws GeneralFailureException, IOException {
        String beMobileNumber = beCustomer.getMobileNumber();
        Customer customer = getCustomerByMobileNumber(beMobileNumber);
        if(customer!=null){
            boolean isBECustomer = (customer.getBECustomerId()!=null);
            businessMessage.getParameters().put("isCustomerExists",true);
            businessMessage.getParameters().put("customerData",customer);
            if(!isBECustomer){
                String beCustomerExistsMessage= propertyLoader.loadProperty("beCustomerExistsMessage");
                businessMessage.getParameters().put("isCustomerExists",true);
                businessMessage.getParameters().put("customerExists",beCustomerExistsMessage);
            }else{
                throw new SMEException(SMEException.CUSTOMER_IS_ALREADY_BECUSTOMRE);
            }
        }
        else{
            businessMessage.getParameters().put("isCustomerExists",false);
        }
        businessMessage.getParameters().remove("BECustomer");
    }
    public void sendActiviationCode (BECustomer beCustomer) throws Exception {
        String firstName = beCustomer.getFirstName();
        String mobileNumber = beCustomer.getMobileNumber();
        String apiCode = businessMessage.getApiCode();
        businessMessage.getParameters().put("otpApiCode",apiCode);
        BusinessEntity businessEntity = getBusinessEntity();
        beCustomer.setCorporateId(businessEntity.getBusinessEntityID());
        businessMessage.getParameters().put("businessEntityId",businessEntity.getBusinessEntityID());
        customerManager.sendOtpPerApiCode(businessMessage,mobileNumber,firstName);
//        prepareNotificationMessage(businessMessage,beCustomer);
    }

    private Customer getCustomerByMobileNumber(String beMobileNumber) throws CustomerException {
        Customer customer = customerManager.getCustomerByMsisdn(beMobileNumber);
        return customer;
    }
    public BusinessEntity getBusinessEntity()  {
        Long customerId = businessMessage.getHeader().getCustomerId();
        String query = ServiceQueryEngine.getQueryStringToExecute("getBusinessEntity", this.getClass(),
                String.valueOf(customerId));
        Long businessEntityId = null;
        String registrationNumber = null;
        Long beUserTypeId= null;
        Query queryResult = (Query) baseDao.executeNativeQuery(query);
        List list = queryResult.getResultList();
        if ((list != null) && (!list.isEmpty())) {
            Object[] result = (Object[]) list.get(0);
            businessEntityId = ((BigDecimal) result[0]).longValue();
            registrationNumber = result[1].toString();
            beUserTypeId = ((BigDecimal) result[2]).longValue();
        }
        BusinessEntity businessEntity = new BusinessEntity();
        businessEntity.setBusinessEntityID(businessEntityId);
        businessEntity.setRegistrationNumber(registrationNumber);
        businessMessage.getParameters().put("beUserTypeId",beUserTypeId);
        businessMessage.getParameters().put("registrationNumber",businessEntity.getRegistrationNumber());
        return businessEntity;
    }
    public void prepareNotificationMessage(BusinessMessage businessMessage , BECustomer beCustomer) throws GeneralFailureException, IOException {
        String mobileNumber = beCustomer.getMobileNumber();
        String [] notificationReceivers = {mobileNumber};
        String otp = businessMessage.getParameters().getAttributeAsString("otp");
        //TODO notification parameters what is app name
        businessMessage.getParameters().put("appName","adel rotating");
        businessMessage.getParameters().put("generatedActivationCode",otp);
        businessMessage.getParameters().put("corporateId","123456");
        Customer customer = customerManager.getCustomerByMsisdn(beCustomer.getMobileNumber());
        if(customer !=null)
             notificationHelper.prepareNotificationData(businessMessage,notificationReceivers,NOTIFICATION_MESSAGE_EXISTING_CUSTOMER);
        else
            notificationHelper.prepareNotificationData(businessMessage,notificationReceivers,NOTIFICATION_MESSAGE_NEW_CUSTOMER);
    }

    public BECustomer addBeCustomer(BECustomer beCustomer) throws Exception {
        Gson gson = new Gson();
        Message message = new Message();
        prepareMessage(message,beCustomer);
        String jsonMessage = gson.toJson(message);
        String response = "";
        String headers = "{\"Content-Type\":\"application/json\",\"Authorization\":\"Basic dXNlck5hbWU6cGFzc3dvcmQ=\"}";
        String url = propertyLoader.loadProperty("registerSubscriberPortalURL");
        ObjectMapper objectMapper = new ObjectMapper();
        JettyResponse jettyResponse = new JettyResponse();
                response = JettyHttpClient.post(url, jsonMessage, headers);
                if(!response.equals("")) {
                    try {
                        jettyResponse = objectMapper.readValue(response, JettyResponse.class);
                    } catch (Exception e) {
                        response = response.substring(1, response.length() - 1);
                        Long beCustomerId = Long.valueOf(response.split(",")[2]);
                        businessMessage.getParameters().put("beCustomerId", beCustomerId);
                        businessMessage.getParameters().put("BECustomer", beCustomer);
                        return beCustomer;
                    }
                    if (jettyResponse.get("status").equals(500)) {
                        String jettyMessage = jettyResponse.getAttributeAsString("message");
                        String jettyMessageSplited  = jettyMessage.split(":")[1];
                        if(jettyMessageSplited!=null && !jettyMessageSplited.equals( "null")){
                            throw new IntegrationException(jettyMessageSplited);
                        }
                        throw new IntegrationException(jettyMessage);
                    }
                }
                return beCustomer;
    }

    private void prepareMessage(Message message, BECustomer beCustomer) throws CustomerException {
        setHeader(message,beCustomer);
        setPayload(message,beCustomer);
        setAdditionalData(message,beCustomer);
    }
    private void setHeader(Message message, BECustomer beCustomer) throws CustomerException {
        Header header = new Header();
        addHeaderInfo(beCustomer, header);
        message.setHeader(header);
    }
    private void addHeaderInfo(BECustomer beCustomer, Header header) throws CustomerException {
        header.setAttribute("serviceCode",businessMessage.getServiceInfo().getCode());
        header.setAttribute("serviceName","subscriber-service");
        header.setAttribute("walletId", beCustomer.getWalletId());
        header.setAttribute("walletShortCode", beCustomer.getWalletShortCode());
        header.setAttribute("channel","web"); // ask
        header.setAttribute("project","vericash");
        header.setAttribute("databaseConnectionType","oracle");
        Long createdBy = businessMessage.getParameters().getAttributeAsLong("createdBy");
        header.setAttribute("userId", createdBy);
        header.setAttribute("userName", beCustomer.getFirstName()); // ask
        header.setAttribute("userFullName", beCustomer.getFirstName());
        BusinessEntity businessEntity = getBusinessEntity();
        header.setAttribute("businessEntityId",businessEntity.getBusinessEntityID());}
    private void setAdditionalData(Message message, BECustomer beCustomer) throws CustomerException {
        AdditionalData additionalData = new AdditionalData();
        setAdditionalDataInfo(beCustomer, additionalData);
        message.setAdditionalData(additionalData);
    }
    private void setAdditionalDataInfo(BECustomer beCustomer, AdditionalData additionalData) throws CustomerException {
        additionalData.setAttribute("firstName", beCustomer.getFirstName());
        additionalData.setAttribute("middleName", beCustomer.getMiddleName());
        additionalData.setAttribute("lastName", beCustomer.getLastName());
        additionalData.setAttribute("msisdn", beCustomer.getMobileNumber());
        additionalData.setAttribute("country",null);
        additionalData.setAttribute("role", beCustomer.getChooseRole());
        additionalData.setAttribute("superUser",false);
        BusinessEntity businessEntity = getBusinessEntity();
        additionalData.setAttribute("businessEntityId",businessEntity.getBusinessEntityID());
        additionalData.setAttribute("SMEBusinessEntityID",businessEntity.getBusinessEntityID());
        additionalData.setAttribute("customerProfileTypeId",null);
        additionalData.setAttribute("businessEntityRegistrationNumber",null);
        additionalData.setAttribute("gender",null);
        additionalData.setAttribute("country",null);
        additionalData.setAttribute("language",null);
        additionalData.setAttribute("corporateUser",true);
        Long  beUserTypeId = businessMessage.getParameters().getAttributeAsLong("beUserTypeId");
        additionalData.setAttribute("beUserTypeId",beUserTypeId);
    }
    private void setPayload(Message message, BECustomer beCustomer) {
        Payload payload = new Payload();
        payload.setAttribute("firstName", beCustomer.getFirstName());
        payload.setAttribute("middleName", beCustomer.getMiddleName());
        payload.setAttribute("lastName", beCustomer.getLastName());
        payload.setAttribute("msisdn", beCustomer.getMobileNumber());
        payload.setAttribute("country",null);
        payload.setAttribute("registrationNumber",null);
        payload.setAttribute("nationIdType",null);
        payload.setAttribute("email",beCustomer.getEmail());
        payload.setAttribute("gender",null);
        payload.setAttribute("secretAnswer",null);
        payload.setAttribute("preferredLanguage",null);
        payload.setAttribute("secretAnswer","E");
        payload.setAttribute("customerType",null);
        payload.setAttribute("secretQuestion",null);
        payload.setAttribute("riskProfile",null);
        payload.setAttribute("feeProfile",null);
        payload.setAttribute("userProfile",null);
        payload.setAttribute("customerProfile",null);
        payload.setAttribute("status",0);
        payload.setAttribute("customerAuthenticationType",null);
        payload.setAttribute("customerRegistrationType",null);
        BusinessEntity businessEntity = getBusinessEntity();
        payload.setAttribute("corporateRegistrationNumber",businessEntity.getRegistrationNumber());

        message.setPayload(payload);
    }
}
