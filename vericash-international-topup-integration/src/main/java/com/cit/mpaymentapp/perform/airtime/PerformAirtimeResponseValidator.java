package com.cit.mpaymentapp.perform.airtime;

import com.cit.mpaymentapp.airtime.PrimeTopupResponseValidator;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;


public class PerformAirtimeResponseValidator{

	public Object onCall(BusinessMessage businessMessage, PerformAirtimeResponse airtimeResponse) throws Exception {

		if(PrimeTopupResponseValidator.isSuccessTopup( airtimeResponse, businessMessage)){
			
			return businessMessage;
		}else{
			throw new GeneralFailureException();
		}
	}

}
