package com.cit.mpaymentapp.perform.airtime;

import com.cit.mpaymentapp.airtime.InternationalTopupUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;
import com.google.gson.Gson;

public class PerformAirtimeRequestGenerator {

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		PerformAirtimeRequest airtimeRequest = new PerformAirtimeRequest();
		airtimeRequest.setProduct_id(businessMessage.getITopup().getTopupProduct().getProduct_id());
		airtimeRequest.setDenomination(businessMessage.getTransactionInfo().getTransactionAmount());
		airtimeRequest.setCustomer_reference(businessMessage.getWalletInfo().getCountryIso3()
				+businessMessage.getTransactionInfo().getTransactionId().toString());
		Gson gson = new Gson();
		return gson.toJson(airtimeRequest);		
	}
	
	public String validateMsisdn(BusinessMessage businessMessage) throws GeneralFailureException {
		String msisdn = InternationalTopupUtil.validateMSISDN(businessMessage.getPrimaryReceiverInfo().getMsisdn(), businessMessage.getPrimaryReceiverInfo().getCountryIso2());
		return msisdn;
	}

}
