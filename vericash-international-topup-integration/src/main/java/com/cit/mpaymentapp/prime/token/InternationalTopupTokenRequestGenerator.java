package com.cit.mpaymentapp.prime.token;

import java.util.Properties;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.international.airtime.token.InternationalTopupTokenRequest;
import com.google.gson.Gson;

public class InternationalTopupTokenRequestGenerator {

	private Properties internationalTopupProperties;
	private final String USERNAME = "vericash.account.username";
	private final String PASSWORD = "vericash.account.password";

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		InternationalTopupTokenRequest tokenRequest = new InternationalTopupTokenRequest();
		tokenRequest.setUsername(internationalTopupProperties.getProperty(USERNAME));
		tokenRequest.setPassword(internationalTopupProperties.getProperty(PASSWORD));
		Gson gson = new Gson();
		return gson.toJson(tokenRequest);
	}

	public Properties getInternationalTopupProperties() {
		return internationalTopupProperties;
	}

	public void setInternationalTopupProperties(Properties internationalTopupProperties) {
		this.internationalTopupProperties = internationalTopupProperties;
	}

}
