package com.cit.mpaymentapp.prime.token;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.cit.mpaymentapp.international.airtime.token.InternationalTopupToken;
import com.cit.mpaymentapp.international.airtime.token.InternationalTopupTokenResponse;

public class InternationalTopupTokenResponseValidator {

	public Object onCall(InternationalTopupTokenResponse tokenResponse) throws Exception {

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

		Date expiryDate = null;
		try {
			expiryDate = sdf.parse(tokenResponse.getExpires());
		} catch (ParseException e) {
			e.printStackTrace();
		}

		InternationalTopupToken.setInternationalTopupToken(tokenResponse.getToken());
		InternationalTopupToken.setTokenExpiryDate(expiryDate);

		return tokenResponse;
	}

}
