package com.cit.mpaymentapp.get.operators;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.type.TypeFactory;

import com.cit.mpaymentapp.airtime.PrimeTopupResponseValidator;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.international.airtime.common.AbstractAirtimeResponse;
import com.cit.mpaymentapp.international.airtime.common.InternationalTopup;
import com.cit.mpaymentapp.international.airtime.common.OperatorInfo;

public class GetOperatorsResponseValidator {

	public Object onCall(BusinessMessage businessMessage, String responseString) throws Exception {

		ObjectMapper mapper = new ObjectMapper();
		ArrayList<OperatorInfo> operators = new ArrayList<OperatorInfo>();

		try {
			operators = mapper.readValue(responseString,
					TypeFactory.defaultInstance().constructCollectionType((Class<? extends Collection>) List.class, OperatorInfo.class));
			if (operators.size() == 0) {
				businessMessage.getStatus().setErrorFlag(true);
				businessMessage.getStatus().setStatusMsg("Invalid Country Code");
			} else {
				InternationalTopup internationalTopup = 
						businessMessage.getITopup() == null ? new InternationalTopup() : businessMessage.getITopup();
				internationalTopup.setOperatorsList(operators);
				businessMessage.setITopup(internationalTopup);
			}
		} catch (Exception e) {
			try {
				AbstractAirtimeResponse response =  mapper.readValue(responseString, AbstractAirtimeResponse.class);
				PrimeTopupResponseValidator.isValidResponse(response, businessMessage);
			} catch (Exception ex) {
				ex.printStackTrace();

			}
		}

		return businessMessage;
	}

}
