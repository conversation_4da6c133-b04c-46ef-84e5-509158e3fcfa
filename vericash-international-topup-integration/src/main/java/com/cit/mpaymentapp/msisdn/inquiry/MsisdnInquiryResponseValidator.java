package com.cit.mpaymentapp.msisdn.inquiry;

import java.util.ArrayList;

import com.cit.mpaymentapp.airtime.PrimeTopupResponseValidator;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.international.airtime.common.AbstractAirtimeResponse;
import com.cit.mpaymentapp.international.airtime.common.AirtimeProduct;
import com.cit.mpaymentapp.international.airtime.common.InternationalTopup;
import com.cit.mpaymentapp.international.airtime.common.MsisdnInquiryResult;
import com.cit.mpaymentapp.international.airtime.common.OperatorInfo;

public class MsisdnInquiryResponseValidator{

	public Object onCall(BusinessMessage businessMessage, MsisdnInquiryResponse msisdnInquiryResponse) throws Exception {

		if(PrimeTopupResponseValidator.isValidResponse((AbstractAirtimeResponse) msisdnInquiryResponse, businessMessage))
		{
			InternationalTopup internationalTopup = businessMessage.getITopup() == null ? new InternationalTopup() : businessMessage.getITopup() ;
			
			MsisdnInquiryResult inquiryResult = new MsisdnInquiryResult();
			inquiryResult.setOpts(msisdnInquiryResponse.getOpts());
			OperatorInfo operatorInfo = new OperatorInfo();
			operatorInfo.setCountry(msisdnInquiryResponse.getOpts().getCountry());
			operatorInfo.setOperator_name(msisdnInquiryResponse.getOpts().getOperator());
			operatorInfo.setOperator_id("1");
			operatorInfo.setIso(msisdnInquiryResponse.getOpts().getIso());
			operatorInfo.setProducts(new ArrayList<AirtimeProduct>());
			for(AirtimeProduct airtimeProduct :msisdnInquiryResponse.getProducts()){
				airtimeProduct.setProduct_type("1");
				System.out.println("setting product type");
			}
			
			operatorInfo.getProducts().addAll(msisdnInquiryResponse.getProducts());
			inquiryResult.getOperatorsList().add(operatorInfo);
			internationalTopup.setMsisdnInquiryResult(inquiryResult);
			
			businessMessage.setITopup(internationalTopup);
			
		}
		
		return businessMessage;
	}
	
	
}
