package com.cit.mpaymentapp.msisdn.inquiry;

import com.cit.mpaymentapp.airtime.InternationalTopupUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;

public class MsisdnInquiryRequestGenerator {

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		
		if(!businessMessage.getWalletInfo().getCountryIso2().equalsIgnoreCase(businessMessage.getPrimaryReceiverInfo().getCountryIso2())){
			throw new GeneralFailureException("VAL03017");
		}
		
		String msisdn = InternationalTopupUtil.validateMSISDN(businessMessage.getPrimaryReceiverInfo().getMsisdn(), businessMessage.getPrimaryReceiverInfo().getCountryIso2());
		
		businessMessage.getPrimaryReceiverInfo().setMsisdn(msisdn);
		return businessMessage;
	}
	
}