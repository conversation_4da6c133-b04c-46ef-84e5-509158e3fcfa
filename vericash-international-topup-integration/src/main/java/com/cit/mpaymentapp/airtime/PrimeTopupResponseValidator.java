package com.cit.mpaymentapp.airtime;

import java.util.Arrays;
import java.util.Properties;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.international.airtime.common.AbstractAirtimeResponse;
import com.cit.mpaymentapp.perform.airtime.PerformAirtimeResponse;

public final class PrimeTopupResponseValidator {

	private static final int SUCCESS = 200;
	private static final int SUCCESS_OBJECT_CREATED = 201;
	private static Properties internationalTopupProperties;
	private static final String PRIME_ERROR_CODES = "prime_error_codes";
	private static final String GENERAL_PRIME_ERROR = "503";
	private static final String ERROR_PRIFIX = "EXTP";

	public static final boolean isValidResponse(AbstractAirtimeResponse abstractAirtimeResponse,
			BusinessMessage businessMessage) {

		boolean isValid = true;
		if (abstractAirtimeResponse.getError() != null) {
			isValid = false;
			handleError(String.valueOf(abstractAirtimeResponse.getError().getStatus()), businessMessage);
		} else if (abstractAirtimeResponse.getStatus() != null) {
			if (!(abstractAirtimeResponse.getStatus() == (SUCCESS)
					|| abstractAirtimeResponse.getStatus() == (SUCCESS_OBJECT_CREATED))) {
				isValid = false;
				handleError(abstractAirtimeResponse.getStatus().toString(), businessMessage);
			}
		}

		return isValid;

	}

	public static boolean isSuccessTopup(PerformAirtimeResponse topupResponse, BusinessMessage businessMessage) {
		boolean isSuccess = false;

		if (topupResponse.getStatus() != null
				&& (topupResponse.getStatus() == (SUCCESS) || topupResponse.getStatus() == (SUCCESS_OBJECT_CREATED))) {
			return true;

		} else {
			if( topupResponse.getStatus()!=null){
				handleError( topupResponse.getStatus().toString(),  businessMessage);
			}else{
				handleError( ERROR_PRIFIX + GENERAL_PRIME_ERROR,  businessMessage);
			}
		}

		return isSuccess;
	}

	private static void handleError(String statusCode, BusinessMessage businessMessage) {
		
		if (Arrays.asList(internationalTopupProperties.getProperty(PRIME_ERROR_CODES).split(","))
				.contains(statusCode)) {
			statusCode = ERROR_PRIFIX + statusCode;
		} else {
			statusCode = ERROR_PRIFIX + GENERAL_PRIME_ERROR;
		}
		
		businessMessage.getStatus().setErrorFlag(true);
		businessMessage.getStatus().setStatusCode(statusCode);
	}

	public Properties getInternationalTopupProperties() {
		return internationalTopupProperties;
	}

	public void setInternationalTopupProperties(Properties internationalTopupProperties) {
		PrimeTopupResponseValidator.internationalTopupProperties = internationalTopupProperties;
	}

}