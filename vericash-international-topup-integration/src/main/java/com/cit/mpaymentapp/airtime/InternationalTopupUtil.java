package com.cit.mpaymentapp.airtime;

import com.cit.mpaymentapp.common.format.MsisdnFormatterImpl;
import com.cit.shared.error.exception.GeneralFailureException;

public class InternationalTopupUtil {
	
	public static String validateMSISDN(String msisdn,String countryIsoCode2) throws GeneralFailureException{
		MsisdnFormatterImpl msisdnFormatterImpl = MsisdnFormatterImpl.getInstance();
		msisdn = msisdnFormatterImpl.formatMSISDN(msisdn,countryIsoCode2.toUpperCase());
		msisdn = msisdn.replace("+", "");
		return msisdn;
	}

}
