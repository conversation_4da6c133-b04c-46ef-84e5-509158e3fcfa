package com.cit.mpayment.sochitel.integration;

public class ExecuteTransactionResult {
	
	private String id;
	private Country country;
	private UserVsOperator amount;
	private UserVsOperator currency;
	private TransactionOperator operator;
    private String msisdn;
    private String userReference;
    private boolean simulation;
    private String productType;
    private String productId;
	private Balance balance;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Country getCountry() {
		return country;
	}
	public void setCountry(Country country) {
		this.country = country;
	}
	public UserVsOperator getAmount() {
		return amount;
	}
	public void setAmount(UserVsOperator amount) {
		this.amount = amount;
	}
	public UserVsOperator getCurrency() {
		return currency;
	}
	public void setCurrency(UserVsOperator currency) {
		this.currency = currency;
	}
	public TransactionOperator getOperator() {
		return operator;
	}
	public void setOperator(TransactionOperator operator) {
		this.operator = operator;
	}
	public String getMsisdn() {
		return msisdn;
	}
	public void setMsisdn(String msisdn) {
		this.msisdn = msisdn;
	}
	public String getUserReference() {
		return userReference;
	}
	public void setUserReference(String userReference) {
		this.userReference = userReference;
	}
	public boolean isSimulation() {
		return simulation;
	}
	public void setSimulation(boolean simulation) {
		this.simulation = simulation;
	}
	public String getProductType() {
		return productType;
	}
	public void setProductType(String productType) {
		this.productType = productType;
	}
	public String getProductId() {
		return productId;
	}
	public void setProductId(String productId) {
		this.productId = productId;
	}
	public Balance getBalance() {
		return balance;
	}
	public void setBalance(Balance balance) {
		this.balance = balance;
	}
	@Override
	public String toString() {
		return "ExecuteTransactionResult [id=" + id + ", country=" + country + ", amount=" + amount + ", currency="
				+ currency + ", operator=" + operator + ", msisdn=" + msisdn + ", userReference=" + userReference
				+ ", simulation=" + simulation + ", productType=" + productType + ", productId=" + productId
				+ ", balance=" + balance + "]";
	}
	
}
