package com.cit.mpayment.sochitel.integration;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.google.gson.annotations.Expose;

@JsonAutoDetect
public class SochExecuteTransactionRequest extends RequestCommon {
	private static final long serialVersionUID = 1L;
	@Expose
	private String operator;
	@Expose
	private String transactionAmount;
	@Expose
	private String msisdn;
	@Expose
	private String productId;
	@Expose
	private String userReference;

	private String operator_name;

	private String transactionCurrency;

	private String accountNumber;

	private String narration;

	private String billerId;

	private String billerName;

	private Attributes attributes;

    private String sourceOfFund;
	
	private String rechargeType;
	
	private SochExecuteTransactionRequestPrepaidCard prepaidCard;
	
	public String getOperator_name() {
		return operator_name;
	}

	public SochExecuteTransactionRequestPrepaidCard getPrepaidCard() {
		return prepaidCard;
	}

	public void setPrepaidCard(SochExecuteTransactionRequestPrepaidCard prepaidCard) {
		this.prepaidCard = prepaidCard;
	}

	public void setOperator_name(String operator_name) {
		this.operator_name = operator_name;
	}

	public String getUserReference() {
		return userReference;
	}

	public void setUserReference(String userReference) {
		this.userReference = userReference;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(String transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	public String getMsisdn() {
		return msisdn;
	}

	public void setMsisdn(String msisdn) {
		this.msisdn = msisdn;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getTransactionCurrency() {
		return transactionCurrency;
	}

	public void setTransactionCurrency(String transactionCurrency) {
		this.transactionCurrency = transactionCurrency;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public String getNarration() {
		return narration;
	}

	public void setNarration(String narration) {
		this.narration = narration;
	}

	public String getBillerId() {
		return billerId;
	}

	public void setBillerId(String billerId) {
		this.billerId = billerId;
	}

	public String getBillerName() {
		return billerName;
	}

	public void setBillerName(String billerName) {
		this.billerName = billerName;
	}

	public Attributes getAttributes() {
		return attributes;
	}

	public void setAttributes(Attributes attributes) {
		this.attributes = attributes;
	}
	public String getSourceOfFund() {
		return sourceOfFund;
	}

	public void setSourceOfFund(String sourceOfFund) {
		this.sourceOfFund = sourceOfFund;
	}

	public String getRechargeType() {
		return rechargeType;
	}

	public void setRechargeType(String rechargeType) {
		this.rechargeType = rechargeType;
	}
}
