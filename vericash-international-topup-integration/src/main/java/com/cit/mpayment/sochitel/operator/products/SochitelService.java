package com.cit.mpayment.sochitel.operator.products;

import com.cit.mpayment.sochitel.integration.*;
import com.cit.mpayment.sochitel.util.StatusChecker;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.utility.general.lookups.component.GeneralLookupsComponent;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.impl.util.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Logger;

@Component
@RequiredArgsConstructor(onConstructor = @__({@Autowired, @Lazy}))
public class SochitelService{
	
    private final static Logger logger = Logger.getLogger(SochitelService.class.getName());
	private final StatusChecker statusChecker;
	private OperatorProductsRequest operatorProductsCacheReq=null;
	private final GeneralLookupsComponent generalLookupsComponent;
	
	public Object getOperatorProductsFromDB(BusinessMessage businessMessage){
		
		String operator = null;
		String country = null;
		OperatorProducts operatorProducts=null;
		SochOperatorProductsResponse operatorProductsResponse= null;
		
		if(businessMessage!=null && businessMessage.getITopup()!=null&& businessMessage.getPrimaryReceiverInfo()!=null) {
			 operator= businessMessage.getITopup().getOperator_id();
			 country= businessMessage.getPrimaryReceiverInfo().getCountryIso2();
		}
		String operatorRequestKey = country + "-" + operator;	
		if(operatorRequestKey!=null) {
			String countryOperatorVal= generalLookupsComponent.getValueByKey(operatorRequestKey);

				if(countryOperatorVal!=null) {
						 operatorProductsResponse =new Gson().fromJson(countryOperatorVal, SochOperatorProductsResponse.class);
				
				operatorProducts = getOperatorProductsList(operatorProductsResponse);
				operatorProducts.setOperator_id(operator);
				operatorProducts.setCountry(country);
				if (operatorProducts.getProducts().isEmpty())
					operatorProductsResponse.setStatusType(statusChecker.getOperatorErrorStatus());
				else {
					operatorProductsResponse.getOperatorsList().add(operatorProducts);	
					operatorProductsResponse.setStatusType(statusChecker.checkServiceStatus(operatorProductsResponse));	
				}
			}else {
				logger.info("###### CALLING ENDPOINT ########");
			}
		}
		if(operatorProductsResponse != null) {
			Gson gson = new Gson();
			return gson.toJson(operatorProductsResponse);			
		}
		return null;
	}
	
	public Object onCall(BusinessMessage businessMessage) throws Exception {

		String operator = null;
		String country = null;
	
		if(businessMessage!=null && businessMessage.getITopup()!=null&& businessMessage.getPrimaryReceiverInfo()!=null) {
			 operator= businessMessage.getITopup().getOperator_id();
			 country= businessMessage.getPrimaryReceiverInfo().getCountryIso2();
		}
		OperatorProductsRequest operatorProductsRequest= new OperatorProductsRequest();
		operatorProductsRequest.setCountry(country);
		operatorProductsRequest.setOperator(operator);
		
		this.operatorProductsCacheReq=operatorProductsRequest;
		Gson gson = new Gson();
		return gson.toJson(operatorProductsRequest);
	}	
		
	public String saveOperatorProductsEndpointResponse(String response) {

		if(response!=null) {
			String operator= null;
			String country=null;
			if(operatorProductsCacheReq!=null) {
				country= operatorProductsCacheReq.getCountry();
				operator= operatorProductsCacheReq.getOperator();
			}
			String operatorRequestKey = country + "-" + operator;
			generalLookupsComponent.saveKeyAndValue(operatorRequestKey, response);
		}else {
			logger.info("###### Empty Products wont be saved in DB #########");
		}
		return response;
	}
	
	public OperatorProducts getOperatorProductsList(SochOperatorProductsResponse operatorProductsResponse) {
		OperatorProducts operatorProducts = new OperatorProducts();
		

		logger.info("###### operatorProductsResponse.getResult().getProducts(): "+operatorProductsResponse.getResult().getProducts() +" #####");


		for (Entry<String, TopupProduct> entry : operatorProductsResponse.getResult().getProducts().entrySet()) {
			Product product = new Product();
			TopupProduct topupProduct = entry.getValue();
			product.setTopup_currency(operatorProductsResponse.getResult().getCurrency().getUser());
			product.setCurrency(operatorProductsResponse.getResult().getCurrency().getOperator());
			product.setProduct_id(topupProduct.getId());
			product.setProduc_name(topupProduct.getName());
			product.setProduct_type(topupProduct.getProductType().getId());
			if (topupProduct.getPriceType().equalsIgnoreCase("range")) {
				product.setOpenRange(true);
				product.setOpenRangeMin(topupProduct.getPrice().getMin().getUser());
				product.setOpenRangeMax(topupProduct.getPrice().getMax().getUser());
				product.setStep("1");

			} else {
				product.setOpenRange(false);
				product.setDenomination(new BigDecimal(topupProduct.getPrice().getUser()));
				product.setPrice(new BigDecimal(topupProduct.getPrice().getOperator()));
			}

			operatorProducts.getProducts().add(product);
		}
		return operatorProducts;
	}
	
	public SochOperatorProductsResponse convertResponseToSochResponse(String strResponse){
		
		JSONObject jo= new JSONObject(strResponse);
		JSONObject resultJsonObj= jo.getJSONObject("result");
		JSONObject productsJsonObj= resultJsonObj.getJSONObject("products");
		JSONObject typeJsonObj= resultJsonObj.getJSONObject("type");
		JSONObject statusJSONObject= jo.getJSONObject("status");
		
		UserVsOperator userVsOperator=new UserVsOperator();
		userVsOperator.setOperator(resultJsonObj.getJSONObject("currency").get("operator").toString());
		userVsOperator.setUser(resultJsonObj.getJSONObject("currency").get("user").toString());
		
		SochOperatorProductsResponse operatorProductsResponse = new SochOperatorProductsResponse();
		operatorProductsResponse.setResult(new SochOperatorProductsRequest());
		
		operatorProductsResponse.getResult().setType(new ProductType());
		operatorProductsResponse.getResult().setCurrency(userVsOperator);
		operatorProductsResponse.getResult().getType().setId(typeJsonObj.getString("id"));
		operatorProductsResponse.getResult().getType().setName(typeJsonObj.getString("name"));
		
		operatorProductsResponse.setStatus(new Status());
		if(statusJSONObject!=null) {
			operatorProductsResponse.getStatus().setId(statusJSONObject.getInt("id"));
			operatorProductsResponse.getStatus().setName(statusJSONObject.getString("name"));
			operatorProductsResponse.getStatus().setType(statusJSONObject.getInt("type"));
			operatorProductsResponse.getStatus().setTypeName(statusJSONObject.getString("typeName"));
		}

		HashMap<String, TopupProduct> productsMap = new Gson().fromJson(productsJsonObj.toString(), new TypeToken<Map<String, TopupProduct>>() {}.getType());

		operatorProductsResponse.getResult().setProducts(productsMap);

		OperatorProducts operatorProducts = getOperatorProductsList(operatorProductsResponse);
		
		if(operatorProductsCacheReq!=null) {
			operatorProducts.setOperator_id(operatorProductsCacheReq.getOperator());
			operatorProducts.setCountry(operatorProductsCacheReq.getCountry());
		}
		if (operatorProducts.getProducts().isEmpty())
			operatorProductsResponse.setStatusType(statusChecker.getOperatorErrorStatus());
		else {
			operatorProductsResponse.getOperatorsList().add(operatorProducts);	
			operatorProductsResponse.setStatusType(statusChecker.checkServiceStatus(operatorProductsResponse));	
		}
		return operatorProductsResponse;
	}

	public Object saveAndConvertResponseToSoch(String endpointResponse) {
		
		SochOperatorProductsResponse operatorProductsResponse= new SochOperatorProductsResponse();
		
		CommonSochResponse sochResponse= new Gson().fromJson(endpointResponse, CommonSochResponse.class);
		if(sochResponse.getStatus()!=null && "Successful".equalsIgnoreCase(sochResponse.getStatus().getName()) && 0 == sochResponse.getStatus().getId()) {
			String res= saveOperatorProductsEndpointResponse(endpointResponse);
			 operatorProductsResponse= convertResponseToSochResponse(res);
		}
		return new Gson().toJson(operatorProductsResponse);
	}
}
