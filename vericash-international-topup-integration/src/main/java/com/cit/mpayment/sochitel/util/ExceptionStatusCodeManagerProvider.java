package com.cit.mpayment.sochitel.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.cit.mpayment.sochitel.integration.StatusCode;

public class ExceptionStatusCodeManagerProvider {
	
	private ResourceLoader resourceLoader = new DefaultResourceLoader();
	public final static String GENERAL_FAILURE_ERROR = "EXT003";
	static public Map<String, StatusCode> serviceCodeMap = new HashMap<String, StatusCode>();

	
	public void readStatusCodes() throws IOException  {
		
		Resource[] files = loadResources("classpath:flows/sochitel/sochitel_statusCode.xml");
		
		InputStream is = readFile(files[0]);
		
		DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
		DocumentBuilder dBuilder;
		try {
			dBuilder = dbFactory.newDocumentBuilder();
			Document doc = dBuilder.parse(is);
			doc.getDocumentElement().normalize();
			System.out.println("Root element :" + doc.getDocumentElement().getNodeName());
			NodeList nodeList = doc.getElementsByTagName("ExtrenalVcCodeVcCode");

			for (int i = 0; i < nodeList.getLength(); i++) {
				StatusCode statusCode = getErrorclass(nodeList.item(i));
				System.out.println("element " + i + statusCode.toString());
				serviceCodeMap.put(statusCode.getExtrenalCode(), statusCode);

			}

		} catch (Exception e1) {
			e1.printStackTrace();
		}

	}

	private static StatusCode getErrorclass(Node node) {

		StatusCode sce = new StatusCode();
		if (node.getNodeType() == Node.ELEMENT_NODE) {

			Element element = (Element) node;
			sce.setCode(getTagValue("VcCode", element));
			sce.setDescription(getTagValue("Description", element));
			sce.setExtrenalCode(getTagValue("ExtrenalCode", element));

		}

		return sce;
	}

	private static String getTagValue(String tag, Element statusCode) {
		NodeList nodeList = statusCode.getElementsByTagName(tag).item(0).getChildNodes();
		Node node = (Node) nodeList.item(0);
		return node.getNodeValue();

	}

	public static String getErrorVcCode(String errorCode) {
		if(serviceCodeMap.containsKey(errorCode)){
			String vcCode = serviceCodeMap.get(errorCode).getCode();
			if (vcCode != null && !vcCode.isEmpty()) {
				return vcCode;
			}
		}
		return GENERAL_FAILURE_ERROR;
	}
	
	private InputStream readFile(Resource migrationFile) throws IOException, UnsupportedEncodingException {
		InputStream stream = migrationFile.getInputStream();
		return stream;
	}

	Resource[] loadResources(String pattern) throws IOException {
		return ResourcePatternUtils.getResourcePatternResolver(resourceLoader).getResources(pattern);
	}

}