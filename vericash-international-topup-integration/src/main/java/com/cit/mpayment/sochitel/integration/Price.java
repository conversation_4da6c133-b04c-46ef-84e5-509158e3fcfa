package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;

public class Price  implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String operator;
	private String user;
	private UserVsOperator min;
	private UserVsOperator max;
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getUser() {
		return user;
	}
	public void setUser(String user) {
		this.user = user;
	}
	public UserVsOperator getMin() {
		return min;
	}
	public void setMin(UserVsOperator min) {
		this.min = min;
	}
	public UserVsOperator getMax() {
		return max;
	}
	public void setMax(UserVsOperator max) {
		this.max = max;
	}
	
	@Override
	public String toString() {
		return "Price [operator=" + operator + ", user=" + user + ", min=" + min + ", max=" + max + "]";
	}
	
}
