package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonIgnoreProperties;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

import com.cit.mpaymentapp.common.message.StatusType;

@SuppressWarnings("deprecation")
@JsonSerialize(include=Inclusion.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonSochResponse implements Serializable{

	private static final long serialVersionUID = 1L;
	private Status status;
	private String command;
	private Long timestamp;
	private String reference;
	private StatusType statusType;

	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	public String getCommand() {
		return command;
	}

	public void setCommand(String command) {
		this.command = command;
	}

	public Long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public String getReference() {
		return reference;
	}

	public void setReference(String reference) {
		this.reference = reference;
	}

	public StatusType getStatusType() {
		return statusType;
	}

	public void setStatusType(StatusType statusType) {
		this.statusType = statusType;
	}

	@Override
	public String toString() {
		return "CommonSochResponse [status=" + status + ", command=" + command + ", timestamp=" + timestamp
				+ ", reference=" + reference + ", statusType=" + statusType + "]";
	}

}
