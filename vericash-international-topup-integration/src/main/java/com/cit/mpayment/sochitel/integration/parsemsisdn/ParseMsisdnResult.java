package com.cit.mpayment.sochitel.integration.parsemsisdn;

import java.io.Serializable;

import com.cit.mpayment.sochitel.integration.Country;
import com.cit.mpayment.sochitel.integration.Operator;

public class ParseMsisdnResult implements Serializable{
	private static final long serialVersionUID = 1L;
	private String original;
	private String normalized;
	private boolean isValid;
	private boolean isFormallyValid;
	private Country country;
	private Operator operator;

	public String getOriginal() {
		return original;
	}

	public void setOriginal(String original) {
		this.original = original;
	}

	public String getNormalized() {
		return normalized;
	}

	public void setNormalized(String normalized) {
		this.normalized = normalized;
	}

	public boolean isValid() {
		return isValid;
	}

	public void setValid(boolean isValid) {
		this.isValid = isValid;
	}

	public boolean isFormallyValid() {
		return isFormallyValid;
	}

	public void setFormallyValid(boolean isFormallyValid) {
		this.isFormallyValid = isFormallyValid;
	}

	public Country getCountry() {
		return country;
	}

	public void setCountry(Country country) {
		this.country = country;
	}

	public Operator getOperator() {
		return operator;
	}

	public void setOperator(Operator operator) {
		this.operator = operator;
	}

	@Override
	public String toString() {
		return "ParseMsisdnResult [original=" + original + ", normalized=" + normalized + ", isValid=" + isValid
				+ ", isFormallyValid=" + isFormallyValid + ", country=" + country + ", operator=" + operator + "]";
	}

}
