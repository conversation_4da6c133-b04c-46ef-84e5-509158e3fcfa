package com.cit.mpayment.sochitel.integration;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class SochExecuteTransactionRequestPrepaidCard extends RequestCommon {
	private static final long serialVersionUID = 1L;
	private String type;
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getFirst6() {
		return first6;
	}
	public void setFirst6(String first6) {
		this.first6 = first6;
	}
	public String getLast4() {
		return last4;
	}
	public void setLast4(String last4) {
		this.last4 = last4;
	}
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	private String first6;
	private String last4;
	private String clientId;
}
