package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;

public class TopupProduct implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String id;
	private ProductType productType;
	private String priceType;
	private String name;
	private Price price;
	private String topup_currency;
	private String currency;
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public ProductType getProductType() {
		return productType;
	}
	public void setProductType(ProductType productType) {
		this.productType = productType;
	}
	public String getPriceType() {
		return priceType;
	}
	public void setPriceType(String priceType) {
		this.priceType = priceType;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Price getPrice() {
		return price;
	}
	public void setPrice(Price price) {
		this.price = price;
	}
	public String getTopup_currency() {
		return topup_currency;
	}
	public void setTopup_currency(String topup_currency) {
		this.topup_currency = topup_currency;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	@Override
	public String toString() {
		return "TopupProduct [id=" + id + ", productType=" + productType + ", priceType=" + priceType + ", name=" + name
				+ ", price=" + price + ", topup_currency=" + topup_currency + ", currency=" + currency + "]";
	}
	
}
