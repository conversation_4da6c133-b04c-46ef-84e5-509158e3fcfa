package com.cit.mpayment.sochitel.integration.parsemsisdn;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpayment.sochitel.integration.CommonSochResponse;
import com.cit.mpayment.sochitel.operator.products.OperatorProducts;

public class SochParseMsisdnResponse extends CommonSochResponse{
	
	private static final long serialVersionUID = 1L;
	private ParseMsisdnResult result;
	private List<OperatorProducts> operatorsList = new ArrayList<OperatorProducts>();
	
	public ParseMsisdnResult getResult() {
		return result;
	}
	public void setResult(ParseMsisdnResult result) {
		this.result = result;
	}
	
	public void setResult(List<String> result) {
		this.result = null;
	}
	
	public List<OperatorProducts> getOperatorsList() {
		return operatorsList;
	}
	public void setOperatorsList(List<OperatorProducts> operatorsList) {
		this.operatorsList = operatorsList;
	}
	@Override
	public String toString() {
		return "SochParseMsisdnResponse [result=" + result + ", operatorsList=" + operatorsList + "]";
	}
}
