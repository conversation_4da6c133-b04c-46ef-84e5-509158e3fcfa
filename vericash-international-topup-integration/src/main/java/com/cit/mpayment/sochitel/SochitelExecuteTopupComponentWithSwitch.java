package com.cit.mpayment.sochitel;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Properties;
import java.util.Random;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;

import com.cit.mpayment.sochitel.integration.Attributes;
import com.cit.mpayment.sochitel.integration.CommonSochResponse;
import com.cit.mpayment.sochitel.integration.SochExecuteTransactionRequest;
import com.cit.mpayment.sochitel.integration.SochExecuteTransactionRequestPrepaidCard;
import com.cit.mpayment.sochitel.util.StatusChecker;
import com.cit.mpaymentapp.common.format.MsisdnFormatterImpl;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.mpaymentapp.common.narration.NarrationBuilder;
import com.cit.mpaymentapp.common.narration.NarrationBuilderProvider;
import com.cit.shared.error.exception.GeneralFailureException;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor(onConstructor = @__({@Autowired, @Lazy}))
public class SochitelExecuteTopupComponentWithSwitch{
	protected static final String PROPERTY_FILE="corebanking/corebanking-app.properties";
	protected static Properties properties;
	private final StatusChecker statusChecker;
	public NarrationBuilderProvider getNarrationBuilderProvider() {
		return narrationBuilderProvider;
	}


	public void setNarrationBuilderProvider(NarrationBuilderProvider narrationBuilderProvider) {
		this.narrationBuilderProvider = narrationBuilderProvider;
	}

	private NarrationBuilderProvider narrationBuilderProvider;
	Gson gson = new Gson();
	DecimalFormat numberFormat = new DecimalFormat(".00");
	
	public String generateRequest(BusinessMessage message) throws GeneralFailureException {
		SochExecuteTransactionRequest request = new SochExecuteTransactionRequest();	
		MsisdnFormatterImpl msisdnFormatterImpl = MsisdnFormatterImpl.getInstance();
		String receiverMsisdn= message.getPrimaryReceiverInfo().getMsisdn().trim();
 		String countryIso2= message.getPrimaryReceiverInfo().getCountryIso2();
 		String formattedMsisdn= msisdnFormatterImpl.formatMSISDN(receiverMsisdn, countryIso2);
		request.setCountry(countryIso2);
		request.setOperator(message.getITopup().getOperator_id());
		request.setTransactionAmount(numberFormat.format(new BigDecimal(message.getTransactionInfo().getTransactionAmount().toString())));
		request.setMsisdn(formattedMsisdn.replace("+", ""));
		request.setProductId(message.getITopup().getTopupProduct().getProduct_id());	 
		request.setTransactionCurrency(message.getWalletInfo().getCurrency());
		request.setBillerId(message.getITopup().getOperator_id());
		request.setBillerName(message.getITopup().getTopupProduct().getOperator_name());
		Attributes attr = new Attributes();
		attr.setcF1(formattedMsisdn.replace("+", ""));		 
		request.setAttributes(attr);
		request.setUserReference(getUserRefernce());
		if (message.getPrimarySenderInfo().getPaymentMethod().getPaymentMethodType() == 5) {
			SochExecuteTransactionRequestPrepaidCard prepaidCard =  new SochExecuteTransactionRequestPrepaidCard();
			request.setAccountNumber("");
			request.setSourceOfFund("prepaidCard");
			String clientId = message.getPrimarySenderInfo().getPaymentMethod().getCard().getClientId();
			prepaidCard.setClientId(clientId);
			prepaidCard.setType("visacard");
			String first6 = clientId.substring(0,6); 
			String garShortCode = message.getPrimarySenderInfo().getPaymentMethod().getGarShortCode();
			String last4 = clientId.substring(clientId.length()-4,clientId.length());
			prepaidCard.setFirst6(first6);
			if (garShortCode != null && garShortCode.length() > 0) {
				String[] gar = garShortCode.split("_");
				last4 = gar[1];
			}		
			prepaidCard.setFirst6(first6);
			prepaidCard.setLast4(last4);
			request.setPrepaidCard(prepaidCard);
			message.getSoftFields().put("Source", clientId);
		} else if (message.getPrimarySenderInfo().getPaymentMethod().getPaymentMethodType() == 0) {
			request.setSourceOfFund("account");
			request.setAccountNumber(message.getPrimarySenderInfo().getPaymentMethod().getBank().getAccountNumber());
			message.getSoftFields().put("Source", message.getPrimarySenderInfo().getPaymentMethod().getBank().getAccountNumber());
		}
		if ("2".equals(message.getITopup().getTopupProduct().getProduct_type())
		   || "4".equals(message.getITopup().getTopupProduct().getProduct_type())) {
			request.setRechargeType("Data");
		} else {
			request.setRechargeType("Airtime");
		}
		String userDefinedNarration = null;
		if(message.getPrimarySenderInfo().getPersonalDetails().getComment() != null){
			userDefinedNarration = message.getPrimarySenderInfo().getPersonalDetails().getComment();
			
		}
		else if(!StringUtils.isEmpty(((String)message.getSoftFields().get("Comment")))){
			userDefinedNarration = ((String)message.getSoftFields().get("Comment")).replace("\n", " ");		
		}
		
		String transactionType = getPropertyFile().getProperty(message.getServiceInfo().getCode());
		NarrationBuilder narrationBuilder = narrationBuilderProvider.getBuilder(transactionType);
		String narration = narrationBuilder.validateAndBuildNarration(message, getPropertyFile(), userDefinedNarration);
		request.setNarration(narration);
		// Solve Transaction Detail Issue which Root cause is History with null narration because Receipt saved while Narration soft filed has no value
		message.getSoftFields().put("Transaction_Naration",narration);
		return new Gson().toJson(request);
	}


	public Object onCall(BusinessMessage message, String response) throws Exception {

		CommonSochResponse commonSochResponse = new CommonSochResponse();
		try {
			 commonSochResponse = gson.fromJson(response, CommonSochResponse.class); 			
		}catch(Exception e){
			e.printStackTrace();
			message.getStatus().setStatusCode("EXT00001");
		}
		
		boolean isSuccess = statusChecker.isSuccessTransaction(commonSochResponse);

		if (!isSuccess) {
			StatusType status = statusChecker.checkServiceStatus(commonSochResponse);
			message.getStatus().setStatusCode(status.getStatusCode());
			if(status.getErrorFlag()) {
				throw new GeneralFailureException(status.getStatusCode());
			}
		}
		
		return message;
	}

	protected static Properties getPropertyFile()
	 {
		if(properties == null){
			InputStream inputStream = SochitelExecuteTopupComponent.class.getClassLoader()
	        .getResourceAsStream(PROPERTY_FILE);
			properties = new Properties();

			try {
				properties.load(inputStream);
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return properties;		
	}
	private String getUserRefernce(){
		Date date = new Date();
		Calendar rightNowDate = Calendar.getInstance();
		SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyHHmmss");
		String strDate = sdf.format(rightNowDate.getTime());
		long first12digit = generateRandom(12);
		String userRefernce = strDate + first12digit;
		System.out.println("userRefernce is : " + userRefernce);
		return userRefernce;
	}
	
	public static long generateRandom(int length) {
	    Random random = new Random();
	    char[] digits = new char[length];
	    digits[0] = (char) (random.nextInt(9) + '1');
	    for (int i = 1; i < length; i++) {
	        digits[i] = (char) (random.nextInt(10) + '0');
	    }
	    return Long.parseLong(new String(digits));
	}
}
	