package com.cit.mpayment.sochitel.integration;

public class ResponseRepresentation {

	String  transactionid;
	String status;
	String errorcode ;
	String description;
	public String getTransactionid() {
		return transactionid;
	}
	public void setTransactionid(String transactionid) {
		this.transactionid = transactionid;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getErrorcode() {
		return errorcode;
	}
	public void setErrorcode(String errorcode) {
		this.errorcode = errorcode;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public ResponseRepresentation(String transactionid, String status, String errorcode, String description) {
		super();
		this.transactionid = transactionid;
		this.status = status;
		this.errorcode = errorcode;
		this.description = description;
	}
	@Override
	public String toString() {
		return "ResponseRepresentation [transactionid=" + transactionid + ", status=" + status + ", errorcode="
				+ errorcode + ", description=" + description + "]";
	}
	
	
	
	
	
	
}
