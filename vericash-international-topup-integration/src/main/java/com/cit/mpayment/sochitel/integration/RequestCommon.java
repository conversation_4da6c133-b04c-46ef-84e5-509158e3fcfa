package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;

import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

import com.google.gson.annotations.Expose;

@JsonSerialize(include=Inclusion.NON_NULL)
public class RequestCommon implements Serializable{
	private static final long serialVersionUID = 1L;
	private Authentication auth;
	private Integer version;
	private String command;
	@Expose
	private String country;
	
	public Authentication getAuth() {
		return auth;
	}
	public Integer getVersion() {
		return version;
	}
	public void setVersion(Integer version) {
		this.version = version;
	}
	public String getCommand() {
		return command;
	}
	public void setCommand(String command) {
		this.command = command;
	}
	public void setAuth(Authentication auth) {
		this.auth = auth;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	
}
