package com.cit.mpayment.sochitel;

import java.math.BigDecimal;
import java.text.DecimalFormat;

import com.cit.mpayment.sochitel.integration.CommonSochResponse;
import com.cit.mpayment.sochitel.integration.SochExecuteTransactionRequest;
import com.cit.mpayment.sochitel.util.StatusChecker;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.shared.error.exception.GeneralFailureException;
import com.google.gson.Gson;

public class SochitelExecuteTopupComponent{
	private StatusChecker statusChecker;
	Gson gson = new Gson();
	DecimalFormat numberFormat = new DecimalFormat(".00");
	
	public String generateRequest(BusinessMessage message) {
		SochExecuteTransactionRequest request = new SochExecuteTransactionRequest();
		request.setCountry(message.getPrimaryReceiverInfo().getCountryIso2());
		request.setOperator(message.getITopup().getOperator_id());
		request.setTransactionAmount(numberFormat.format(new BigDecimal(message.getTransactionInfo().getTransactionAmount().toString())));
		request.setMsisdn(message.getPrimaryReceiverInfo().getMsisdn().replace("+", ""));
		request.setProductId(message.getITopup().getTopupProduct().getProduct_id());
		request.setUserReference(message.getTransactionInfo().getTransactionId().toString());
		
		
		return new Gson().toJson(request);
	}


	public Object onCall(BusinessMessage message, String response) throws Exception {

		CommonSochResponse commonSochResponse = new CommonSochResponse();
		try {
			 commonSochResponse = gson.fromJson(response, CommonSochResponse.class); 			
		}catch(Exception e){
			e.printStackTrace();
			message.getStatus().setStatusCode("EXT00001");
		}
		
		boolean isSuccess = statusChecker.isSuccessTransaction(commonSochResponse);

		if (!isSuccess) {
			StatusType status = statusChecker.checkServiceStatus(commonSochResponse);
			message.getStatus().setStatusCode(status.getStatusCode());
			if(status.getErrorFlag()) {
				throw new GeneralFailureException(status.getStatusCode());
			}
		}
		
		return message;
	}
	
	public StatusChecker getStatusChecker() {
		return statusChecker;
	}

	public void setStatusChecker(StatusChecker statusChecker) {
		this.statusChecker = statusChecker;
	}

}
