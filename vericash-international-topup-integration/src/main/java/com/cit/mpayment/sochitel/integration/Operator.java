package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;
import java.util.Map;

public class Operator implements Serializable{
	private static final long serialVersionUID = 1L;
	private String id;
	private String name;
	private String brandId;
	private String fullprefix;
	private String prefix;
	private String number;
	private String prefixnumber;
	private int confidence;
	private Map<String, OperatorAlternative> alt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getBrandId() {
		return brandId;
	}

	public void setBrandId(String brandId) {
		this.brandId = brandId;
	}

	public String getFullprefix() {
		return fullprefix;
	}

	public void setFullprefix(String fullprefix) {
		this.fullprefix = fullprefix;
	}

	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getPrefixnumber() {
		return prefixnumber;
	}

	public void setPrefixnumber(String prefixnumber) {
		this.prefixnumber = prefixnumber;
	}

	public int getConfidence() {
		return confidence;
	}

	public void setConfidence(int confidence) {
		this.confidence = confidence;
	}

	public Map<String, OperatorAlternative> getAlt() {
		return alt;
	}

	public void setAlt(Map<String, OperatorAlternative> alt) {
		this.alt = alt;
	}
}
