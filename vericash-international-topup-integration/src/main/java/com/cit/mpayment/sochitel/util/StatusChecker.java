package com.cit.mpayment.sochitel.util;

import com.cit.mpayment.sochitel.integration.CommonSochResponse;
import com.cit.mpaymentapp.common.message.StatusType;


public class StatusChecker {

	public StatusType checkServiceStatus(CommonSochResponse commonSochResponse) {
		StatusType status = new StatusType();
		try {
			if (commonSochResponse == null || commonSochResponse.getStatus() == null) {
				status = getPendingStatus();
				return status;
			} else if (commonSochResponse.getStatus() != null && commonSochResponse.getStatus().getType() == 0
					&& commonSochResponse.getStatus().getId() == 0
					&& commonSochResponse.getStatus().getTypeName().equalsIgnoreCase("success")
					&& commonSochResponse.getStatus().getName().equalsIgnoreCase("Successful")) {
				status.setErrorFlag(false);
				return status;

			} else if (commonSochResponse.getStatus() != null && commonSochResponse.getStatus().getType() == 2) {
				status.setErrorFlag(true);
				status.setStatusCode(ExceptionStatusCodeManagerProvider
						.getErrorVcCode(String.valueOf(commonSochResponse.getStatus().getId())));
				return status;
			} else if (commonSochResponse.getStatus() != null && commonSochResponse.getStatus().getType() == 1) {
				status.setErrorFlag(false);
				status.setStatusCode(ExceptionStatusCodeManagerProvider
						.getErrorVcCode(String.valueOf(commonSochResponse.getStatus().getId())));
				return status;
			} else {
				status.setErrorFlag(true);
				status.setStatusCode(ExceptionStatusCodeManagerProvider.GENERAL_FAILURE_ERROR);
				return status;
			}
		} catch (Exception e) {
			return getPendingStatus();
		}

	}

	public boolean isSuccessTransaction(CommonSochResponse commonSochResponse)
	{
		if (commonSochResponse!=null && commonSochResponse.getStatus() != null && commonSochResponse.getStatus().getType() == 0
				&& commonSochResponse.getStatus().getId() == 0
				&& commonSochResponse.getStatus().getTypeName().equalsIgnoreCase("success")
				&& commonSochResponse.getStatus().getName().equalsIgnoreCase("Successful")) {
			return true;
		}
		return false;
	}
	public StatusType getTimeoutStatus() {
		StatusType statusType = new StatusType();
		statusType.setErrorFlag(true);
		statusType.setStatusCode("EXT00101");
		statusType.setStatusMsg("Time Out");
		return statusType;
	}

	public StatusType getPendingStatus() {
		StatusType statusType = new StatusType();
		statusType.setErrorFlag(false);
		statusType.setStatusCode("EXT00001");
		statusType.setStatusMsg("Pending");
		return statusType;
	}
	
	public StatusType getOperatorErrorStatus() {
		StatusType statusType = new StatusType();
		statusType.setErrorFlag(true);
		statusType.setStatusCode("EXT002");
		statusType.setStatusMsg("Operator Error");
		return statusType;
	}
}
