package com.cit.mpayment.sochitel.operator.products;

import java.util.List;
import java.util.logging.Logger;

import com.cit.mpayment.sochitel.integration.SochOperatorProductsResponse;
import com.cit.mpayment.sochitel.util.SochitelUtility;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.international.airtime.common.MsisdnInquiryResult;
import com.cit.mpaymentapp.international.airtime.common.OperatorInfo;
import com.google.gson.Gson;

public class OperatorProductsResponseValidator{

	 private final static Logger logger = Logger.getLogger(OperatorProductsResponseValidator.class.getName());
	 SochitelUtility sochitelUtility;
	  
	public Object onCall(BusinessMessage businessMessage, String responseString) throws Exception {

		logger.info("################ GetProducts Response ################"+responseString);

		if(responseString!=null) {
			SochOperatorProductsResponse productsResponse = new Gson().fromJson(responseString, SochOperatorProductsResponse.class);	
			
			businessMessage.getSoftFields().put("sochProductsResponse", productsResponse);
			if(productsResponse!=null) {
				List<OperatorInfo> operatorInfoList= sochitelUtility.convertOperatorProductToOperatorInfo(productsResponse.getOperatorsList());
				if(businessMessage.getITopup().getMsisdnInquiryResult()==null)
					businessMessage.getITopup().setMsisdnInquiryResult(new MsisdnInquiryResult());			
				businessMessage.getITopup().getMsisdnInquiryResult().setOperatorsList(operatorInfoList);
			}
		}
		return businessMessage;
	}	

	public SochitelUtility getSochitelUtility() {
		return sochitelUtility;
	}

	public void setSochitelUtility(SochitelUtility sochitelUtility) {
		this.sochitelUtility = sochitelUtility;
	}
	
}