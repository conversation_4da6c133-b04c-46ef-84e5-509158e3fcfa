package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;

public class UserVsOperator  implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String user;
	private String operator;
	
	public String getUser() {
		return user;
	}
	public void setUser(String user) {
		this.user = user;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	@Override
	public String toString() {
		return "Currency [user=" + user + ", operator=" + operator + "]";
	}
	
}
