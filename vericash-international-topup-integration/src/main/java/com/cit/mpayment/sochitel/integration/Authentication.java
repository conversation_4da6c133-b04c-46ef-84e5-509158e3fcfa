package com.cit.mpayment.sochitel.integration;

import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

@JsonSerialize(include= Inclusion.NON_NULL)
public class Authentication {
	
	private final String username=null;
	private final String salt=null;
	private final String password=null;
	
	public String getUsername() {
		return username;
	}
	public String getSalt() {
		return salt;
	}
	public String getPassword() {
		return password;
	}
	
}
