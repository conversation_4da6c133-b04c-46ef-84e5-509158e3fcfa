package com.cit.mpayment.sochitel.integration;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpayment.sochitel.operator.products.OperatorProducts;

public class SochOperatorProductsResponse extends CommonSochResponse{
	
	private static final long serialVersionUID = 1L;
	private SochOperatorProductsRequest result;
	private List<OperatorProducts> operatorsList = new ArrayList<OperatorProducts>();

	public SochOperatorProductsRequest getResult() {
		return result;
	}

	public void setResult(SochOperatorProductsRequest result) {
		this.result = result;
	}
	
	public void setResult(List<String> result) {
		this.result = null;
	}

	public List<OperatorProducts> getOperatorsList() {
		return operatorsList;
	}

	public void setOperatorsList(List<OperatorProducts> operatorsList) {
		this.operatorsList = operatorsList;
	}

	@Override
	public String toString() {
		return "SochOperatorProductsResponse [result=" + result + ", operatorsList=" + operatorsList + "]";
	}

}
