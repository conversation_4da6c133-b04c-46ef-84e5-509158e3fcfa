package com.cit.mpayment.sochitel.integration.parsemsisdn;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cit.mpayment.sochitel.integration.SochOperatorProductsResponse;
import com.cit.mpayment.sochitel.operator.products.OperatorProductsResponseValidator;
import com.cit.mpayment.sochitel.operator.products.SochitelService;
import com.cit.mpayment.sochitel.util.SochitelUtility;
import com.cit.mpayment.sochitel.util.StatusChecker;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.utility.general.lookups.component.GeneralLookupsComponent;
import com.cit.mpaymentapp.international.airtime.common.AirtimeOptions;
import com.cit.mpaymentapp.international.airtime.common.InternationalTopup;
import com.cit.mpaymentapp.international.airtime.common.MsisdnInquiryResult;
import com.cit.mpaymentapp.international.airtime.common.OperatorInfo;
import com.google.gson.Gson;

public class ParseMsisdnService{
	
	StatusChecker statusChecker;
	SochitelService sochitelService;
	SochitelUtility sochitelUtility;	
	OperatorProductsResponseValidator operatorProductsResponseValidator;
	private final static String Temp_Parse_Msisdn_Response="tempParseMsisdnResponse";
	Properties prefixProperty;
	private static Map<Object, Object> saveOperatorsCacheMapFile;
	private final Logger logger = LoggerFactory.getLogger(ParseMsisdnService.class);
	GeneralLookupsComponent generalLookupsComponent;
	
	public String parseMsisdnRequest(BusinessMessage businessMessage) {
		SochParseMsisdnRequest sochParseMsisdnRequest= new SochParseMsisdnRequest();
		if(businessMessage!=null && businessMessage.getPrimaryReceiverInfo()!=null) {
			String msisdn= businessMessage.getPrimaryReceiverInfo().getMsisdn();
			String country= businessMessage.getPrimaryReceiverInfo().getCountryIso2();
			sochParseMsisdnRequest.setMsisdn(msisdn);
			sochParseMsisdnRequest.setCountry(country);
		}
		return new Gson().toJson(sochParseMsisdnRequest);
	}
	
	
	public BusinessMessage parseMsisdn(BusinessMessage businessMessage) {
		
		String parseMsisdnJson= parseMsisdnRequest(businessMessage);
		SochParseMsisdnRequest parseMsisdnRequest= new Gson().fromJson(parseMsisdnJson, SochParseMsisdnRequest.class);
		logger.info("Sochitel request in parse msisdn [{}]", parseMsisdnRequest.toString());	
		String extractPrefixOfMsisdn = parseMsisdnRequest.getMsisdn();
		SochParseMsisdnResponse tempParseMsisdnResponse = new SochParseMsisdnResponse();
		if(extractPrefixOfMsisdn!=null) {
			extractPrefixOfMsisdn = extractPrefixOfMsisdn.substring(0, 6);
		
			for (Entry<Object, Object> enrty : saveOperatorsCacheMapFile.entrySet()) {		
				if (enrty.getValue().toString().contains(extractPrefixOfMsisdn)) {
					businessMessage.getITopup().setOperator_id(enrty.getKey().toString());
					
					String key= parseMsisdnRequest.getCountry()+"-op-"+enrty.getKey().toString();
					String parseValue= generalLookupsComponent.getLookupValue(key);
					
					if(parseValue!=null) {
						SochParseMsisdnResponse	parseMsisdnResponse= new Gson().fromJson(parseValue, SochParseMsisdnResponse.class);
						
						tempParseMsisdnResponse= parseMsisdnResponse;
						setParseMsisdnResponseIntoBM(parseMsisdnResponse, businessMessage);
						businessMessage.getSoftFields().put(Temp_Parse_Msisdn_Response, tempParseMsisdnResponse);
						return businessMessage;
					}else {
						return null;
					}
				}
			}
		}
		return null;
	}
	
	public Object onCall(BusinessMessage businessMessage, String responseString) throws Exception {
		SochParseMsisdnResponse tempParseMsisdnResponse = new SochParseMsisdnResponse();
		if(responseString!=null) {
			SochParseMsisdnResponse	parseMsisdnResponse= new Gson().fromJson(responseString, SochParseMsisdnResponse.class);
			if(parseMsisdnResponse.getResult()!=null) {
				String key= parseMsisdnResponse.getResult().getCountry().getId() +"-op-"+parseMsisdnResponse.getResult().getOperator().getId();
				if(generalLookupsComponent.getValueByKey(key)==null)
					generalLookupsComponent.saveKeyAndValue(key, responseString);
			}
			parseMsisdnResponse.setStatusType(statusChecker.checkServiceStatus(parseMsisdnResponse));
			tempParseMsisdnResponse= parseMsisdnResponse;
			if(businessMessage.getITopup()==null)
				businessMessage.setITopup(new InternationalTopup());	

			businessMessage.getITopup().setOperator_id(parseMsisdnResponse.getResult().getOperator().getId());
			
			setParseMsisdnResponseIntoBM(parseMsisdnResponse, businessMessage);
		}
		
		businessMessage.getSoftFields().put(Temp_Parse_Msisdn_Response,tempParseMsisdnResponse);
		return businessMessage;
	}
	
	public BusinessMessage generateResponse(BusinessMessage businessMessage) {
	
		SochOperatorProductsResponse sochitelProductRes = (SochOperatorProductsResponse) businessMessage.getSoftFields().get("sochProductsResponse");
		businessMessage.getSoftFields().remove("sochProductsResponse");
		SochParseMsisdnResponse tempParseMsisdnResponse=  (SochParseMsisdnResponse) businessMessage.getSoftFields().get(Temp_Parse_Msisdn_Response);
		businessMessage.getSoftFields().remove(Temp_Parse_Msisdn_Response);
		if(tempParseMsisdnResponse!=null && sochitelProductRes!=null) {
			sochitelUtility.fillOperatorsAndProductsInResponse(tempParseMsisdnResponse, sochitelProductRes);
			List<OperatorInfo> operatorInfoList= sochitelUtility.convertOperatorProductToOperatorInfo(tempParseMsisdnResponse.getOperatorsList());
			businessMessage.getITopup().getMsisdnInquiryResult().setOperatorsList(operatorInfoList);	
		}
		return businessMessage;
	}
	
	private BusinessMessage setParseMsisdnResponseIntoBM(SochParseMsisdnResponse parseMsisdnResponse, BusinessMessage businessMessage) {
		
		AirtimeOptions airtimeOptions= new AirtimeOptions();
		if(parseMsisdnResponse!=null && parseMsisdnResponse.getResult()!=null) {	
			airtimeOptions.setHasOpenRange(true);
			airtimeOptions.setCountry(parseMsisdnResponse.getResult().getCountry().getName());
			airtimeOptions.setIso(parseMsisdnResponse.getResult().getCountry().getId());
			airtimeOptions.setCanOverride(true);
			airtimeOptions.setMsisdn(parseMsisdnResponse.getResult().getNormalized());
		}
		if(businessMessage.getITopup().getMsisdnInquiryResult()==null)
			businessMessage.getITopup().setMsisdnInquiryResult(new MsisdnInquiryResult());
		businessMessage.getITopup().getMsisdnInquiryResult().setOpts(airtimeOptions);
		
		return businessMessage;
	}
	
	Map<Object, Object> loadPropertyToMap(Properties propertyFile) {
		Map<Object, Object> map = new HashMap<Object, Object>();
		for (String key : propertyFile.stringPropertyNames()) {
		    String value = propertyFile.getProperty(key);
		    map.put(key, value);
		}
		return map;
	}

	public StatusChecker getStatusChecker() {
		return statusChecker;
	}

	public void setStatusChecker(StatusChecker statusChecker) {
		this.statusChecker = statusChecker;
	}

	public SochitelService getSochitelService() {
		return sochitelService;
	}

	public void setSochitelService(SochitelService sochitelService) {
		this.sochitelService = sochitelService;
	}

	public SochitelUtility getSochitelUtility() {
		return sochitelUtility;
	}

	public void setSochitelUtility(SochitelUtility sochitelUtility) {
		this.sochitelUtility = sochitelUtility;
	}

	public Properties getPrefixProperty() {
		return prefixProperty;
	}

	public void setPrefixProperty(Properties prefixProperty) {
		this.prefixProperty = prefixProperty;
		saveOperatorsCacheMapFile = loadPropertyToMap(prefixProperty);
	}
	
	public OperatorProductsResponseValidator getOperatorProductsResponseValidator() {
		return operatorProductsResponseValidator;
	}

	public void setOperatorProductsResponseValidator(OperatorProductsResponseValidator operatorProductsResponseValidator) {
		this.operatorProductsResponseValidator = operatorProductsResponseValidator;
	}


	
	public GeneralLookupsComponent getGeneralLookupsComponent() {
		return generalLookupsComponent;
	}


	public void setGeneralLookupsComponent(GeneralLookupsComponent generalLookupsComponent) {
		this.generalLookupsComponent = generalLookupsComponent;
	}	
	
	
}