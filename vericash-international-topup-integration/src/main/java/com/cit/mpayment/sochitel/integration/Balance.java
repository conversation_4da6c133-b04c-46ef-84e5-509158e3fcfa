package com.cit.mpayment.sochitel.integration;

import org.codehaus.jackson.annotate.JsonProperty;

public class Balance {
	private String initial;
	private String transaction;
	private String commission;
	private String commissionPercentage;
	@JsonProperty("final")
	private String Final;
	private String currency;
	public String getInitial() {
		return initial;
	}
	public void setInitial(String initial) {
		this.initial = initial;
	}
	public String getTransaction() {
		return transaction;
	}
	public void setTransaction(String transaction) {
		this.transaction = transaction;
	}
	public String getCommission() {
		return commission;
	}
	public void setCommission(String commission) {
		this.commission = commission;
	}
	public String getCommissionPercentage() {
		return commissionPercentage;
	}
	public void setCommissionPercentage(String commissionPercentage) {
		this.commissionPercentage = commissionPercentage;
	}
	public String getFinal() {
		return Final;
	}
	public void setFinal(String final1) {
		Final = final1;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	
	
}
