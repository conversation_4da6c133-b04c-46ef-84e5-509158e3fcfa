package com.cit.mpayment.sochitel.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;

import com.cit.mpayment.sochitel.integration.OperatorAlternative;
import com.cit.mpayment.sochitel.integration.SochOperatorProductsResponse;
import com.cit.mpayment.sochitel.integration.TopupProduct;
import com.cit.mpayment.sochitel.integration.parsemsisdn.SochParseMsisdnResponse;
import com.cit.mpayment.sochitel.operator.products.OperatorProducts;
import com.cit.mpayment.sochitel.operator.products.Product;
import com.cit.mpaymentapp.international.airtime.common.AirtimeProduct;
import com.cit.mpaymentapp.international.airtime.common.OperatorInfo;

public class SochitelUtility {
	public void fillOperatorsAndProductsInResponse(SochParseMsisdnResponse parseMsisdnResponse,
			SochOperatorProductsResponse operatorProducts) {
		OperatorProducts operator = new OperatorProducts();
		operator.setCode(parseMsisdnResponse.getResult().getOperator().getBrandId());
		operator.setOperator_id(parseMsisdnResponse.getResult().getOperator().getId());
		operator.setOperator_name(parseMsisdnResponse.getResult().getOperator().getName());
		operator.setCountry(parseMsisdnResponse.getResult().getCountry().getName());
		operator.setIso(parseMsisdnResponse.getResult().getCountry().getId().toLowerCase());
		operator.setProducts(getOperatorProductsList(operatorProducts).getProducts());
		parseMsisdnResponse.getOperatorsList().add(operator);
		for (Entry<String, OperatorAlternative> entry : parseMsisdnResponse.getResult().getOperator().getAlt()
				.entrySet()) {
			if (!entry.getKey().equals(parseMsisdnResponse.getOperatorsList().get(0).getOperator_id())) {
				OperatorAlternative alt = entry.getValue();
				operator = new OperatorProducts();
				operator.setCode(alt.getBrandId());
				operator.setOperator_id(alt.getId());
				operator.setOperator_name(alt.getName());
				operator.setCountry(parseMsisdnResponse.getResult().getCountry().getName());
				operator.setIso(parseMsisdnResponse.getResult().getCountry().getId().toLowerCase());
				parseMsisdnResponse.getOperatorsList().add(operator);
			}
		}

	}
	public OperatorProducts getOperatorProductsList(SochOperatorProductsResponse operatorProductsResponse) {
		OperatorProducts operatorProducts = new OperatorProducts();

		for (Entry<String, TopupProduct> entry : operatorProductsResponse.getResult().getProducts().entrySet()) {
			Product product = new Product();
			TopupProduct topupProduct = entry.getValue();
			product.setTopup_currency(operatorProductsResponse.getResult().getCurrency().getUser());
			product.setCurrency(operatorProductsResponse.getResult().getCurrency().getOperator());
			product.setProduct_id(topupProduct.getId());
			product.setProduc_name(topupProduct.getName());
			product.setProduct_type(topupProduct.getProductType().getId());
			if (topupProduct.getPriceType().equalsIgnoreCase("range")) {
				product.setOpenRange(true);
				product.setOpenRangeMin(topupProduct.getPrice().getMin().getUser());
				product.setOpenRangeMax(topupProduct.getPrice().getMax().getUser());
				product.setStep("1");

			} else {
				product.setOpenRange(false);
				product.setDenomination(new BigDecimal(topupProduct.getPrice().getUser()));
				product.setPrice(new BigDecimal(topupProduct.getPrice().getOperator()));
			}

			operatorProducts.getProducts().add(product);
		}
		return operatorProducts;
	}
	
	public List<OperatorInfo> convertOperatorProductToOperatorInfo(List<OperatorProducts> operatorProductsList) {
		List<OperatorInfo> operatorInfoList= new ArrayList<OperatorInfo>();
		
		
		for(OperatorProducts op: operatorProductsList) {
			List<AirtimeProduct> airtimeProductList= new ArrayList<AirtimeProduct>();
			OperatorInfo operatorInfo= new OperatorInfo();
			operatorInfo.setCode(op.getCode());
			operatorInfo.setCountry(op.getCountry());
			operatorInfo.setOperator_name(op.getOperator_name());
			operatorInfo.setOperator_id(op.getOperator_id());
			operatorInfo.setIso(op.getIso());

			for(Product p: op.getProducts()) {
				AirtimeProduct airtimeProduct= new AirtimeProduct();
				airtimeProduct.setCurrency(p.getCurrency());
				airtimeProduct.setProduc_name(p.getProduc_name());
				airtimeProduct.setProduct_type(p.getProduct_type());
				airtimeProduct.setProduct_id(p.getProduct_id());
				airtimeProduct.setOpenRange(p.getOpenRange());
				
				airtimeProduct.setOpenRangeMax(p.getOpenRangeMax()!=null?new BigDecimal(p.getOpenRangeMax()):new BigDecimal("0"));
				airtimeProduct.setOpenRangeMin(p.getOpenRangeMin()!=null?new BigDecimal(p.getOpenRangeMin()):new BigDecimal("0"));
				airtimeProduct.setCurrency(p.getCurrency());
				airtimeProduct.setTopup_currency(p.getTopup_currency());
				airtimeProduct.setDenomination(p.getDenomination());
				airtimeProduct.setPrice(p.getPrice()!=null?p.getPrice().toString():"");
				airtimeProduct.setStep(p.getStep()!=null?Integer.parseInt(p.getStep()):0);
				airtimeProductList.add(airtimeProduct);
			}
			operatorInfo.setProducts(airtimeProductList);
			operatorInfoList.add(operatorInfo);
		}
		return operatorInfoList;
	}
	
}
