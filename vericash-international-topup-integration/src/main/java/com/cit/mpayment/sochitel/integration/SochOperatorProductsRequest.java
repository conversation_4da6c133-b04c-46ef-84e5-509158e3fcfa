package com.cit.mpayment.sochitel.integration;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class SochOperatorProductsRequest implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private ProductType type;
	private List<String> productTypes;
	private Map<String,TopupProduct> products;
	private UserVsOperator currency;
	
	public UserVsOperator getCurrency() {
		return currency;
	}
	public void setCurrency(UserVsOperator currency) {
		this.currency = currency;
	}
	public ProductType getType() {
		return type;
	}
	public void setType(ProductType type) {
		this.type = type;
	}
	public List<String> getProductTypes() {
		return productTypes;
	}
	public void setProductTypes(List<String> productTypes) {
		this.productTypes = productTypes;
	}
	public Map<String, TopupProduct> getProducts() {
		return products;
	}
	public void setProducts(Map<String, TopupProduct> products) {
		this.products = products;
	}
	@Override
	public String toString() {
		return "OperatorProductsResult [currency=" + currency + ", type=" + type + ", productTypes=" + productTypes
				+ ", products=" + products + "]";
	}	
}
