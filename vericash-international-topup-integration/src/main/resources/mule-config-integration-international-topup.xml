<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms"
	xmlns:spring="http://www.mulesoft.org/schema/mule/spring"
	xmlns:java="http://www.mulesoft.org/schema/mule/java"
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xsi:schemaLocation=
	"http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd 
	http://www.mulesoft.org/schema/mule/spring http://www.mulesoft.org/schema/mule/spring/current/mule-spring.xsd 
	http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
		<configuration-properties file="properties/international-topup-integration.properties" />					
		<import file="flows/international-topup-msisdn-inquiry-external-flow.xml" />
		<import file="flows/international-topup-perform-airtime-external-flow.xml"/>
		<import file="flows/international-topup-get-operators-external-flow.xml"/>
		<import file="flows/international-topup-token-flow.xml"/>
		<import file="flows/sochitel/integration-sochitel-flows.xml"/>
		<import file="flows/sochitel/get-operator-products-flow.xml"/>
		<import file="flows/sochitel/parse-msisdn-flow.xml"/>
	
	<http:request-config name="httpsInternationalTopupConnector">
		<http:request-connection protocol="HTTPS">
		</http:request-connection>
	</http:request-config>
		
	<error-handler name="InternationalTopupIntegrationExceptionStrategy">
		<on-error-continue>
			<logger level="ERROR" message=" ------------------ Start International topup Integration Exception Strategy ---------------------" />
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>	
			<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
			
			<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
			
			<async>
				<flow-ref name="Handle Exception" />
			</async>
			<logger level="ERROR" message=" ------------------ End International topup Integration Exception Strategy ---------------------"/>

<!--			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--				<jms:message correlationId="#[correlationId]">-->
<!--					<jms:body>#[payload]</jms:body>-->
<!--					<jms:properties>-->
<!--						#[{-->
<!--						MULE_CORRELATION_ID: vars.correlationId,-->
<!--						MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--						MULE_CORRELATION_SEQUENCE:'-1'-->
<!--						}]</jms:properties>-->
<!--				</jms:message>-->
<!--			</jms:publish>			-->
		</on-error-continue>
	</error-handler>

	<error-handler name="InternationalTopupChoiceExceptionStrategy">
		<on-error-continue>
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>		
			<choice>
				<when expression="#[error.cause == 'java.net.SocketTimeoutException']">
					<set-variable variableName="requestReference" value="#[vars.businessMessage.softFields.'REQUEST_REFERENCE']" />
		            <logger message="############# Start InternationalTopupChoiceExceptionStrategy Timeout Strategy #############" level="ERROR" />
		            <set-payload value="#[vars.businessMessage]" />
		            
					<script:execute engine="groovy">
						<script:code>
							payload.getStatus().setStatusCode(com.cit.shared.error.exception.InterSwitchException.INTERSWITCH_GENERIC_ERROR)
							return payload
						</script:code>
					</script:execute>						
		            <flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
		            
		            <logger message="############# End InternationalTopupChoiceExceptionStrategy Timeout Strategy #############" level="ERROR" />

<!--					<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--						<jms:message correlationId="#[correlationId]">-->
<!--							<jms:body>#[payload]</jms:body>-->
<!--							<jms:properties>-->
<!--								#[{-->
<!--								MULE_CORRELATION_ID: vars.correlationId,-->
<!--								MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--								MULE_CORRELATION_SEQUENCE:'-1'-->
<!--								}]</jms:properties>-->
<!--						</jms:message>-->
<!--					</jms:publish>						-->
				</when>
				<otherwise>
					<logger level="ERROR" message=" ------------------ Start International topup Integration Exception Strategy ---------------------" />
		
					<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
					
					<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
					
					
					<flow-ref name="Handle Exception" />
					
					<logger level="ERROR" message=" ------------------ End International topup Integration Exception Strategy ---------------------" />

					<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
						<jms:message correlationId="#[correlationId]">
							<jms:body>#[payload]</jms:body>
							<jms:properties>
								#[{
								MULE_CORRELATION_ID: vars.correlationId,
								MULE_CORRELATION_GROUP_SIZE: '-1',
								MULE_CORRELATION_SEQUENCE:'-1'
								}]</jms:properties>
						</jms:message>
					</jms:publish>						
				</otherwise>
		 </choice>
		</on-error-continue>
	</error-handler>
</mule>
