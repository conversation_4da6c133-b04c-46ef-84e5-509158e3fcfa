##### msisdn inquiry #####
jms.service.international.topup.msisdn.inquiry.inbound=jms/international/topup/msisdn/inquiry/inbound
jms.service.international.topup.msisdn.inquiry.outbound=jms/international/topup/msisdn/inquiry/outbound
##### get operators #####
jms.service.international.topup.get.operators.inbound=jms/international/topup/get/operators/inbound
jms.service.international.topup.get.operators.outbound=jms/international/topup/get/operators/outbound
##### perform airtime ######
jms.service.international.topup.perform.airtime.inbound=jms/international/topup/perform/airtime/inbound
jms.service.international.topup.perform.airtime.outbound=jms/international/topup/perform/airtime/outbound
############### error codes ###############
prime_error_codes=503,551,500,503,429,427,423,415,401,416
###### Prime Token #################
#vericash.account.username=<EMAIL>
#vericash.account.password=UBA@LiveTest444
######################################################
vericash.account.username=lpk_BuLLz8BJee0d6OP0gaiWEtgLI
vericash.account.password=spk_DSUvtUi3HPzDMFFwZdzOHfU0S
vericash.account.username=<EMAIL>
vericash.account.password=UBA@LiveTest444
############# Sochitel ###########################
jms.execute.sochitel.topup.request=jms/execute/sochitel/topup/request
jms.execute.sochitel.topup.response=jms/execute/sochitel/topup/response
sochitel.integration.timeout=50000
sochitel.applCode=VkVSaUNBU0g=
jms.service.international.topup.get.operator.products.inbound=jms/international/topup/getOperatorProducts/inbound
jms.service.international.topup.get.operator.products.outbound=jms/international/topup/getOperatorProducts/outbound
jms.service.sochitel.parse.msisdn.request=jms/sochitel/parseMsisdn/inbound
jms.service.sochitel.parse.msisdn.response=jms/sochitel/parseMsisdn/outbound
