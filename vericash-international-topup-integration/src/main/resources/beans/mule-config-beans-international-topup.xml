<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
    http://www.springframework.org/schema/beans/spring-beans.xsd 
    http://www.springframework.org/schema/context 
    http://www.springframework.org/schema/context/spring-context.xsd
    http://www.springframework.org/schema/util 
    http://www.springframework.org/schema/util/spring-util.xsd">

	 <bean id="jndiConfiguration" class="org.springframework.jndi.JndiTemplate">
        <property name="environment">
            <props>
                <prop key="java.naming.factory.url.pkgs">org.jboss.ejb.client.naming
                </prop>
            </props>
        </property>
    </bean>
	<bean id="msisdnInquiryResponseValidator" class="com.cit.mpaymentapp.msisdn.inquiry.MsisdnInquiryResponseValidator"/>
	<bean id="msisdnInquiryRequestGenerator" class="com.cit.mpaymentapp.msisdn.inquiry.MsisdnInquiryRequestGenerator"/>
	
	<bean id="getOperatorsResponseValidator" class="com.cit.mpaymentapp.get.operators.GetOperatorsResponseValidator"/>
	
	<bean id="performAirtimeRequestGenerator" class="com.cit.mpaymentapp.perform.airtime.PerformAirtimeRequestGenerator" />
	<bean id="performAirtimeResponseValidator" class="com.cit.mpaymentapp.perform.airtime.PerformAirtimeResponseValidator"/>
	
	<bean id="internationalTopupTokenRequestGenerator" class="com.cit.mpaymentapp.prime.token.InternationalTopupTokenRequestGenerator">
		<property name="internationalTopupProperties">
            <util:properties location="classpath:properties/international-topup-integration.properties" />
        </property>
	</bean>
	<bean id="internationalTopupTokenResponseValidator" class="com.cit.mpaymentapp.prime.token.InternationalTopupTokenResponseValidator"/>


	<bean id="internationalTopupToken" class="com.cit.mpaymentapp.international.airtime.token.InternationalTopupToken"/>
	
	<bean id="primeTopupResponseValidator" class="com.cit.mpaymentapp.airtime.PrimeTopupResponseValidator">
		<property name="internationalTopupProperties">
            <util:properties location="classpath:properties/international-topup-integration.properties" />
        </property>
	</bean>
	
	<bean id="statusChecker" class="com.cit.mpayment.sochitel.util.StatusChecker"/>
	<!--
	<bean id="sochitelService" class="com.cit.mpayment.sochitel.operator.products.SochitelService">
		<property name="statusChecker" ref="statusChecker"/>
        <property name="generalLookupsComponent" ref="generalLookupsComponent"></property>
    </bean>
	-->
    
	<bean id="operatorProductsResponseValidator" class="com.cit.mpayment.sochitel.operator.products.OperatorProductsResponseValidator">
		<property name="sochitelUtility" ref="sochitelUtility"/>
	</bean>

	<bean id="sochitelExecuteTopupComponent" class="com.cit.mpayment.sochitel.SochitelExecuteTopupComponent">
		<property name="statusChecker" ref="statusChecker" />
	</bean>
	<!--
	<bean id="sochitelExecuteTopupComponentWithSwitch" class="com.cit.mpayment.sochitel.SochitelExecuteTopupComponentWithSwitch">
		<property name="statusChecker" ref="statusChecker" />
	</bean>
	-->
	<bean id="exceptionStatusCodeManagerProvider" init-method="readStatusCodes" class="com.cit.mpayment.sochitel.util.ExceptionStatusCodeManagerProvider" />
	
	<bean id="sochitelUtility" class="com.cit.mpayment.sochitel.util.SochitelUtility"/>
	<bean id="parseMsisdnService" class="com.cit.mpayment.sochitel.integration.parsemsisdn.ParseMsisdnService">
		<property name="statusChecker" ref="statusChecker"/>
		<property name="sochitelUtility" ref="sochitelUtility"/>
		<property name="prefixProperty">
            <util:properties location="classpath:properties/prefixes.properties" />
        </property>
        <property name="operatorProductsResponseValidator" ref="operatorProductsResponseValidator"/>
	</bean>

</beans>