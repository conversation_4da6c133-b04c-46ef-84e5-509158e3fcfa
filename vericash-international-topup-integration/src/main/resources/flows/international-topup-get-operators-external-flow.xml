<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

	<flow name="International topup get operators External Flow">

		<jms:listener config-ref="JMS_Config" destination="${jms.service.international.topup.get.operators.inbound}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>
		
		<logger level="INFO" message="---------------------- International topup get opeartors External Flow --------------" />

		<set-variable value="#[correlationId]" variableName="correlationId"/>
		<set-variable variableName="businessMessage" value="#[payload]" doc:name="Variable" />
		<set-variable variableName="responseQueue" value="${jms.service.international.topup.get.operators.outbound}"  doc:name="Variable"/>
		

		<set-variable variableName="operatorIsoCode" value="#[payload.iTopup.msisdnInquiryResponse.opts.iso]" />

		<logger level="INFO" message="---------------------- ${international_topup_get_operators_address}#[vars.operatorIsoCode] --------------"/>
				
		<set-variable variableName="requestSOAPMessage" value="${international_topup_get_operators_address}#[vars.operatorIsoCode]" />				
		<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
		
		<flow-ref name="International topup token sub-flow"/>			
		
		<set-variable value="${international_topup_get_operators_address}" variableName="international_topup_get_operators_address"/>
		<http:request method="POST" url="#[vars.international_topup_get_operators_address ++ vars.operatorIsoCode]" responseTimeout="${thirdParty.integration.timeout}" config-ref="httpsInternationalTopupConnector">
			<http:headers ><![CDATA[#[output application/java
									---
									{
										Authorization : vars.Authorization,
										"Content-Type" : "application/json"
									}]]]>
			</http:headers>
		</http:request>		
		
		<logger level="INFO" message="------------------ International topup get operators response is : #[payload] -------------------------" />

		<set-variable variableName="responseSOAPMessage" value="#[payload]" />
		<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />

		<java:invoke
			class="com.cit.mpaymentapp.get.operators.GetOperatorsResponseValidator"
			method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,java.lang.String)"
			instance="getOperatorsResponseValidator">
			<java:args>
				<![CDATA[#[{ arg0: vars.businessMessage,
 	 				arg1: payload as String}]]]>
			</java:args>
		</java:invoke>	
		<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="InternationalTopupIntegrationExceptionStrategy" />
	</flow>

</mule>