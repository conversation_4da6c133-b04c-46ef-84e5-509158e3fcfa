<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

	<sub-flow name="International topup token sub-flow">
		
		<set-variable variableName="inMessage" value="#[payload]"/>
		<logger level="INFO" message="---------------------- International topup token sub-flow --------------" />
		
		<set-variable variableName="isTokenExpired" value="#[app.registry.internationalTopupToken.isExpired()]"/>
		
		<choice>
			<when expression="#[vars.isTokenExpired]">

				<java:invoke
					class="com.cit.mpaymentapp.prime.token.InternationalTopupTokenRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="internationalTopupTokenRequestGenerator">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>			


				<http:request method="POST" url="${international_airtime_generate_token_address}" responseTimeout="${thirdParty.integration.timeout}" config-ref="httpsInternationalTopupConnector"/>
	
				<logger level="INFO" message="------------------ International topup token response is : #[payload] -------------------------"  />


				<java:invoke
					class="com.cit.mpaymentapp.prime.token.InternationalTopupTokenResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.cit.mpaymentapp.international.airtime.token.InternationalTopupTokenResponse)"
					instance="internationalTopupTokenResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
			 				arg1: payload as Object {class: "com.cit.mpaymentapp.international.airtime.token.InternationalTopupTokenResponse"}}]]]>
					</java:args>
				</java:invoke>	
			</when>
			<otherwise>
				<logger level="INFO" message="----------isTokenExpired: #[vars.isTokenExpired] / Token: #[app.registry.internationalTopupToken.getInternationalTopupToken()]"/>
			</otherwise>
		</choice>
		<set-payload value="#[vars.inMessage]"/>
		<set-variable variableName="primeToken" value="Bearer #[app.registry.internationalTopupToken.getInternationalTopupToken()]"/>
		<logger level="INFO" message="Tooooooooooooooooooken: #[vars.primeToken]"/>	
		
		<set-variable value="#[vars.primeToken]" variableName="Authorization"/>
		<set-variable value="application/json" variableName="Content-Type"/>

			
	</sub-flow>

</mule>