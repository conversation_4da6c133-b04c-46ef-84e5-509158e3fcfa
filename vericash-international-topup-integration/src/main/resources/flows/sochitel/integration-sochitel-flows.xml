<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

		<flow name="Execute-Sochitel-Topup-External-Flow">
			<jms:listener config-ref="JMS_Config" destination="${jms.execute.sochitel.topup.request}" />
			
			<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
			<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
			<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
			<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>
			<set-variable value="#[correlationId]" variableName="correlationId" doc:name="Variable" />
			<set-variable variableName="businessMessage" value="#[payload]" doc:name="Variable" />
			<set-variable variableName="responseQueue" value="${jms.execute.sochitel.topup.response}" doc:name="Variable"/>
 			<set-variable variableName="walletShortCode" value="#[payload.walletInfo.walletShortCode]"/>
 			<set-variable value="http://" variableName="urlPrefix"/>
 				<logger level="INFO" message="----- Before Top-Up  Integration XXXXXXXXXXXX-------------" doc:name="Logger" />
			<choice>

				<when expression="#[${AirTimeSwitching_enabled} == true ]">
				   <set-variable variableName="SochTopUpURL" value="${execute_sochitel_topup_airtime_switching}" doc:name="Variable" />
					<java:invoke
						class="com.cit.mpayment.sochitel.SochitelExecuteTopupComponentWithSwitch"
						method="generateRequest(com.cit.mpaymentapp.common.message.BusinessMessage)"
						instance="sochitelExecuteTopupComponentWithSwitch">
						<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
					</java:invoke>						
				</when>
				<otherwise>
					<set-variable variableName="SochTopUpURL" value="${execute_sochitel_topup}" doc:name="Variable" />
					<java:invoke
						class="com.cit.mpayment.sochitel.SochitelExecuteTopupComponent"
						method="generateRequest(com.cit.mpaymentapp.common.message.BusinessMessage)"
						instance="sochitelExecuteTopupComponent">
						<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
					</java:invoke>						
				</otherwise>
			</choice>
			
			<logger level="INFO" message="----- Top-Up URL #[vars.SochTopUpURL] --------------" doc:name="Logger" />
			
			
			
			<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
			<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />				

			<set-variable variableName="SOCHITEL_APPLCODE" value="${sochitel.applCode}" />
			
			<http:request method="POST" url="#[vars.SochTopUpURL]" responseTimeout="${sochitel.integration.timeout}">
				<http:headers ><![CDATA[#[output application/java
										---
										{
											"Applcode" : vars.SOCHITEL_APPLCODE,
											"Content-Type" : "application/json"
										}]]]>
				</http:headers>
			</http:request>					
			<logger level="INFO" message="----After Integration hhhhhhhhhhhhhh #[${AirTimeSwitching_enabled}]" doc:name="Logger" />
			<logger level="INFO" message=" Topup Response #[payload] "/>
			<set-variable variableName="responseSOAPMessage" value="#[payload]" />
			<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
			<choice>
					<when expression="#[${AirTimeSwitching_enabled} == true ]">
			
		
					
					<java:invoke
								
						class="com.cit.mpayment.sochitel.SochitelExecuteTopupComponentWithSwitch"
						method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,java.lang.String)"
						instance="sochitelExecuteTopupComponentWithSwitch">
						<java:args>
							<![CDATA[#[{ arg0: vars.businessMessage,
			 	 				arg1: payload.^raw as String }]]]>
						</java:args>
					</java:invoke>							
				</when>
				<otherwise>				
					<java:invoke
													
						class="com.cit.mpayment.sochitel.SochitelExecuteTopupComponent"
						method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,java.lang.String)"
						instance="sochitelExecuteTopupComponent">
						<java:args>
							<![CDATA[#[{ arg0: vars.businessMessage,
			 	 				arg1: payload.^raw as String }]]]>
						</java:args>
					</java:invoke>							
				</otherwise>
	
			</choice>			
			
			<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>

<!--			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--				<jms:message correlationId="#[correlationId]">-->
<!--					<jms:body>#[payload]</jms:body>-->
<!--					<jms:properties>-->
<!--						#[{-->
<!--						MULE_CORRELATION_ID: vars.correlationId,-->
<!--						MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--						MULE_CORRELATION_SEQUENCE:'-1'-->
<!--						}]</jms:properties>-->
<!--				</jms:message>-->
<!--			</jms:publish>-->
			<error-handler ref="InternationalTopupChoiceExceptionStrategy" />
		</flow>

</mule>
