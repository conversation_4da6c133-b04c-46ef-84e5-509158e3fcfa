<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

	<flow name="Sochitel Parse Msisdn Flow">
		<jms:listener config-ref="JMS_Config" destination="${jms.service.sochitel.parse.msisdn.request}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>
		
		<logger level="INFO" message="######## START Parse MSISDN Flow #[message.payload] ########" doc:name="Logger" />

		<set-variable value="#[correlationId]" variableName="correlationId" doc:name="Variable" />
		<set-variable variableName="businessMessage" value="#[payload]" doc:name="Variable" />
		<set-variable variableName="responseQueue" value="${jms.service.sochitel.parse.msisdn.response}"  doc:name="Variable"/>

		<java:invoke
			class="com.cit.mpayment.sochitel.integration.parsemsisdn.ParseMsisdnService"
			method="parseMsisdn(com.cit.mpaymentapp.common.message.BusinessMessage)"
			instance="parseMsisdnService">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>	
		<choice>
		  <when expression="#[payload==null]">
			<logger level="INFO" message="######## CALLING PARSE MSISDN ENDPOINT WITH URL : ${sochitel_parseMsisdn}  ::Applcode is ${sochitel.applCode} ########" doc:name="Logger" />
			 <set-payload value="#[vars.businessMessage]" doc:name="Set Payload"/>		

			<java:invoke
				class="com.cit.mpayment.sochitel.integration.parsemsisdn.ParseMsisdnService"
				method="parseMsisdnRequest(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="parseMsisdnService">
				<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
			</java:invoke>		

			<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
			<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />

			<set-variable variableName="SOCHITEL_APPLCODE" value="${sochitel.applCode}" />
			
			<http:request method="POST" url="${sochitel_parseMsisdn}" responseTimeout="${sochitel.integration.timeout}">
				<http:headers ><![CDATA[#[output application/java
										---
										{
											"Applcode" : vars.SOCHITEL_APPLCODE,
											"Content-Type" : "application/json"
										}]]]>
				</http:headers>
			</http:request>			

			<logger level="INFO" message="######## ENDPOINT RESPONSE IS: #[message.payload]  ########" doc:name="Logger" />		

			<set-variable variableName="responseSOAPMessage" value="#[payload]" />
			<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
			
			<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>

			<java:invoke
				class="com.cit.mpayment.sochitel.integration.parsemsisdn.ParseMsisdnService"
				method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,java.lang.String)"
				instance="parseMsisdnService">
				<java:args>
					<![CDATA[#[{ arg0: vars.businessMessage,
		 				arg1: payload as String}]]]>
				</java:args>
			</java:invoke>				
		</when>
		<otherwise>
			 		<logger level="INFO" message="######## No Need To Call Endpoint ########" doc:name="Logger" />		
		</otherwise>
		</choice>
	
		<flow-ref name="common-get-operator-product-flow"/>

 		<logger level="INFO" message="######## PAYLOAD AFTER CALLING GET PRODUCTS FLOW: #[message.payload]  ########" doc:name="Logger" />		

			<java:invoke
				class="com.cit.mpayment.sochitel.integration.parsemsisdn.ParseMsisdnService"
				method="generateResponse(com.cit.mpaymentapp.common.message.BusinessMessage)"
				instance="parseMsisdnService">
				<java:args>
					<![CDATA[#[{ arg0: payload }]]]>
				</java:args>
			</java:invoke>	
		<logger level="INFO" message="######## END topup get operators products Flow ########" doc:name="Logger" />

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		 <error-handler ref="InternationalTopupChoiceExceptionStrategy" />
	</flow>

</mule>