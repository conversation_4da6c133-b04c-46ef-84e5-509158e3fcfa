package com.cit.service.commons.codeMapping;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import reactor.util.annotation.NonNull;

@Component
@RequiredArgsConstructor(onConstructor = @__({@Autowired,@Lazy}))
public class NewImplementationMappingComponent {
    @NonNull
    @Qualifier("newImplementationMappingCache")
    private NewImplementationMappingCache newImplementationMappingCache;

    public NewImplementationMappingComponent() {

    }

    public boolean isNewImplementation(String serviceCode){
        boolean isNewImplementation = newImplementationMappingCache.implementationList.contains(serviceCode) ? true : false;
        return  isNewImplementation;
    }
}
