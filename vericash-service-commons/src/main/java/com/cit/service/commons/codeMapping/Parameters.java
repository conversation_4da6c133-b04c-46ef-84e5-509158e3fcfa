package com.cit.service.commons.codeMapping;


import javax.xml.bind.annotation.XmlElement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class Parameters {

    public static final String DESTINATION_PAYMENT_METHOD_PAYMENT_METHOD_TYPE = "/payload/destinationPaymentMethod/paymentMethodType";
    @XmlElement
    private List<Param> param;

    @XmlElement
    private String serviceCode;

    public List<Param> getParam() {
        return param;
    }
    public String getDestinationPmType() {
        return getParamValue(DESTINATION_PAYMENT_METHOD_PAYMENT_METHOD_TYPE);
    }
    public String getParamValue(String path) {
        return this.param
                .stream()
                .filter(parameter-> parameter.getPath().equals(path))
                .findFirst()
                .orElse(new Param())
                .getValue();
    }

    public void setParam(List<Param> param) {
        this.param = param;
    }

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }
    public boolean containsParam(Param param) {
        if (param == null || this.param == null || this.param.isEmpty()) {
            return false;
        }
        return this.param
                .stream()
                .anyMatch(existingParam -> existingParam.equalsParam(param));

    }
}