package com.cit.mpaymentapp.common.seerbit;

import java.io.Serializable;

import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;

@JsonSerialize(include = Inclusion.NON_NULL)
public class SeerBitPayoutStatusRes implements Serializable{

	private static final long serialVersionUID = 1L;
	private String statusCode;
	private String statusMessage;
	
	public String getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}
	public String getStatusMessage() {
		return statusMessage;
	}
	public void setStatusMessage(String statusMessage) {
		this.statusMessage = statusMessage;
	}
	@Override
	public String toString() {
		return "SeerBitPayoutStatusRes [statusCode=" + statusCode
				+ ", statusMessage=" + statusMessage + "]";
	}
	

}
