package com.cit.mpaymentapp.common.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.cit.mpaymentapp.common.dao.Dao;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.TransactionInformation;
import com.cit.mpaymentapp.model.service.ServiceDetails;
import com.cit.mpaymentapp.model.service.ServiceLog;
import com.cit.mpaymentapp.model.service.ServiceLogStep;
import com.cit.mpaymentapp.model.service.ServiceStatus;
import com.cit.mpaymentapp.model.transaction.TransactionPartiesKyc;

public interface ServiceLogManager {

	public void saveTransactionInfo(TransactionPartiesKyc transactionKycInformation);
	public List<ServiceLog> getAllServicesLog(String walletShortCode);
	
	ServiceLogStep findServiceStep(Long stepId);
	
	Long addServiceLogStep(ServiceLogStep logStep, Long serviceLogId);
	
	boolean isExist(String requestId);
	
	ServiceLog addServiceLog(BusinessMessage message);
	ServiceLog addServiceLog(String instanceUniqueID, String serviceID, String serviceCode, String senderMSISDN, String senderUserKey, String senderWalletShortCode, String receiverMSISDN, String receiverUserKey, String receiverWalletShortCode, BigDecimal transactionAmount);
	
	Long addServiceLogStep(BusinessMessage message, Long stepId);
	
	void updateServiceLog(BusinessMessage message, ServiceStatus status, String reason);
	
	Long updateServiceLogStep(BusinessMessage message, Long stepId, ServiceStatus status);
	
	Long saveServiceLog(ServiceLog serviceLog);
	
	List<ServiceLog> searchServiceLogs(ServiceLog serviceLog);
	
	public List<ServiceDetails> getServiceLogs(ServiceLog serviceLog, boolean walletOwner, Long parentBusinessEntityID);
	
	public Map<Integer,Object> getServiceLogs(ServiceLog serviceLog,boolean walletOwner,Long parentBusinessEntityID,
			String transId, int pagingFrom, int pagingSize);
	
	public TransactionPartiesKyc getKycInfo(Long serviceId);
	
	public <T> int updateServiceLogByIdHQL(Class<T> klass, Map<String, Object> queryParametersMap,
			Map<String, Object> idMap, 
	    	String serviceCode);
}
