package com.cit.mpaymentapp.common.foreign.currency;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.common.message.Nation;

public class Configuration implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private List<Nation> countries;
	private List<ValidationTypes> validationTypes;
	
	public Configuration() {
		countries = new ArrayList<Nation>();
		validationTypes = new ArrayList<ValidationTypes>();
	}
	
	public List<Nation> getCountries() {
		return countries;
	}
	public void setCountries(List<Nation> countries) {
		this.countries = countries;
	}

	public List<ValidationTypes> getValidationTypes() {
		return validationTypes;
	}

	public void setValidationTypes(List<ValidationTypes> validationTypes) {
		this.validationTypes = validationTypes;
	}
	

}
