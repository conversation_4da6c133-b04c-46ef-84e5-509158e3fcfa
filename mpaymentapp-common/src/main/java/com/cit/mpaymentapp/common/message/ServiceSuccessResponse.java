package com.cit.mpaymentapp.common.message;

import java.io.Serializable;
import java.util.Arrays;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.common.succsseful.message.SuccessfulMessageDTO;

@JsonAutoDetect
public class ServiceSuccessResponse implements Serializable{

	private static final long serialVersionUID = 1L;
	private String successDescription;
	private String successShortDescription;
	private String sharedText;
	private String statusCode;
	private Boolean pendingApproval;
	private String[]params;
	
	public String getSuccessDescription() {
		return successDescription;
	}
	public void setSuccessDescription(String successDescription) {
		this.successDescription = successDescription;
	}
	public String getSuccessShortDescription() {
		return successShortDescription;
	}
	public void setSuccessShortDescription(String successShortDescription) {
		this.successShortDescription = successShortDescription;
	}
	
	public String getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}
	public String[] getParams() {
		return params;
	}
	public void setParams(String[] params) {
		this.params = params;
	}
	public String getSharedText() {
		return sharedText;
	}
	public void setSharedText(String sharedText) {
		this.sharedText = sharedText;
	}

	public Boolean getPendingApproval() {
		return pendingApproval;
	}

	public void setPendingApproval(Boolean pendingApproval) {
		this.pendingApproval = pendingApproval;
	}

	@Override
	public String toString() {
		return "ServiceSuccessResponse [successDescription=" + successDescription + ", successShortDescription="
				+ successShortDescription + ", sharedText=" + sharedText + ", statusCode=" + statusCode + ", params="
				+ Arrays.toString(params) + "]";
	}
	
}
