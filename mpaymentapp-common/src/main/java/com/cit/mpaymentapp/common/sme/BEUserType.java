package com.cit.mpaymentapp.common.sme;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BEUserType implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private Long typeId;
	private String typeName;
	private List<BERole> roles;
	private BERole role;
	
	public Long getTypeId() {
		return typeId;
	}
	public void setTypeId(Long typeId) {
		this.typeId = typeId;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public List<BERole> getRoles() {
		return roles;
	}
	public void setRoles(List<BERole> roles) {
		this.roles = roles;
	}
	public BERole getRole() {
		return role;
	}
	public void setRole(BERole role) {
		this.role = role;
	}
	
	@Override
	public String toString() {
		return "BEUserType [typeId=" + typeId + ", typeName=" + typeName + ", roles=" + roles + ", role=" + role + "]";
	}
}
