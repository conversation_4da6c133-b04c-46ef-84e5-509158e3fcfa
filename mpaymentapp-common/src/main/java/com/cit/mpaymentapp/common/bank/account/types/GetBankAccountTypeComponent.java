package com.cit.mpaymentapp.common.bank.account.types;

public class GetBankAccountTypeComponent {
	private static final GetBankAccountTypeComponent getBankAccountTypeComponent = new GetBankAccountTypeComponent();
	BankAccountTypesManager bankAccountTypesManager;

	public String getBankAccountType(String key) {
		return bankAccountTypesManager.getBankAccountType(key);
	}

	public BankAccountTypesManager getBankAccountTypesManager() {
		return bankAccountTypesManager;
	}

	public void setBankAccountTypesManager(BankAccountTypesManager bankAccountTypesManager) {
		this.bankAccountTypesManager = bankAccountTypesManager;
	}
	
	public static GetBankAccountTypeComponent getInstance() {
		return getBankAccountTypeComponent;
	}

}
