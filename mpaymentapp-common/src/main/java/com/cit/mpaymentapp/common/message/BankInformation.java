package com.cit.mpaymentapp.common.message;

import java.io.Serializable;
import java.util.Map;

import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.map.annotate.JsonSerialize.Inclusion;
import com.cit.mpaymentapp.common.customer.message.PaymentDetail;
import com.cit.mpaymentapp.common.customer.message.UsersRiskProfile;

@JsonSerialize(include = Inclusion.NON_NULL)
public class BankInformation extends PaymentDetail implements Serializable {

	@Override
	public String toString() {
		return "BankInformation [bankName=" + bankName + ", accountType=" + accountType + ", accountNumber="
				+ accountNumber + ", holderName=" + holderName + ", shortCode=" + shortCode + ", cardType=" + cardType
				+ ", cardCVV2=" + cardCVV2 + ", cardExpDate=" + cardExpDate + ", cardPIN=" + cardPIN + ", cardPAN="
				+ cardPAN + ", solId=" + solId + ", schemeCode=" + schemeCode + ", ribCode=" + ribCode + ", swiftCode="
				+ swiftCode + ", bankAddress=" + bankAddress + ", bankIdentifier=" + bankIdentifier
				+ ", destinationBankMap=" + destinationBankMap + "]";
	}

	private static final long serialVersionUID = 1L;

	private String bankName;
	private String accountType;
	private String accountNumber;
	private String holderName;
	private String shortCode;
	private String cardType;
	private String cardCVV2;
	private String cardExpDate;
	private String cardPIN;
	private String cardPAN;
	private String solId;
	private String schemeCode;
	private String ribCode;
	private String swiftCode;
	private String bankAddress;
	private String bankIdentifier;

	public String getRibCode() {
		return ribCode;
	}

	public void setRibCode(String ribCode) {
		this.ribCode = ribCode;
	}

	private Map<String, String> destinationBankMap;

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getAccountType() {
		return accountType;
	}

	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	public String getAccountNumber() {
		return accountNumber;
	}

	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}

	public String getHolderName() {
		return holderName;
	}

	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public String getCardCVV2() {
		return cardCVV2;
	}

	public void setCardCVV2(String cardCVV2) {
		this.cardCVV2 = cardCVV2;
	}

	public String getCardExpDate() {
		return cardExpDate;
	}

	public void setCardExpDate(String cardExpDate) {
		this.cardExpDate = cardExpDate;
	}

	public String getCardPIN() {
		return cardPIN;
	}

	public void setCardPIN(String cardPIN) {
		this.cardPIN = cardPIN;
	}

	public String getCardPAN() {
		return cardPAN;
	}

	public void setCardPAN(String cardPAN) {
		this.cardPAN = cardPAN;
	}

	public String getSolId() {
		return solId;
	}

	public void setSolId(String solId) {
		this.solId = solId;
	}

	public Map<String, String> getDestinationBankMap() {
		return destinationBankMap;
	}

	public void setDestinationBankMap(Map<String, String> destinationBankMap) {
		this.destinationBankMap = destinationBankMap;
	}

	public String getSchemeCode() {
		return schemeCode;
	}

	public void setSchemeCode(String schemeCode) {
		this.schemeCode = schemeCode;
	}
	
	public String getSwiftCode() {
		return swiftCode;
	}

	public void setSwiftCode(String swiftCode) {
		this.swiftCode = swiftCode;
	}

	public String getBankAddress() {
		return bankAddress;
	}

	public void setBankAddress(String bankAddress) {
		this.bankAddress = bankAddress;
	}

	public String getBankIdentifier() {
		return bankIdentifier;
	}

	public void setBankIdentifier(String bankIdentifier) {
		this.bankIdentifier = bankIdentifier;
	}

}
