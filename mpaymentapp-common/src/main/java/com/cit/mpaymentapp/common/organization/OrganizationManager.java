package com.cit.mpaymentapp.common.organization;

import java.util.Date;
import java.util.List;

import javax.ejb.Local;

import com.cit.mpaymentapp.model.businessentities.CurrencyExchangeRate;
import com.cit.mpaymentapp.model.organization.Organization;
import com.cit.shared.error.exception.GeneralFailureException;



public interface OrganizationManager {

	public void createOrganization(Organization organization) throws GeneralFailureException;

	public void updateOrganization(Organization organization) throws GeneralFailureException;

	public void removeOrganization(Long organizationId);

	public Organization getOrganization(Long organizationId);

	public List<Organization> getAllOrganizationList();

	public boolean isBusinessEntityInCobrandedOrganization(Long businessEntityId);
	
	public OrganizationSettlementReport runOrganizationSettlementReport(Long organizationID,
			Date fromDate, Date toDate);
	public void saveCurrencyExchange(CurrencyExchangeRate currencyExchangeRate);
	public void updateCurrencyExchange(CurrencyExchangeRate currencyExchangeRate);
	public List<CurrencyExchangeRate> getCurrencyExchangeRatesByWallet(Long walletId);
	public void deleteCurrencyExchange(CurrencyExchangeRate currencyExchangeRate);
	

}
