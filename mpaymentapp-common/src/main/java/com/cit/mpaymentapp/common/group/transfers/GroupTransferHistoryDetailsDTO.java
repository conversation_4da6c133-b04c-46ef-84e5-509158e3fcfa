package com.cit.mpaymentapp.common.group.transfers;

import java.util.List;

public class GroupTransferHistoryDetailsDTO extends GroupTransferRecipientDTO{

	private static final long serialVersionUID = 1L;
	
	private String executionResult;
	private String executionDate;
	
	private String ATMStatus;
	private String ATMPin;
	private Long ATMVoucherCode;
	private Integer ATMFee;
	private List<Integer> ATMActions;
	
	public String getExecutionResult() {
		return executionResult;
	}
	public void setExecutionResult(String executionResult) {
		this.executionResult = executionResult;
	}
	public String getExecutionDate() {
		return executionDate;
	}
	public void setExecutionDate(String executionDate) {
		this.executionDate = executionDate;
	}
	public String getATMStatus() {
		return ATMStatus;
	}
	public void setATMStatus(String aTMStatus) {
		ATMStatus = aTMStatus;
	}
	public String getATMPin() {
		return ATMPin;
	}
	public void setATMPin(String aTMPin) {
		ATMPin = aTMPin;
	}
	public Long getATMVoucherCode() {
		return ATMVoucherCode;
	}
	public void setATMVoucherCode(Long aTMVoucherCode) {
		ATMVoucherCode = aTMVoucherCode;
	}
	public Integer getATMFee() {
		return ATMFee;
	}
	public void setATMFee(Integer aTMFee) {
		ATMFee = aTMFee;
	}
	public List<Integer> getATMActions() {
		return ATMActions;
	}
	public void setATMActions(List<Integer> aTMActions) {
		ATMActions = aTMActions;
	}
}
