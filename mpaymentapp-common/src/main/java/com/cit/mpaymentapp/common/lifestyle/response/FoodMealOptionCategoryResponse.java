package com.cit.mpaymentapp.common.lifestyle.response;

import java.io.Serializable;

public class FoodMealOptionCategoryResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	private boolean option_multi_select;
	private String _id;
	private String name;
	private String option_title;
	private Integer option_maximum;
	
	public boolean isOption_multi_select() {
		return option_multi_select;
	}
	public void setOption_multi_select(boolean option_multi_select) {
		this.option_multi_select = option_multi_select;
	}
	public String get_id() {
		return _id;
	}
	public void set_id(String _id) {
		this._id = _id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getOption_title() {
		return option_title;
	}
	public void setOption_title(String option_title) {
		this.option_title = option_title;
	}
	public Integer getOption_maximum() {
		return option_maximum;
	}
	public void setOption_maximum(Integer option_maximum) {
		this.option_maximum = option_maximum;
	}
	

}
