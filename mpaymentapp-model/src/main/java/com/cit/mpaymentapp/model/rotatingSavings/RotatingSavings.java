package com.cit.mpaymentapp.model.rotatingSavings;

import com.cit.mpaymentapp.model.base.IBaseModel;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.mpaymentapp.model.group.transfers.GroupTransferSetup;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "ROTATING_SAVINGS")
public class RotatingSavings implements IBaseModel, Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    @Column(name = "ID")
    private Long id;

    @Column(name = "START_DATE")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    private Date startDate;

    @Column(name = "NUMBER_MEMBERS")
    private int numberOfMembers;

    @ManyToOne
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
    @JoinColumn(name = "STATUS", referencedColumnName = "ID", nullable = true)
    private RotatingSavingsStatus status;

    @Column(name = "CONTRIBUTION_AMOUNT")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private int contributionAmount;

    @Column(name = "CURRENCY")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private String currency;

    @ManyToOne
    @JoinColumn(name = "CONTRIBUTION_FREQUENCY", referencedColumnName = "ID", nullable = true)
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private RotatingSavingsFrequency contributionFrequency;

    @Column(name = "PAYMENT_DUE_DAY")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private String paymentDueDay;

    @ManyToOne
    @JoinColumn(name = "WINNING_TURN_DECISION", referencedColumnName = "ID", nullable = true)
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private WinningTurnDecision winningTurnDecision;

    @Column(name = "SUBSCRIPTION_START_DATE")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private Date subscriptionStartDate;

    @Column(name = "SUBSCRIPTION_DURATION")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private int subscriptionDuration;

    @Column(name = "CURRENT_TURN")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private int currentTurn;

    @ManyToOne
    @JoinColumn(name = "ORGANIZER", referencedColumnName = "Customer_ID", nullable = true)
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private Customer organizer;

    @Column(name = "SUBSCRIPTION_END_DATE")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private Date subscriptionEndDate;

    @Column(name = "TOTAL_AMOUNT")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private int totalAmount;

    @Column(name = "CREATION_DATE")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private Date creationDate;

    @Column(name = "END_DATE")
    @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)

    private Date endDate;

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public int getNumberOfMembers() {
        return numberOfMembers;
    }

    public void setNumberOfMembers(int numberOfMembers) {
        this.numberOfMembers = numberOfMembers;
    }

    public RotatingSavingsStatus getStatus() {
        return status;
    }

    public void setStatus(RotatingSavingsStatus status) {
        this.status = status;
    }

    public int getContributionAmount() {
        return contributionAmount;
    }

    public void setContributionAmount(int contributionAmount) {
        this.contributionAmount = contributionAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public RotatingSavingsFrequency getContributionFrequency() {
        return contributionFrequency;
    }

    public void setContributionFrequency(RotatingSavingsFrequency contributionFrequency) {
        this.contributionFrequency = contributionFrequency;
    }

    public String getPaymentDueDay() {
        return paymentDueDay;
    }

    public void setPaymentDueDay(String paymentDueDay) {
        this.paymentDueDay = paymentDueDay;
    }

    public WinningTurnDecision getWinningTurnDecision() {
        return winningTurnDecision;
    }

    public void setWinningTurnDecision(WinningTurnDecision winningTurnDecision) {
        this.winningTurnDecision = winningTurnDecision;
    }

    public Date getSubscriptionStartDate() {
        return subscriptionStartDate;
    }

    public void setSubscriptionStartDate(Date subscriptionStartDate) {
        this.subscriptionStartDate = subscriptionStartDate;
    }

    public int getSubscriptionDuration() {
        return subscriptionDuration;
    }

    public void setSubscriptionDuration(int subscriptionDuration) {
        this.subscriptionDuration = subscriptionDuration;
    }

    public int getCurrentTurn() {
        return currentTurn;
    }

    public void setCurrentTurn(int currentTurn) {
        this.currentTurn = currentTurn;
    }

    public Customer getOrganizer() {
        return organizer;
    }

    public void setOrganizer(Customer organizer) {
        this.organizer = organizer;
    }

    public Date getSubscriptionEndDate() {
        return subscriptionEndDate;
    }

    public void setSubscriptionEndDate(Date subscriptionEndDate) {
        this.subscriptionEndDate = subscriptionEndDate;
    }

    public int getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(int totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
