package com.cit.mpaymentapp.model.audit;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.cit.mpaymentapp.model.base.IBaseModel;

@Entity
@Table(name = "Audit_Trail")
public class AuditTrail implements IBaseModel , Serializable{
	
	public String toString() {
		return "AuditTrail [id=" + id + ", userName=" + userName
				+ ", createdAt=" + createdAt + ", description=" + description
				+ ", detailedDescription=" + detailedDescription
				+ "]";
	}

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE)
	@Column(name = "audit_trail_id")
	private Long id;
	@Column(name = "User_Name")
	private String userName;
	@Column(name = "created_At")
	private Date createdAt;
	@Column(name = "Description")
	private String description;
	
	@Column(name = "Detailed_Description")
	@Lob
	@Type(type="org.hibernate.type.MaterializedClobType")
	private String detailedDescription;
	@ManyToOne
	@JoinColumn(name="Audit_Configuration_id")
	private AuditConfiguration auditConfiguration;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDetailedDescription() {
		return detailedDescription;
	}

	public void setDetailedDescription(String detailedDescription) {
		this.detailedDescription = detailedDescription;
	}

	public AuditConfiguration getAuditConfiguration() {
		return auditConfiguration;
	}

	public void setAuditConfiguration(AuditConfiguration auditConfiguration) {
		this.auditConfiguration = auditConfiguration;
	}
}
