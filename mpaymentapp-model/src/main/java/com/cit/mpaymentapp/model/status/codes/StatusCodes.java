package com.cit.mpaymentapp.model.status.codes;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.cit.mpaymentapp.model.base.IBaseModel;

@Entity
@Table(name = "Status_Codes")
public class StatusCodes implements Serializable, IBaseModel {

	private static final long serialVersionUID = 1L;
	
	@EmbeddedId
	private StatusCodePK id;
	@Column(name = "Source")
	private String source;
	@Column(name = "Description", length=1000, nullable = false)
	private String description;
	@Column(name = "Short_Description", length=1000)
	private String shortDescription;
	@Column(name = "Local_Description", length=1000)
	private String localDescription;
	@Column(name = "Email_Alert", columnDefinition = "NUMBER(1,0) default 0")
	private Integer emailAlert = 0;
	@Column(name = "SMS_Alert", columnDefinition = "NUMBER(1,0) default 0")
	private Integer sMSAlert = 0;
	@Column(name = "DISPLAY_HINT", columnDefinition = "NUMBER(1,0) default 0")
	private Integer displayHint = 0;
	
	public StatusCodePK getId() {
		return id;
	}

	public void setId(StatusCodePK id) {
		this.id = id;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getShortDescription() {
		return shortDescription;
	}

	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}

	public String getLocalDescription() {
		return localDescription;
	}

	public void setLocalDescription(String localDescription) {
		this.localDescription = localDescription;
	}

	public Boolean isEmailAlert() {
		return emailAlert == 1;
	}

	public void setEmailAlert(Boolean emailAlert) {
		this.emailAlert = emailAlert.compareTo(false);
	}

	public Boolean isSMSAlert() {
		return sMSAlert == 1;
	}

	public void setsMSAlert(Boolean sMSAlert) {
		this.sMSAlert = sMSAlert.compareTo(false);
	}

	public Integer getDisplayHint() {
		return displayHint;
	}

	public void setDisplayHint(Integer displayHint) {
		this.displayHint = displayHint;
	}
	
}