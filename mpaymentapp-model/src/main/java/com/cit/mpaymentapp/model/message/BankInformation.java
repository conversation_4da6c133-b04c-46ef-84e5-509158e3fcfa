package com.cit.mpaymentapp.model.message;

import java.io.Serializable;

public class BankInformation implements Serializable{

	
	@Override
	public String toString() {
		return "BankInformation [bankName=" + bankName + ", accountType="
				+ accountType + ", accountNumber=" + accountNumber
				+ ", holderName=" + holderName + ", shortCode=" + shortCode
				+ "]";
	}
	private static final long serialVersionUID = 1L;
	
	private String bankName;
	private String accountType;
	private String accountNumber;
	private String holderName;
	private String shortCode;
	private String cardType;
	private String cardCVV2;
	private String cardExpDate;
	private String cardPIN;
	private String cardPAN;
	
	
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getAccountType() {
		return accountType;
	}
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}
	public String getAccountNumber() {
		return accountNumber;
	}
	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}
	public String getHolderName() {
		return holderName;
	}
	public void setHolderName(String holderName) {
		this.holderName = holderName;
	}
	public String getShortCode() {
		return shortCode;
	}
	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}
	public String getCardType() {
		return cardType;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}
	public String getCardCVV2() {
		return cardCVV2;
	}
	public void setCardCVV2(String cardCVV2) {
		this.cardCVV2 = cardCVV2;
	}
	public String getCardExpDate() {
		return cardExpDate;
	}
	public void setCardExpDate(String cardExpDate) {
		this.cardExpDate = cardExpDate;
	}
	public String getCardPIN() {
		return cardPIN;
	}
	public void setCardPIN(String cardPIN) {
		this.cardPIN = cardPIN;
	}
	public String getCardPAN() {
		return cardPAN;
	}
	public void setCardPAN(String cardPAN) {
		this.cardPAN = cardPAN;
	}
	

}
