package com.cit.mpaymentapp.model.transaction;

import java.io.Serializable;

import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import com.cit.mpaymentapp.model.base.IBaseModel;
@Entity
@Table(name="TRANSACTIONEXCUTEDSTATUS")
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)

public class TransactionsExcutedStatus implements IBaseModel, Serializable{
	
	

	private static final long serialVersionUID = 1L;
	@Id
	@Column(name="Status_ID",nullable=false)
	private int StatusId;
	
	
	  @Column(name = "TRANSACTION_EXCUTED_STATUS")
	 
	  private String  transactionsExcutedStatus ;


	public int getStatusId() {
		return StatusId;
	}


	public void setStatusId(int statusId) {
		StatusId = statusId;
	}


	public String getTransactionsExcutedStatus() {
		return transactionsExcutedStatus;
	}


	public void setTransactionsExcutedStatus(String transactionsExcutedStatus) {
		this.transactionsExcutedStatus = transactionsExcutedStatus;
	}


	



	


}
