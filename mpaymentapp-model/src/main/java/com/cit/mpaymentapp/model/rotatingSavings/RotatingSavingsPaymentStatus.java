package com.cit.mpaymentapp.model.rotatingSavings;


import com.cit.mpaymentapp.model.base.IBaseModel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "ROTATING_SAVING_PAYMENT_STATUS")
public class RotatingSavingsPaymentStatus implements IBaseModel, Serializable {
    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "NAME")
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
