package com.cit.mpaymentapp.model.customer.approval;

import com.cit.mpaymentapp.model.base.IBaseModel;
import lombok.*;
import org.hibernate.proxy.HibernateProxy;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "RECEIVER_REQUEST_APPROVAL_STATUS")
public class ReceiverRequestApprovalStatus implements Serializable, IBaseModel {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    private Long id;
    @Column(name = "status_name", nullable = false)
    private String statusName;

    public ReceiverRequestApprovalStatus(RECEIVER_REQUEST_APPROVAL_STATUS customerApprovalStatus){
        id= (long) customerApprovalStatus.getId();
        statusName=customerApprovalStatus.name();
    }
    public ReceiverRequestApprovalStatus(Long id){
        this.id= (long)id;
    }

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (o == null) return false;
        Class<?> oEffectiveClass = o instanceof HibernateProxy ? ((HibernateProxy) o).getHibernateLazyInitializer().getPersistentClass() : o.getClass();
        Class<?> thisEffectiveClass = this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass() : this.getClass();
        if (thisEffectiveClass != oEffectiveClass) return false;
        ReceiverRequestApprovalStatus that = (ReceiverRequestApprovalStatus) o;
        return getId() != null && Objects.equals(getId(), that.getId());
    }


    @Override
    public final int hashCode() {
        return this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass().hashCode() : getClass().hashCode();
    }
}