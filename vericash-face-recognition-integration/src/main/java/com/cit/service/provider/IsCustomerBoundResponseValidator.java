package com.cit.service.provider;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.IsCustomerBoundResponse;
import com.daon.ws.identityx.IsCustomerBoundResponse.ResponseData;

public class IsCustomerBoundResponseValidator  {

	public Object onCall(BusinessMessage businessMessage, IsCustomerBoundResponse isCustomerBoundResponse) throws Exception {


		ResponseStatus responseStatus = isCustomerBoundResponse.getResponseStatus();
		ResponseData responseData = isCustomerBoundResponse.getResponseData();
		String profileIdentifier = (String) businessMessage.getSoftFields().get("ProfileIdentifier");

		if (responseStatus.getReturnCode() == FaceRecognitionStatusCodes.SUCCESS) {
			
			businessMessage.getSoftFields().put("customer_bound", responseData.isCustomerBound());

			if (responseData.isCustomerBound() && !responseData.getProfileIdentifier().equals(profileIdentifier)) {
				businessMessage.getStatus().setErrorFlag(true);
				businessMessage.getStatus().setStatusMsg("Customer bound to another profile");
			}

		} else {

			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());

		}

		return businessMessage;

	}
}
