package com.cit.service.provider;

import java.io.StringWriter;
import java.util.Properties;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.BindCustomerToServiceProviderRequest;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class BindCustomerRequestGenerator {
	 
	private Properties faceRecognitionProperties;
	

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		BindCustomerToServiceProviderRequest bindCustomerToServiceProviderRequest = new BindCustomerToServiceProviderRequest();		
		
		bindCustomerToServiceProviderRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		bindCustomerToServiceProviderRequest.setCustomerIdentifier((String) businessMessage.getSoftFields().get("CustomerIdentifier"));
		bindCustomerToServiceProviderRequest.setServiceProviderIdentifier(faceRecognitionProperties.getProperty("SERVICE_PROVIDER_IDENTIFIER"));
		bindCustomerToServiceProviderRequest.setProfileIdentifier((String) businessMessage.getSoftFields().get("ProfileIdentifier"));
	
		JAXBContext context = JAXBContext.newInstance(BindCustomerToServiceProviderRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(bindCustomerToServiceProviderRequest, sw);
		return sw;
	
		
	}

	public Properties getFaceRecognitionProperties() {
		return faceRecognitionProperties;
	}
	public void setFaceRecognitionProperties(Properties faceRecognitionProperties) {
		this.faceRecognitionProperties = faceRecognitionProperties;
	}

}

