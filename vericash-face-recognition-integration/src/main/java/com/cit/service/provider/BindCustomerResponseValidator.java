package com.cit.service.provider;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.BindCustomerToServiceProviderResponse;

public class BindCustomerResponseValidator{

	public Object onCall(BusinessMessage businessMessage, BindCustomerToServiceProviderResponse bindCustomerToServiceProviderResponse) throws Exception {


		ResponseStatus responseStatus = bindCustomerToServiceProviderResponse.getResponseStatus();

		if (responseStatus.getReturnCode() != FaceRecognitionStatusCodes.SUCCESS) {
			
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());
		}

		return businessMessage;
		
	}
}
