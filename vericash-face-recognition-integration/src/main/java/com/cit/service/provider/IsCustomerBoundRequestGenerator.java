package com.cit.service.provider;

import java.io.StringWriter;
import java.util.Properties;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.IsCustomerBoundRequest;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class IsCustomerBoundRequestGenerator  {
	
	private Properties faceRecognitionProperties;
	
	public Object onCall(BusinessMessage businessMessage) throws Exception {

		IsCustomerBoundRequest isCustomerBoundRequest = new IsCustomerBoundRequest();		
		
		isCustomerBoundRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		isCustomerBoundRequest.setCustomerIdentifier((String) businessMessage.getSoftFields().get("CustomerIdentifier"));
		isCustomerBoundRequest.setServiceProviderIdentifier(faceRecognitionProperties.getProperty("SERVICE_PROVIDER_IDENTIFIER"));
		
		JAXBContext context = JAXBContext.newInstance(IsCustomerBoundRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(isCustomerBoundRequest, sw);
		return sw;
		
	}

	public void setFaceRecognitionProperties(Properties faceRecognitionProperties) {
		this.faceRecognitionProperties = faceRecognitionProperties;
	}

	public Properties getFaceRecognitionProperties() {
		return faceRecognitionProperties;
	}
	
}

