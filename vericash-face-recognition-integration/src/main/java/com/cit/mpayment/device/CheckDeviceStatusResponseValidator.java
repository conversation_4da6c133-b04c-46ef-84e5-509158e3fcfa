package com.cit.mpayment.device;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpayment.util.FaceRecognitionStatusCodes.CheckDeviceStatus;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.CheckDeviceStatusResponse;

public class CheckDeviceStatusResponseValidator  {


	public Object onCall(BusinessMessage businessMessage, CheckDeviceStatusResponse checkDeviceStatusResponse) throws Exception {


		ResponseStatus responseStatus = checkDeviceStatusResponse.getResponseStatus();

		if (responseStatus.getReturnCode() == FaceRecognitionStatusCodes.SUCCESS) {
			businessMessage.getSoftFields().put("new_device", true);

		} else if (responseStatus.getReturnCode() == CheckDeviceStatus.DEVICE_BOUND) {
			businessMessage.getSoftFields().put("new_device", false);

		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());
			
		}

		return businessMessage;

	}
}
