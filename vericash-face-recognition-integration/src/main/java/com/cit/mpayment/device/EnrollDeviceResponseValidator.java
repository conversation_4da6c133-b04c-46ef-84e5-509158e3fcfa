package com.cit.mpayment.device;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.AddDeviceResponse;

public class EnrollDeviceResponseValidator{


	public Object onCall(BusinessMessage businessMessage, AddDeviceResponse addDeviceResponse) throws Exception {

		ResponseStatus responseStatus = addDeviceResponse.getResponseStatus();

		if (responseStatus.getReturnCode() != FaceRecognitionStatusCodes.SUCCESS) {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());
		}

		return businessMessage;
		
	}
}
