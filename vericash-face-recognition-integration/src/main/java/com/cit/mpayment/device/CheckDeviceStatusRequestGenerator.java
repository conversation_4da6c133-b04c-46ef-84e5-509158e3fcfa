package com.cit.mpayment.device;

import java.io.StringWriter;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.CheckDeviceStatusRequest;
import com.daon.ws.identityx.DeviceInfo;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class CheckDeviceStatusRequestGenerator {
	 

	public Object onCall(BusinessMessage businessMessage) throws Exception {
	

		
		CheckDeviceStatusRequest checkDeviceStatusRequest = new CheckDeviceStatusRequest();		
		
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceIdentifier((String) businessMessage.getSoftFields().get("DeviceIdentifier"));
		deviceInfo.getDeviceData().add(FaceRecognitionUtil.getCababilites());
	
		
		checkDeviceStatusRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		checkDeviceStatusRequest.setDeviceInfo(deviceInfo);
		JAXBContext context = JAXBContext.newInstance(CheckDeviceStatusRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(checkDeviceStatusRequest, sw);
		return sw;
	
		
	}

}

