package com.cit.mpayment.device;

import java.io.StringWriter;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.AddDeviceRequest;
import com.daon.ws.identityx.CaptureMetaData;
import com.daon.ws.identityx.CaptureMetaDataItem;
import com.daon.ws.identityx.CaptureScriptResults;
import com.daon.ws.identityx.CaptureStepDataItem;
import com.daon.ws.identityx.CaptureStepResult;
import com.daon.ws.identityx.DeviceInfo;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class EnrollDeviceRequestGenerator  {

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		
		String challengeKeyValue = null;
		if( businessMessage.getPrimarySenderInfo().getFaceDetails() != null &&
				(challengeKeyValue = businessMessage.getPrimarySenderInfo().getFaceDetails().getChallengeKey()) == null)
			throw new NullPointerException();
		
		CaptureMetaDataItem challengeKey = new CaptureMetaDataItem();
		challengeKey.setName("ChallengeKey");
		challengeKey.setValue(challengeKeyValue);
		
		CaptureMetaDataItem challengeKeyIdentifier = new CaptureMetaDataItem();
		challengeKeyIdentifier.setName("ChallengeKeyIdentifier");
		challengeKeyIdentifier.setValue((String) businessMessage.getSoftFields().get("ChallengeKeyIdentifier"));
		
		CaptureMetaDataItem challengeKeyAlgorithm = new CaptureMetaDataItem();
		challengeKeyAlgorithm.setName("ChallengeKeyAlgorithm");
		challengeKeyAlgorithm.setValue("RSA");
		
		CaptureMetaData captureMetaData = new CaptureMetaData();
		captureMetaData.getMetaDataItem().add(challengeKey);
		captureMetaData.getMetaDataItem().add(challengeKeyIdentifier);
		captureMetaData.getMetaDataItem().add(challengeKeyAlgorithm);
		
		CaptureStepDataItem captureStepDataItem = new CaptureStepDataItem();
		captureStepDataItem.setIdentifier("key");
		captureStepDataItem.setMetaData(captureMetaData);
		
		CaptureStepResult captureStepResult = new CaptureStepResult();
		captureStepResult.setIdentifier("device");
		captureStepResult.getDataItem().add(captureStepDataItem);
		
		CaptureScriptResults captureScriptResults = new CaptureScriptResults();
		captureScriptResults.setCaptureScriptIdentifier("identityx");
		captureScriptResults.getCaptureStepResult().add(captureStepResult);
		
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceIdentifier((String) businessMessage.getSoftFields().get("DeviceIdentifier"));
		deviceInfo.getDeviceData().add(FaceRecognitionUtil.getCababilites());
	
		AddDeviceRequest addDeviceRequest = new AddDeviceRequest();
		addDeviceRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		addDeviceRequest.setDeviceInfo(deviceInfo);
		addDeviceRequest.setProfileIdentifier((String) businessMessage.getSoftFields().get("ProfileIdentifier"));
		addDeviceRequest.getCaptureScriptResults().add(captureScriptResults);
			
		JAXBContext context = JAXBContext.newInstance(AddDeviceRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(addDeviceRequest, sw);
		return sw;
		
	}

}
