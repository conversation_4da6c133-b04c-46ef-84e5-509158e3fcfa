package com.cit.mpayment.face;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.shared.error.util.ExceptionResolver;
import com.cit.shared.error.util.ExceptionUtil;
import com.daon.ws.identityx.VerifyIdentityResponse;

public class VerifyCustomerFaceResponseValidator{


	public Object onCall(BusinessMessage businessMessage, VerifyIdentityResponse verifyFaceResponse) throws Exception {
		ExceptionResolver exception = null;

		if(verifyFaceResponse instanceof VerifyIdentityResponse){
			if(verifyFaceResponse.getResponseStatus()!=null && verifyFaceResponse.getResponseStatus().getReturnCode()==0){
				if(!verifyFaceResponse.getResponseData().getCapturedDataProcessingResults().get(0).getCaptureStepResult().get(0).getDataItem().get(0).getMetaData().getMetaDataItem().get(3).getValue().equals("MATCH")){
					throw new GeneralFailureException(GeneralFailureException.FAILED_TO_VERIFY_FACE);
				}else{
					businessMessage.getSoftFields().put("Face_Recognition_Success", true);
					}
			}else if(verifyFaceResponse.getResponseStatus()!=null){
				if(verifyFaceResponse.getResponseStatus().getReturnCode() == FaceRecognitionStatusCodes.CheckDeviceStatus.IDENTITYX_NO_DEVICES_FOUND)
					throw new GeneralFailureException(GeneralFailureException.NO_DEVICES_FOUND);
				
				businessMessage.getStatus().setErrorFlag(true);
				businessMessage.getStatus().setStatusCode(String.valueOf(verifyFaceResponse.getResponseStatus().getReturnCode()));
				businessMessage.getStatus().setStatusMsg(verifyFaceResponse.getResponseStatus().getDescription());
				exception = ExceptionUtil.handle(String.valueOf(verifyFaceResponse.getResponseStatus().getReturnCode()));
			}
			return businessMessage;
		}else{
			throw new GeneralFailureException(GeneralFailureException.SERVICE_IS_TEMPORARILY_UNAVAILLABLE);
		}
		
	}

}
