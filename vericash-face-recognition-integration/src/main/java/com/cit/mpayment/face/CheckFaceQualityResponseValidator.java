package com.cit.mpayment.face;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.CheckFaceQualityResponse;
import com.daon.ws.identityx.CheckFaceQualityResponse.ResponseData;

public class CheckFaceQualityResponseValidator{

	public Object onCall(BusinessMessage businessMessage, CheckFaceQualityResponse checkFaceQualityResponse) throws Exception {



		ResponseStatus responseStatus = checkFaceQualityResponse.getResponseStatus();
		ResponseData responseData = checkFaceQualityResponse.getResponseData();
				
		if (responseStatus.getReturnCode() != FaceRecognitionStatusCodes.SUCCESS 
				|| (responseData != null && !responseData.isPassed()) ) {
		
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());
			
		}
		
		return businessMessage;

	}
}
