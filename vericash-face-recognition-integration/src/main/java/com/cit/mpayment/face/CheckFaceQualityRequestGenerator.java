package com.cit.mpayment.face;

import java.io.StringWriter;

import org.apache.commons.codec.binary.Base64;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.CheckFaceQualityRequest;
import com.daon.ws.identityx.Face;
import com.daon.ws.identityx.ImageType;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class CheckFaceQualityRequestGenerator{
	

	public Object onCall(BusinessMessage businessMessage ) throws Exception {

		CheckFaceQualityRequest checkFaceQualityRequest = new CheckFaceQualityRequest();
		
		String encodedOnce = null;
		if( businessMessage.getPrimarySenderInfo().getFaceDetails() != null && 
				(encodedOnce = businessMessage.getPrimarySenderInfo().getFaceDetails().getImage()) == null)
			throw new NullPointerException();
		
		byte[] rawImageBytes = Base64.decodeBase64(encodedOnce);
		Face face = new Face();
		face.setData(rawImageBytes);
		face.setImageType(ImageType.JPG);
		businessMessage.getSoftFields().put("face_bytes", rawImageBytes);
		
		checkFaceQualityRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		checkFaceQualityRequest.setFace(face);
	
		JAXBContext context = JAXBContext.newInstance(CheckFaceQualityRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(checkFaceQualityRequest, sw);
		return sw;
	
		
	}

}
