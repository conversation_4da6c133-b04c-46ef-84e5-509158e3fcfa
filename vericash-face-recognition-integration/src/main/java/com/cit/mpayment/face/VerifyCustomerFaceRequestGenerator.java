package com.cit.mpayment.face;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Properties;

import org.apache.commons.codec.binary.Base64;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;
import com.daon.ws.identityx.CaptureMetaData;
import com.daon.ws.identityx.CaptureMetaDataItem;
import com.daon.ws.identityx.CaptureScriptResults;
import com.daon.ws.identityx.CaptureStepDataItem;
import com.daon.ws.identityx.CaptureStepResult;
import com.daon.ws.identityx.DeviceInfo;
import com.daon.ws.identityx.VerifyIdentityRequest;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class VerifyCustomerFaceRequestGenerator {
		
	private Properties faceRecognitionProperties;
	public static final String MANDATORY_FIELDS_ARE_REQUIRERD="VAL01056";

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		String deviceIdentifier=null;
		String imei=null;

			validateMandatoryFields(businessMessage);
			String identificationKey=businessMessage.getPrimarySenderInfo().getWalletShortCode()+businessMessage.getPrimarySenderInfo().getMsisdn();
			imei=businessMessage.getPrimarySenderInfo().getImei();
			deviceIdentifier=identificationKey+"/"+imei;	
			String signedChallenge=businessMessage.getPrimarySenderInfo().getFaceDetails().getSignedChallenge();

			VerifyIdentityRequest verifyFaceRequest=new VerifyIdentityRequest();
			verifyFaceRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
			verifyFaceRequest.setServiceProviderIID(faceRecognitionProperties.getProperty("SERVICE_PROVIDER_IDENTIFIER"));
			verifyFaceRequest.setPolicyIdentifier(FaceRecognitionUtil.POLICY_IDENTIFIER);
			
			DeviceInfo deviceInfo=new DeviceInfo();
			deviceInfo.setDeviceIdentifier(deviceIdentifier);
			deviceInfo.getDeviceData().add(FaceRecognitionUtil.getCababilites());
			verifyFaceRequest.setDeviceInfo(deviceInfo);
			
						
			CaptureScriptResults captureScriptResult_1=new CaptureScriptResults();
			captureScriptResult_1.setCaptureScriptIdentifier(FaceRecognitionUtil.CAPTURE_SCRIPT_IDENTIFIER);
			captureScriptResult_1.getCaptureStepResult().add(getCaptureStepResult(signedChallenge, deviceIdentifier));
			verifyFaceRequest.getCaptureScriptResults().add(captureScriptResult_1);
			
			CaptureScriptResults captureScriptResult_2=new CaptureScriptResults();
			captureScriptResult_2.setCaptureScriptIdentifier(FaceRecognitionUtil.CAPTURE_SCRIPT_IDENTIFIER);
			byte[] imageDecodedBase64ForOnceEncoded=Base64.decodeBase64(businessMessage.getPrimarySenderInfo().getFaceDetails().getImage());
			captureScriptResult_2.getCaptureStepResult().add(getCaptureStepFaceResult(imageDecodedBase64ForOnceEncoded));
			verifyFaceRequest.getCaptureScriptResults().add(captureScriptResult_2);
			
			JAXBContext context = JAXBContext.newInstance(VerifyIdentityRequest.class);
			Marshaller marshaller = context.createMarshaller();		
			StringWriter sw = new StringWriter();
			marshaller.marshal(verifyFaceRequest, sw);
			return sw;
	}
	private CaptureStepResult getCaptureStepFaceResult(byte[] image) {
		CaptureStepResult captureStepResult=new CaptureStepResult();
		captureStepResult.setIdentifier("face");
		
		CaptureStepDataItem dataItem=new CaptureStepDataItem();
		dataItem.setIdentifier("image");
		dataItem.getData().add(image);
		
		captureStepResult.getDataItem().add(dataItem);
		return captureStepResult;
	}
	private  CaptureStepResult getCaptureStepResult(String signedChallengeKey,String deviceIdentifier) {
		CaptureStepResult captureStepResult=new CaptureStepResult();
			captureStepResult=new CaptureStepResult();
			captureStepResult.setIdentifier("device");
			
			CaptureStepDataItem dataItem=new CaptureStepDataItem();
			dataItem.setIdentifier("key");
			
			CaptureMetaDataItem signatureAlgorithm=new CaptureMetaDataItem();
			signatureAlgorithm.setName("ChallengeSignatureAlgorithm");
			signatureAlgorithm.setValue("RSA");
			
			CaptureMetaDataItem signedChallenge=new CaptureMetaDataItem();
			signedChallenge.setName("SignedChallenge");
			signedChallenge.setValue(signedChallengeKey);
			
			CaptureMetaDataItem challengeKeyIdentifier=new CaptureMetaDataItem();
			challengeKeyIdentifier.setName("ChallengeKeyIdentifier");
			challengeKeyIdentifier.setValue(deviceIdentifier);
					
			
			CaptureMetaData metaData=new CaptureMetaData();
			metaData.getMetaDataItem().add(signatureAlgorithm);
			metaData.getMetaDataItem().add(signedChallenge);
			metaData.getMetaDataItem().add(challengeKeyIdentifier);
			dataItem.setMetaData(metaData);
			
			captureStepResult.getDataItem().add(dataItem);
			
		return captureStepResult;
	}
	
	private boolean validateMandatoryFields(BusinessMessage businessMessage) throws GeneralFailureException{
		HashMap<String, String> vars = new HashMap<String, String>();
		if(businessMessage.getPrimarySenderInfo().getWalletShortCode()==null){
			vars.put("attribute","Wallet Short Code");
		    throw new GeneralFailureException(MANDATORY_FIELDS_ARE_REQUIRERD, vars);
		}
		if(businessMessage.getPrimarySenderInfo().getMsisdn()==null){
			vars.put("attribute","Phone Number");
		    throw new GeneralFailureException(MANDATORY_FIELDS_ARE_REQUIRERD, vars);
		}
		if(businessMessage.getPrimarySenderInfo().getImei()==null){
			vars.put("attribute","IMEI");
		    throw new GeneralFailureException(MANDATORY_FIELDS_ARE_REQUIRERD, vars);
		}
		if(businessMessage.getPrimarySenderInfo().getFaceDetails()==null){
			vars.put("attribute","Face Detials");
		    throw new GeneralFailureException(MANDATORY_FIELDS_ARE_REQUIRERD, vars);
		}
		if(businessMessage.getPrimarySenderInfo().getFaceDetails().getSignedChallenge()==null){
			vars.put("attribute","Signed Challenge");
		    throw new GeneralFailureException(MANDATORY_FIELDS_ARE_REQUIRERD, vars);
		}		
		return true;
	}
	
	public Properties getFaceRecognitionProperties() {
		return faceRecognitionProperties;
	}
	public void setFaceRecognitionProperties(Properties faceRecognitionProperties) {
		this.faceRecognitionProperties = faceRecognitionProperties;
	}
	
}
