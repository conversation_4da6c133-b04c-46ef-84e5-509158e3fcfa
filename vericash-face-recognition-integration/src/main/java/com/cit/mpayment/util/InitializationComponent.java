package com.cit.mpayment.util;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class InitializationComponent{
	 

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		String uniqueIdentifier = businessMessage.getPrimarySenderInfo().getWalletShortCode()
				+ businessMessage.getPrimarySenderInfo().getMsisdn();
		
		businessMessage.getSoftFields().put("ProfileIdentifier", uniqueIdentifier);
		businessMessage.getSoftFields().put("CustomerIdentifier", uniqueIdentifier);
		
		businessMessage.getSoftFields().put("DeviceIdentifier", uniqueIdentifier 
				+ "/" + businessMessage.getPrimarySenderInfo().getImei());
		businessMessage.getSoftFields().put("ChallengeKeyIdentifier", uniqueIdentifier 
				+ "/" + businessMessage.getPrimarySenderInfo().getImei());
		
		return businessMessage;
	}

}
