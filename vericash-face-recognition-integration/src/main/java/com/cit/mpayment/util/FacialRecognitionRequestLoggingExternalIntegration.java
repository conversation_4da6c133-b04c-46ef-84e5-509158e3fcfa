package com.cit.mpayment.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;

import org.apache.commons.io.IOUtils;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.interceptor.LoggingOutInterceptor;
import org.apache.cxf.io.CacheAndWriteOutputStream;
import org.apache.cxf.io.CachedOutputStream;
import org.apache.cxf.io.CachedOutputStreamCallback;
import org.apache.cxf.message.Message;
import org.mule.api.MuleEvent;
import org.mule.api.transport.PropertyScope;


public class FacialRecognitionRequestLoggingExternalIntegration extends LoggingOutInterceptor {	
	@Override
    public void handleMessage(Message message) throws Fault {
        OutputStream out = message.getContent(OutputStream.class);
        final CacheAndWriteOutputStream newOut = new CacheAndWriteOutputStream(out);
        message.setContent(OutputStream.class, newOut);
        newOut.registerCallback(new LoggingCallback(message));
    }

    public class LoggingCallback implements CachedOutputStreamCallback {
    	private Message message = null;
    	public LoggingCallback(Message message){
    		this.message = message;
    	}
        public void onFlush(CachedOutputStream cos) {
        }

        public void onClose(CachedOutputStream cos) {
            try {
                StringBuilder builder = new StringBuilder();
                Date requestDate = new Date();
                cos.writeCacheTo(builder, limit);
                String faceRecognitionWithoutImageBase64=extractImageData(builder.toString());
                ((MuleEvent)message.getContextualProperty("mule.event")).getMessage().setProperty("requestSOAPMessage", faceRecognitionWithoutImageBase64, PropertyScope.INVOCATION);
                ((MuleEvent)message.getContextualProperty("mule.event")).getMessage().setProperty("requestSOAPMessageDate", requestDate, PropertyScope.INVOCATION);
            } catch (Exception e) {
            	((MuleEvent)message.getContextualProperty("mule.event")).getMessage().setProperty("soapFault", "500: " +e.getMessage(), PropertyScope.INVOCATION);
            }
        }
    }


	private String extractImageData(String faceRecognitionRequest) {
		String newPartOfRequest="image</iden:Identifier><iden:Data>image base64</iden:Data></iden:Data";
		String regex="image(.*)\\D(.*)\\d(.*)\n(.*):Data";
		return faceRecognitionRequest.replaceAll(regex, newPartOfRequest);
	}
}
