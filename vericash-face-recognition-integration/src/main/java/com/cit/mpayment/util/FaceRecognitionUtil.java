package com.cit.mpayment.util;

import com.daon.ws.de.GenericRequestParameters;
import com.daon.ws.identityx.DeviceData;


public final class FaceRecognitionUtil {
	public static final String POLICY_IDENTIFIER="Face";
	public static final String CAPTURE_SCRIPT_IDENTIFIER="identityx";
	
	private static DeviceData deviceData;
	private static GenericRequestParameters genericRequestParameters;
	
	private FaceRecognitionUtil(){
	}
	
	public static final DeviceData getCababilites(){
		if(deviceData == null){
			deviceData = new DeviceData();
			deviceData.setName("Capabilities");
			deviceData.setValue("{\"activityList\":[\"device\",\"pin\",\"face\",\"voice\"]}");
		}
		return deviceData;
	}
	
	public static final GenericRequestParameters getGenericRequestParameters(){
		if(genericRequestParameters == null){
			genericRequestParameters = new GenericRequestParameters();
			genericRequestParameters.setApplicationIdentifier("Vericash-MOB");
			genericRequestParameters.setApplicationUserIdentifier("Vericash-USER");
		}
		return genericRequestParameters;
	}

	

}
