package com.cit.mpayment.profile;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.EnrollProfileResponse;

public class EnrollProfileResponseValidator {

	public Object onCall(BusinessMessage businessMessage, EnrollProfileResponse enrollProfileResponse) throws Exception {


		ResponseStatus responseStatus = enrollProfileResponse.getResponseStatus();
		
		if (responseStatus.getReturnCode() != FaceRecognitionStatusCodes.SUCCESS) {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());

		}

		return businessMessage;

	}
}
