package com.cit.mpayment.profile;

import java.io.StringWriter;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.CaptureMetaData;
import com.daon.ws.identityx.CaptureMetaDataItem;
import com.daon.ws.identityx.CaptureScriptResults;
import com.daon.ws.identityx.CaptureStepDataItem;
import com.daon.ws.identityx.CaptureStepResult;
import com.daon.ws.identityx.DeviceInfo;
import com.daon.ws.identityx.EnrollProfileRequest;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class EnrollProfileRequestGenerator{
		
	public Object onCall(BusinessMessage businessMessage) throws Exception {

		String challengeKeyValue = null;
		if(businessMessage.getPrimarySenderInfo().getFaceDetails() != null &&
				(challengeKeyValue = businessMessage.getPrimarySenderInfo().getFaceDetails().getChallengeKey()) == null)
			throw new NullPointerException();
		
		
		CaptureMetaDataItem challengeKey = new CaptureMetaDataItem();
		challengeKey.setName("ChallengeKey");
		challengeKey.setValue(challengeKeyValue);
		
		CaptureMetaDataItem challengeKeyIdentifier = new CaptureMetaDataItem();
		challengeKeyIdentifier.setName("ChallengeKeyIdentifier");
		challengeKeyIdentifier.setValue((String) businessMessage.getSoftFields().get("ChallengeKeyIdentifier"));
		
		CaptureMetaDataItem challengeKeyAlgorithm = new CaptureMetaDataItem();
		challengeKeyAlgorithm.setName("ChallengeKeyAlgorithm");
		challengeKeyAlgorithm.setValue("RSA");
		
		CaptureMetaData captureMetaData = new CaptureMetaData();
		captureMetaData.getMetaDataItem().add(challengeKey);
		captureMetaData.getMetaDataItem().add(challengeKeyIdentifier);
		captureMetaData.getMetaDataItem().add(challengeKeyAlgorithm);
		
		CaptureStepDataItem dataItem = new CaptureStepDataItem();
		dataItem.setIdentifier("key");
		dataItem.setMetaData(captureMetaData);
				
		CaptureStepResult captureStepResult = new CaptureStepResult();
		captureStepResult.setIdentifier("device");
		captureStepResult.getDataItem().add(dataItem);
		
		CaptureScriptResults captureScriptResults = new CaptureScriptResults();
		captureScriptResults.setCaptureScriptIdentifier("identityx");
		captureScriptResults.getCaptureStepResult().add(captureStepResult);
		
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceIdentifier((String) businessMessage.getSoftFields().get("DeviceIdentifier"));
		deviceInfo.getDeviceData().add(FaceRecognitionUtil.getCababilites());
		
		EnrollProfileRequest enrollProfileRequest = new EnrollProfileRequest();
		enrollProfileRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		enrollProfileRequest.setDeviceInfo(deviceInfo);
		enrollProfileRequest.setProfileID((String) businessMessage.getSoftFields().get("ProfileIdentifier"));
		enrollProfileRequest.getCaptureScriptResults().add(captureScriptResults);

		JAXBContext context = JAXBContext.newInstance(EnrollProfileRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(enrollProfileRequest, sw);
		return sw;
	
		
	}

}
