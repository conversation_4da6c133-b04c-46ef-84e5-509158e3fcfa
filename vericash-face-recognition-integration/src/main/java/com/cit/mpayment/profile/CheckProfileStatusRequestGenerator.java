package com.cit.mpayment.profile;

import java.io.StringWriter;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.CheckProfileStatusRequest;
import com.daon.ws.identityx.DeviceInfo;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class CheckProfileStatusRequestGenerator {
	 	

	public Object onCall(BusinessMessage businessMessage) throws Exception {


		CheckProfileStatusRequest checkProfileStatusRequest = new CheckProfileStatusRequest();
		
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceIdentifier((String) businessMessage.getSoftFields().get("DeviceIdentifier"));	
		
		checkProfileStatusRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		checkProfileStatusRequest.setProfileID((String) businessMessage.getSoftFields().get("ProfileIdentifier"));
		checkProfileStatusRequest.setDeviceInfo(deviceInfo);
		
		JAXBContext context = JAXBContext.newInstance(CheckProfileStatusRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(checkProfileStatusRequest, sw);
		return sw;
	
		
	}

}

