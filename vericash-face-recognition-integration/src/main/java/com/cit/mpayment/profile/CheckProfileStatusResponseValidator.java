package com.cit.mpayment.profile;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.CheckProfileStatusResponse;
import com.daon.ws.identityx.CheckProfileStatusResponse.ResponseData;

public class CheckProfileStatusResponseValidator {

	public Object onCall(BusinessMessage businessMessage, CheckProfileStatusResponse checkProfileStatusResponse) throws Exception {


		ResponseStatus responseStatus = checkProfileStatusResponse.getResponseStatus();
		ResponseData responseData = checkProfileStatusResponse.getResponseData();

		if (responseStatus.getReturnCode() == FaceRecognitionStatusCodes.SUCCESS) {

			businessMessage.getSoftFields().put("exisiting_profile", responseData.isProfileExists());

		} else {

			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());

		}

		return businessMessage;

	}
}
