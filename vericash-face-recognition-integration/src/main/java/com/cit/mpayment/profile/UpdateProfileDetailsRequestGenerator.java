package com.cit.mpayment.profile;

import java.io.StringWriter;

import com.cit.mpayment.util.FaceRecognitionUtil;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.identityx.DeviceInfo;
import com.daon.ws.identityx.ImageType;
import com.daon.ws.identityx.UpdateProfileDetailsRequest;
import com.daon.ws.identityx.UpdateProfileDetailsRequest.FaceData;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Marshaller;

public class UpdateProfileDetailsRequestGenerator  {
	 	

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setDeviceIdentifier((String) businessMessage.getSoftFields().get("DeviceIdentifier"));
		deviceInfo.getDeviceData().add(FaceRecognitionUtil.getCababilites());
		byte[] rawImageBytes = (byte[]) businessMessage.getSoftFields().get("face_bytes");
		FaceData faceData = new FaceData();
		faceData.setDeleteOnly(false);
		faceData.setImageType(ImageType.JPG);
		faceData.setData(rawImageBytes);
		
		
		UpdateProfileDetailsRequest updateProfileDetailsRequest = new UpdateProfileDetailsRequest();
		updateProfileDetailsRequest.setGenericRequestParameters(FaceRecognitionUtil.getGenericRequestParameters());
		updateProfileDetailsRequest.setDeviceInfo(deviceInfo);
		updateProfileDetailsRequest.setFaceData(faceData);
		
		JAXBContext context = JAXBContext.newInstance(UpdateProfileDetailsRequest.class);
		Marshaller marshaller = context.createMarshaller();		
		StringWriter sw = new StringWriter();
		marshaller.marshal(updateProfileDetailsRequest, sw);
		return sw;
	
		
	}
	
	
}

