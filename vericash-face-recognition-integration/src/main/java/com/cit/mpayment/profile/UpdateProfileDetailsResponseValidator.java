package com.cit.mpayment.profile;

import com.cit.mpayment.util.FaceRecognitionStatusCodes;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.daon.ws.de.ResponseStatus;
import com.daon.ws.identityx.UpdateProfileDetailsResponse;

public class UpdateProfileDetailsResponseValidator  {

	public Object onCall(BusinessMessage businessMessage, UpdateProfileDetailsResponse updateProfileDetailsResponse) throws Exception {


		ResponseStatus responseStatus = updateProfileDetailsResponse.getResponseStatus();

		if (responseStatus.getReturnCode() != FaceRecognitionStatusCodes.SUCCESS) {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode(String.valueOf(responseStatus.getReturnCode()));
			businessMessage.getStatus().setStatusMsg(responseStatus.getDescription());

		}
		businessMessage.getPrimarySenderInfo().getFaceDetails().setImage(null);
		businessMessage.getSoftFields().remove("face_data");
		
		return businessMessage;
		
	}
}
