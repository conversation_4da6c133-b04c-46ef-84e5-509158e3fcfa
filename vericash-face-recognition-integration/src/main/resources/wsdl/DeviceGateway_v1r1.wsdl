<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:schema="http://www.daon.com/ws/identityx" xmlns:tns="http://www.daon.com/ws/identityx/DeviceGateway" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://www.daon.com/ws/identityx/DeviceGateway">
  <wsdl:types>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.daon.com/ws/identityx" xmlns:wsde="http://www.daon.com/ws/de" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://www.daon.com/ws/identityx" elementFormDefault="qualified" jaxb:version="2.0">
      <xsd:import namespace="http://www.daon.com/ws/de" />
      <xsd:simpleType name="TransactionReturnType">
        <xsd:annotation>
          <xsd:documentation>Type of Transaction data.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="OTP">
            <xsd:annotation>
              <xsd:documentation>Return type is Authenticate code</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VIRTUALCARD">
            <xsd:annotation>
              <xsd:documentation>Return type is Virtual card details.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="OTHER">
            <xsd:annotation>
              <xsd:documentation>Other type</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="ListServiceProvidersResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to list service providers</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for a call to list service providers.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="ServiceProviderInfo" type="ServiceProviderInfo" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>Service Provider information for the user bound to this service provider</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="VirtualCardDetails">
        <xsd:annotation>
          <xsd:documentation>Contains the user's virtual card details</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="CardNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Virtual credit card number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ExpirationDate" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Expiration date of the card</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CVV" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Card verification value</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="Modality">
        <xsd:annotation>
          <xsd:documentation>Information about a modality.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Type" type="ModalityType" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Type of modality whether face, voice etc</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Scores" type="Scores" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Score information of each modality used in the biometric fusion</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ListTransactionResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to list transactions</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for a call to list transactions.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="TransactionInfo" type="TransactionInfo" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>Transaction information for the user defined by the service provider</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="Policy">
        <xsd:annotation>
          <xsd:documentation>Contains the policy attributes defined by service provider</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="PossessPhone" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if a user needs to possesses the phone
						for verifcation</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PolicyIdentifier" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Policy identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PolicyIID" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Internal Policy IID, only to be used by AdminGateway</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PolicyDescription" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Description of the policy</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Internal Service Provider IID</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderUniqueID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider Unique ID, different from ServiceProviderIID (usually a name)</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationType" type="VerificationType" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Denotes the Verifcation Type(s) that can be used for a policy</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsFaceRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Face needs to be captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsVoiceRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Voice needs to be captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsPalmRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Palm needs to be captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsFaceLivenessRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Face Liveness needs to be considered.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsVoiceLivenessRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Voice Liveness needs to be considered.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NumberOfVoiceSample" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes voice sample count needed.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsGPSCoordinatesRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if GPS coordinates needs to be captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsPINRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if PIN needs to be captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsDuressPINAllowed" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if a Duress PIN is captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsOffline" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if a device is offline and 
            			wants to use a time based token.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsOfflineWithPin" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if a device is offline and 
            			wants to use a time based token with pin.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsEnrollmentPolicy" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the policy is the default enrollment policy.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsSponsorshipRequired" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Sponsorship is Required.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NumberOfRetryAttempts" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes how many times a user can retry to verify transaction.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="EnrollmentUtterance" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes what a user must say to enroll or verify.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsPolicyBlocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the Policy is blocked from use or not.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ExpiredTimestamp" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>When the policy expires.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SharedSecretTTL" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the number of days the shared secret is valid</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NumberOfOTPRetryAttempts" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes how many times a user can retry to verify OTP.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="OTPConfiguration" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>JSON string specifying length and allowable characters for one time password.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="FactorsDefinition" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Contains a list of various data collection options using
						Scripts</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ActivityDefinition" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Contains a JSON definition of activity options during enrollment and verification</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ProcessDataRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the transactions to be validated for a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Device Info</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Token" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Sponsorship Token.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Language" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>The desired language for the profile</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="EnrollProfileForIdentityXLiteResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for enrolling to IdentityXLite sponsored by a service provider.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse" />
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="Certificate">
        <xsd:sequence>
          <xsd:element minOccurs="0" name="CertificateType" type="CertificateType" />
          <xsd:element minOccurs="0" name="CertificateData" type="xsd:base64Binary" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="CheckFaceQualityAndLivenessResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response for face quality and liveness check.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for face quality check.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="FaceQualityCheckPassed" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the face quality is sufficient.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="FaceLivenessCheckPassed" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the face liveness check is passed.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="CertificateType">
        <xsd:annotation>
          <xsd:documentation>Type of certificate being passed back for the service provider</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="P12">
            <xsd:annotation>
              <xsd:documentation>.p12 Certificate/Private Key combination</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="CER">
            <xsd:annotation>
              <xsd:documentation>Just a certificate</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="CheckDeviceStatusRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the device bound status.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Contains the Device details.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceToken" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Token or code to Enable another IdentityX device for a profile</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Language" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>The desired language for the profile</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ApplicationType" type="ApplicationType" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Contains the Application type</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="RefreshKeysResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the device bound status.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="SharedSecret" type="xsd:base64Binary" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>The Shared Secret for the client and server.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ExpirationTimestamp" type="xsd:dateTime" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>The date and time the shared secret expires.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="VerifyTransactionRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to verify a transaction of a service provider user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CustomerIID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the user within the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Pin" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code presented at enrollment time</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Face" type="Face" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Face biometric data with the type of face image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Voice" type="Voice" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Video" type="Video" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Type of the data stream of video. Denotes MPEG4,MOV,AVI,etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Palm" type="Palm" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Palm biometric data with the type of image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="CheckVoiceLivenessResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response for voice liveness check.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for voice liveness check.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="VoiceLivenessCheckPassed" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the voice liveness check passed.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ProcessTransactionDataBlockRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the transactions to be validated for a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Device Info</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="Palm">
        <xsd:annotation>
          <xsd:documentation>Contains the Palm information.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="BiometricData">
            <xsd:sequence>
              <xsd:element name="ImageType" type="ImageType" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>Type of the image. Denotes JPG or BMP or JPG2000 etc</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="VerifyFaceRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to verify a face data of a service provider user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="FaceSamples" type="Face" minOccurs="1" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Live Face data with several face images captured with micro-movements.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetServerTimeRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Servers as a ping operation to ensure the web service is available.
					Returns the current date and time on the server.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest" />
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="DetailedTransaction">
        <xsd:annotation>
          <xsd:documentation>Contains the transaction information for the user.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ServiceProviderID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Unique name of the service provicer</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderUserID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Unqiue name of the service provider user</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Unique name of the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PolicyID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The policy unique name associated with the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProfileID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The profile unique name associated with the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionDescription" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes the transaction requested</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ExpirationTimestamp" type="xsd:dateTime" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes the date the transaction expires</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsGPSUsed" type="xsd:boolean" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Will GPS be accounted for with the transation</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationCheckLongitude" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The longitude of where the transaction must be verified</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationCheckLatitude" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The latitude of where the transaction must be verified</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationCheckRadius" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The radius a transaction may be verified from center.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AvailableRetries" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The retries remaining for the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProcessedTimestamp" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The date the transaction was made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationResult" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Whether or not the transaction has been verified</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isNotifyServiceProviderUser" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Does the user get notified when the transaction is made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isNotifyServiceProvider" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Does the service provider get notified when the transaction is made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isSMSTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is SMS used for the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isOATHTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is OATH used for the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isOTPTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Does a one time password get generated with the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isVirtualCardTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is this a virtual card transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SMSMessageID" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The ID where the SMS message will be sent</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TotalRetriesAllowed" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>How many retries are allowed for a transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isTransactionComplete" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes if the transaction has been executed</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Utterance" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The utterance assocaited with a transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsTransactionBlocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the Transaction is blocked from use or not.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="CheckVoiceLivenessRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to liveness of the user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CustomerIID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the user within the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="textDependentVoice" type="Voice" minOccurs="1" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data with format of the data for text dependent or static text</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="textIndependentVoice" type="Voice" minOccurs="1" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data with format of the data for text independent or dynamic text</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="SponsorDeviceForProfileResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object to Add Device</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for GenerateDeviceEnablementCode operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="Token" type="xsd:string">
                        <xsd:annotation>
                          <xsd:documentation>Token generated on the server for a new Device to be added.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="QRCode" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>QR Code containing token generated on the server.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="DeviceType">
        <xsd:annotation>
          <xsd:documentation>Type of device.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="PHONE">
            <xsd:annotation>
              <xsd:documentation>Type of the Device is Phone</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="TABLET">
            <xsd:annotation>
              <xsd:documentation>Type of the Device is Tablet</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="OTHER">
            <xsd:annotation>
              <xsd:documentation>Other type</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="InitTransactionResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to Inittransaction</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for InitTransactionResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="TransactionInfo" type="TransactionInfo">
                        <xsd:annotation>
                          <xsd:documentation>Transaction information</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="AddDeviceResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object to Add Device</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for AddDeviceResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="SharedSecret" type="xsd:base64Binary" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>The Shared Secret for the client and server.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ExpirationTimestamp" type="xsd:dateTime" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>The date and time the shared secret expires.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="DeclineTransactionResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to decline a transaction.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse" />
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="CertificateSubjectName">
        <xsd:sequence>
          <xsd:element minOccurs="0" name="CommmonName" type="xsd:string" />
          <xsd:element minOccurs="0" name="Country" type="xsd:string" />
          <xsd:element minOccurs="0" name="Location" type="xsd:string" />
          <xsd:element minOccurs="0" name="OrganizationUnit" type="xsd:string" />
          <xsd:element minOccurs="0" name="State" type="xsd:string" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="PalmStencil">
        <xsd:annotation>
          <xsd:documentation>Return Status indicating the results of the any Identity X request</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Palm" type="Palm" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Palm data</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="AbstractIdentityXResponse">
        <xsd:annotation>
          <xsd:documentation>Basic Abstract IdentityX Response</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="wsde:AbstractResponse" />
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:simpleType name="SponsorshipType">
        <xsd:annotation>
          <xsd:documentation>Type of Sponsorship.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="IDENTITYX">
            <xsd:annotation>
              <xsd:documentation>Biometric Enrollment sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="IDENTITYXBINDUSER">
            <xsd:annotation>
              <xsd:documentation>Transaction Based Enrollment sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="SMSENROLL">
            <xsd:annotation>
              <xsd:documentation>SMS enrollment sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="IDENTITYXLITE">
            <xsd:annotation>
              <xsd:documentation>OATH based token generation sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="PHONECALLBACKENROLL">
            <xsd:annotation>
              <xsd:documentation>Phone callback enrollment</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="DEVICELIFECYCLE">
            <xsd:annotation>
              <xsd:documentation>Device Life Cycle based enrollment.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="IDENTITYXENROLL">
            <xsd:annotation>
              <xsd:documentation>Biometric Enrollment sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="DeviceInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the device information.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="DeviceIdentifier" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Device's unique identifier. For Iphone, it represents the UDID and for blackberry, 
						it corresponds to PIN.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="LogicalDeviceName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Logical name of the device. It is a common  unique identifier. For Iphone, it represents the UDID and for blackberry, 
						it corresponds to PIN.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PhoneNumber" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Phone number if the device is a phone</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CountryCode" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Country code if the device is a phone</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Latitude" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Latitude element of GPS coordinate</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Longitude" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Longitude element of GPS coordinate.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceSerialNumber" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Serial number of the device.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceType" type="DeviceType" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Type of the device. Phone or tablet etc</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceSubType" type="DeviceSubType" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Describes the sub classification of the device. Smart Phone or Feature phone if the type is Phone.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceMake" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Device Manufacturer's name</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceModel" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Model of the device.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsOathEnabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not oath has been enabled on this device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsIdentityXEnabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not IdentityX has been enabled on this device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsSMSEnabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not SMS has been enabled on this device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsNotificationEnabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not Notifications have been enabled on this device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsSMSOnly" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not this device only supports SMS</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceOSVersion" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>What operating system version is associated with this device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsDeviceBlocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the Device is blocked or not.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceFrameworkVersion" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Version number of the device framework.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsPhoneCallbackOnly" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not this device only supports Landline.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ActivationDate" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the date and time when device was bounded to the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="LastUsed" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the date and time when device was last used to process the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceFeatures" type="DeviceFeatures" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Features supported in the device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ActivationDateString" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the date and time when device was bounded to the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="LastUsedString" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the date and time when device was last used to process the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceData" type="DeviceData" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Room for any other info about the device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="Video">
        <xsd:annotation>
          <xsd:documentation>Contains the data stream information.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="BiometricData">
            <xsd:sequence>
              <xsd:element name="VideoFormat" type="VideoFormat" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>Denotes the format of the data stream.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Rotate" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether the images have to be rotated.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Audio" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether the data stream contains audio data.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="SplitAudio" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates the point at which the audio stream needs to be split</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Video" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether the data stream contains video data.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="VideoFramesPerSecond" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The number of frames per second by which the video was recorded</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Audio1Start" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The time (hh:mm:ss) at which first audio starts recording.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Audio1Duration" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The total duration time (hh:mm:ss) of the first Audio Sample.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Audio2Start" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The time (hh:mm:ss) at which second audio starts recording.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Audio2Duration" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The total duration time (hh:mm:ss) of the second Audio Sample.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="ChangeMasterKeyInfo">
        <xsd:annotation>
          <xsd:documentation>Contains information related to Changing a Master key</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="RevokedReason" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Reason of revoke or change</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="KeyLabel" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The new Key label of this key</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="SponsorDeviceForProfileRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to sponsor a device to be bound to an exisiting profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ExpirationDate" type="xsd:dateTime" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Expiration datetime in UTC.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ClientApplicationIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Indicates the client application that has to be invoked.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="termsURL" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Optional configuration that allows the user to specify a terms and conditions URL.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="AudioAttributes">
        <xsd:annotation>
          <xsd:documentation>Describes audio features of the device</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Microphone" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicated if microphone exists or not</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AudioCodecs" type="AudioCodecs" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Denotes Codecs supported</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="AdminRole">
        <xsd:annotation>
          <xsd:documentation>Type of Privileges an Administrator has.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="M">
            <xsd:annotation>
              <xsd:documentation>Master Permission</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="S">
            <xsd:annotation>
              <xsd:documentation>Service Provider Level permission only</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="U">
            <xsd:annotation>
              <xsd:documentation>Service Provider User Level permission only</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="Customer">
        <xsd:annotation>
          <xsd:documentation>Contains the Customer</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="CustomerIdentifier" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Customer Unique Identifier within the realm of service provider</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Internal Service Provider IID of the Customer</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ValidateSponsorshipCodeResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to validate sponsorship code</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for validating sponsorship code.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="Matched" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate the sponsorship code match status. 
                                    			True indicates a successful match.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="DeviceEndPointURL" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Denotes the end point for a device to be used after 
												sponsorship is complete.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ListSponsorshipResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call list the sponsorships for validation</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for list sponsorships operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="SponsorshipTransactionInfo" type="SponsorshipTransactionInfo" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>Sponsorship information for the user defined by the service provider</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="AbstractServiceProviderGatewayResponse">
        <xsd:annotation>
          <xsd:documentation>Basic Abstract Service Provider Response</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractIdentityXResponse" />
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="CameraAttributes">
        <xsd:annotation>
          <xsd:documentation>Describes camera features of the device</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Resolution" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Camera resolution</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Frontal" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes camera is front facing or not</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Orientation" type="xsd:long" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicates rotation in degrees that is required to get the image.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CameraDisabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicates camera either broken or disabled.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="CheckProfileStatusRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check if profile is used.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Profile identifier checked for availability</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Device information like device identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="VerifyIdentityRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to verify an Identity of an IdentityX profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionDescription" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction description.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="PolicyIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the policy.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Pin" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code presented at enrollment time</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Face" type="Face" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Face biometric data with the type of face image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Voice" type="Voice" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Video" type="Video" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Type of the data stream of video. Denotes MPEG4,MOV,AVI,etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Palm" type="Palm" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Palm biometric data with the type of image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ValidateDeviceEnablementCodeRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to validate Device Enablement code in order to add another device to a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceEnablementCode" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Token or code to Enable another IdentityX device for a profile</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ValidateDeviceEnablementCodeResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object to Validate Code for enabling another identityx device.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for GenerateDeviceEnablementCode operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="ProfileIdentifier" type="xsd:string">
                        <xsd:annotation>
                          <xsd:documentation>Identity or user identifier enrolled in IdentityX.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="TransactionInfo" type="TransactionInfo">
                        <xsd:annotation>
                          <xsd:documentation>Transaction information for the user defined by the service provider</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ListDevicesRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the List of devices associated with the profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="AddDeviceRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the device bound status.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Pin" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code presented at enrollment time</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Face" type="Face" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>Face biometric data with the type of image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Voice" type="Voice" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data with format of the data.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Palm" type="Palm" minOccurs="0" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>Palm biometric data with format of the data.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Token" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Sponsorship Token.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="DeviceFeatures">
        <xsd:annotation>
          <xsd:documentation>Features supported by the device.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="CameraAttributes" type="CameraAttributes" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Camera features in the device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AudioAttributes" type="AudioAttributes" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Audio capabilities in the device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VideoAttributes" type="VideoAttributes" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Video capabilities in the device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="SecurityQuestionAnswer">
        <xsd:annotation>
          <xsd:documentation>Contains the Security Question Answer Information</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Identifier" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The Security Question Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Question" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The Security Question</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Answer" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The Security Answer for Profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="EnrollProfileRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to enroll a profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Pin" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code presented at enrollment time</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DuressPin" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code presented at enrollment time for duress</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Face" type="Face" minOccurs="0" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>Face biometric data with the type of image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Voice" type="Voice" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data with format of the data.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Palm" type="Palm" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Palm biometric data with format of the data.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Language" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>The desired language for the profile</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="EnrollData" type="EnrollData" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Room for any other info about the Enrollment.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="UpdateDeviceInfoRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to update the device</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="BlockedDeviceIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>The device identifier that needs to be blocked.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="BlockedIndicator" nillable="true" type="xsd:boolean">
                  <xsd:annotation>
                    <xsd:documentation>True to indicate, a device needs to be blocked. False, otherwise.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="TransformResponse" type="TransformResponse" />
      <xsd:element name="UpdateProfileDetailsRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to update a profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Token" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Token to verify transaction before updating Profile Details.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="SponsorshipToken" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Sponsorship Token.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Transaction identifier associated with updating profile details.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Pin" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:simpleContent>
                      <xsd:extension base="xsd:string">
                        <xsd:attribute name="DeleteOnly" type="xsd:boolean" default="false" />
                      </xsd:extension>
                    </xsd:simpleContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="DuressPin" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Personal identification code for duress</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:simpleContent>
                      <xsd:extension base="xsd:string">
                        <xsd:attribute name="DeleteOnly" type="xsd:boolean" default="false" />
                      </xsd:extension>
                    </xsd:simpleContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="FaceData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Face biometric data</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="Face">
                        <xsd:attribute name="DeleteOnly" type="xsd:boolean" default="false" />
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="VoiceData" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Voice biometric data</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="Voice">
                        <xsd:attribute name="DeleteOnly" type="xsd:boolean" default="false" />
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="PalmData" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Palm biometric data</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:complexContent>
                      <xsd:extension base="Palm">
                        <xsd:attribute name="DeleteOnly" type="xsd:boolean" default="false" />
                      </xsd:extension>
                    </xsd:complexContent>
                  </xsd:complexType>
                </xsd:element>
                <xsd:element name="CaptureScriptResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>The results of the capture script.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Language" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>The desired language for the profile</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="CaptureStepResult">
        <xsd:annotation>
          <xsd:documentation>Contains the result of a single step within a Capture Script</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Identifier" type="xsd:string" />
          <xsd:element name="MetaData" type="CaptureMetaData" minOccurs="0" />
          <xsd:element name="DataItem" type="CaptureStepDataItem" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="BiometricData">
        <xsd:annotation>
          <xsd:documentation>Contains the biometric data.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Data" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Contains the biometric data</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="CheckFaceLivenessRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the quality of face image as well as liveness of the user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="FullFaceFrontalSample" type="Face">
                  <xsd:annotation>
                    <xsd:documentation>Face data with face looking directly into the camera.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="LiveFaceSamples" type="Face" minOccurs="1" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Live Face data with several face images captured with micro-movements.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ProcessingResult">
        <xsd:sequence>
          <xsd:element name="Identifier" type="xsd:string" />
          <xsd:element name="MetaData" type="NameValuePair" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="Version">
        <xsd:annotation>
          <xsd:documentation>Type for Server's version number which the client is written against.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="V4_1" />
          <xsd:enumeration value="V4_0" />
          <xsd:enumeration value="V3_2_3" />
          <xsd:enumeration value="V3_2_2" />
          <xsd:enumeration value="V3_2_1" />
          <xsd:enumeration value="V3_2" />
          <xsd:enumeration value="V3_1" />
          <xsd:enumeration value="V3_0" />
          <xsd:enumeration value="V2_3" />
          <xsd:enumeration value="V2_2" />
          <xsd:enumeration value="V2_1" />
          <xsd:enumeration value="V2_0" />
          <xsd:enumeration value="V1_5" />
          <xsd:enumeration value="V1_4" />
          <xsd:enumeration value="V1_3_1" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="CheckFaceQualityRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the quality of face image.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="Face" type="Face">
                  <xsd:annotation>
                    <xsd:documentation>Face biometric data with the type of image</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionID" minOccurs="0" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" minOccurs="0" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="VerifyIdentityResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to verify the transaction</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for VerifyIdentityResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="Verified" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the Verification Succeeded</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="BiometricScoreInfo" type="BiometricScoreInfo" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Biometric score information</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="TransactionIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Identifier to uniquely identify the transaction whose verification attempts we
												want.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="AuthenticationCode" type="xsd:string" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>One time password to complete the transaction</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Identity or user identifier of the enrolled profile in IdentityX. 
                        						This is unique in IdentityX Realm.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="EnrollmentPolicy">
        <xsd:annotation>
          <xsd:documentation>Contains Enrollment Policy related Info</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="Policy">
            <xsd:sequence>
              <xsd:element name="IsPinCaptured" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether Pin Data has been collected</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="IsDuressPinCaptured" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether Duress Pin Data has been collected</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="IsGPSCaptured" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether GPS Data has been collected</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="IsFaceCaptured" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether Face Data has been captured</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="IsPalmCaptured" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether Palm Data has been captured</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="IsVoiceCaptured" type="xsd:boolean" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Indicates whether Voice Data has been captured</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="CaptureScript" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                  <xsd:documentation>The capture scripts to send to the device</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:simpleType name="DeviceSubType">
        <xsd:annotation>
          <xsd:documentation>Sub Type of device.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="SMARTPHONE">
            <xsd:annotation>
              <xsd:documentation>Type of the Device is Smartphone</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="FEATUREPHONE">
            <xsd:annotation>
              <xsd:documentation>Type of the Device is FeaturePhone</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="OTHER">
            <xsd:annotation>
              <xsd:documentation>Other type</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="AbstractAdminGatewayRequest">
        <xsd:annotation>
          <xsd:documentation>Request object from which all other Admin Gateway requests inherit</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractIdentityXRequest">
            <xsd:sequence>
              <xsd:element name="SecureProxyIdentifier" type="xsd:string">
                <xsd:annotation>
                  <xsd:documentation>Identifier to uniquely identify the application calling the Admin Gateway.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="AdministratorProfileIdentifier" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>The Administrator who made the request to the admin gateway</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="CheckProfileStatusResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response to check if profile is used.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for CheckProfileStatusResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="ProfileExists" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if profile is already used.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="Profile">
        <xsd:annotation>
          <xsd:documentation>Contains the details of a profile.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ProfileUniqueIdentifier" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Profile identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProfileIID" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Profile internal identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProfileName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Profile Name</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Email" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Email address associated with the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="FirstName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>First Name</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="LastName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Last Name</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="MiddleName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Middle Name</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Prefix" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Prefix</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Suffix" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Suffix</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryPhone" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Phone Number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryPhoneCountryCode" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Country code for the primary phone number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AlternatePhone" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Alternate Phone Number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AlternatePhoneCountryCode" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Country code for the alternate phone number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsOathEnabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not oath has been enabled with this profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsIdentityXEnabled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not Identity X has been enabled</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Address" type="Address">
            <xsd:annotation>
              <xsd:documentation>Complete Address</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceInfo" type="DeviceInfo" minOccurs="1" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Information stored on the server for the devices associated with this profile.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="EnrollmentDateTime" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>When the profile was enrolled into Identity X</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="OathSubscriptionDateTime" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>When the profile enabled oath capabilities</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PIN" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>The Pin number for the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DuressPIN" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>The Duress Pin for the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsProfileBlocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the Profile is blocked from use or not.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Face" type="Face">
            <xsd:annotation>
              <xsd:documentation>Face biometric data with the type of image</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VoiceSamples" type="Voice" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Voice biometric data with format of the data.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:sequence>
            <xsd:element name="Voice" type="Voice">
              <xsd:annotation>
                <xsd:documentation>Voice biometric data with format of the data.</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
          <xsd:sequence minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="Palm" type="Palm">
              <xsd:annotation>
                <xsd:documentation>Palm biometric data with format of the data.</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
          <xsd:sequence minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="Customers" type="Customer">
              <xsd:annotation>
                <xsd:documentation>Any Service Provider User ID's attached to the profile that can be known about</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
          <xsd:element name="IsPinEnrolled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not PIN has been collected for this profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsDuressPinEnrolled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not DURESS PIN has been collected for this profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsFaceEnrolled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not FACE data has been collected for this profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsVoiceEnrolled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not Voice data has been collected for this profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsPalmEnrolled" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not Palm data has been collected for this profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ArchivedDate" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the date and time when the profile was archived</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ArchivedDateString" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes the date and time when the profile was archived</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Language" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>The desired language for the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="CaptureStepDataItem">
        <xsd:annotation>
          <xsd:documentation>A single piece of data captured by the capture client</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Identifier" type="xsd:string" minOccurs="0" />
          <xsd:element name="MetaData" type="CaptureMetaData" minOccurs="0" />
          <xsd:element name="Data" type="xsd:base64Binary" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="EnrollProfileForIdentityXLiteRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object for enrolling to IdentityXLite sponsored by a service provider.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="SponsorCode" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>The Sponsorship code required at enrollment time</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="CaptureMetaDataItem">
        <xsd:annotation>
          <xsd:documentation>A single element of Meta data</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Name" type="xsd:string" />
          <xsd:element name="Value" type="xsd:string" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="RefreshKeysRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the device bound status.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="AuthenticationCode" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>One time password to perform this operation</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetPalmStencilResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for the call to the getPalmStencil Operation</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data to get the palm stencil</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="PalmStencil" type="PalmStencil" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>The palm stencil data</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="DataItem">
        <xsd:complexContent>
          <xsd:extension base="BiometricData">
            <xsd:sequence>
              <xsd:element name="Type" type="xsd:string" />
              <xsd:element name="MetaData" type="NameValuePair" minOccurs="0" maxOccurs="unbounded" />
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="Address">
        <xsd:annotation>
          <xsd:documentation>Contains the contact information details</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="AddressLine1" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Address line 1</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AddressLine2" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Address line 2</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="City" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Name of the City</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="State" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Name of the state</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PostalCode" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Zip or postal code</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Country" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Name of the Country</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="UpdatePushTokenResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for device token</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for UpdatePushTokenResponse operation.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="SponsorshipTransactionInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the sponsorship transaction information.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ServiceProviderName" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Name of the service provider</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIcon" type="xsd:base64Binary" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Providers's icon or logo</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderUserIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider's customer or user Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SponsorshipTransactionID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>ServiceProvider sponsorship transaction identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="ImageType">
        <xsd:annotation>
          <xsd:documentation>Type of Image.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="JPG">
            <xsd:annotation>
              <xsd:documentation>JPG Image</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="BMP">
            <xsd:annotation>
              <xsd:documentation>BMP Image</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="JPG2000">
            <xsd:annotation>
              <xsd:documentation>JPG2000 Image</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="OTHER">
            <xsd:annotation>
              <xsd:documentation>Other image type</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:simpleType name="ApplicationType">
        <xsd:annotation>
          <xsd:documentation>Type of application.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="IDENTITYX">
            <xsd:annotation>
              <xsd:documentation>Biometric Enrollment sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="IDENTITYXLITE">
            <xsd:annotation>
              <xsd:documentation>OATH based token generation sponsorship</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="EnrollProfileResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object to enroll a profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for EnrollProfileResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="SharedSecret" type="xsd:base64Binary" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>The Shared Secret for the client and server.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ExpirationTimestamp" type="xsd:dateTime" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>The date and time the shared secret expires.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ResponseScripts" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The scripts to send to the device to capture any further information.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="VideoFormat">
        <xsd:annotation>
          <xsd:documentation>Format of video data.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="MPEG4">
            <xsd:annotation>
              <xsd:documentation>MPEG4 format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="MOV">
            <xsd:annotation>
              <xsd:documentation>MOV format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="AVI">
            <xsd:annotation>
              <xsd:documentation>AVI format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="OTHER">
            <xsd:annotation>
              <xsd:documentation>Other format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:simpleType name="ModalityType">
        <xsd:annotation>
          <xsd:documentation>Descibes the modality used</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="PIN">
            <xsd:annotation>
              <xsd:documentation>PIN</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="FACE">
            <xsd:annotation>
              <xsd:documentation>Face Biometric</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VOICE">
            <xsd:annotation>
              <xsd:documentation>Voice Biometric</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="PALM">
            <xsd:annotation>
              <xsd:documentation>Palm Biometric</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="FACELIVENESS">
            <xsd:annotation>
              <xsd:documentation>Face Liveness Biometric</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VOICELIVENESS">
            <xsd:annotation>
              <xsd:documentation>Voice Liveness Biometric</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="AbstractDeviceGatewayResponse">
        <xsd:annotation>
          <xsd:documentation>Basic Abstract Device Response</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractIdentityXResponse" />
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="DeviceData">
        <xsd:sequence>
          <xsd:element name="Name" type="xsd:string" />
          <xsd:element name="Value" type="xsd:string" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="TransactionInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the transaction information for the user.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ServiceProviderName" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Name of the service provider</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIcon" type="xsd:base64Binary" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Service Providers's icon or logo</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderUserIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider's customer or user Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderTransactionID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>ServiceProvider transaction identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Policy" type="Policy" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes the type of data to be captured.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionDescription" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes the type of transaction to be validated.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Utterance" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Text for voice verification.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="FaceLivenessInstructions" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Instruction for face liveness.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationCheckLongitude" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The longitude of where the transaction must be verified</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationCheckLatitude" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The latitude of where the transaction must be verified</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationCheckRadius" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The radius a transaction may be verified from center.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProfileIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Profile internal identifier. Though deprecated, this identifier is passed back to ensure backwards compatibility.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PolicyIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Policy internal identifier. Though deprecated, this identifier is passed back to ensure backwards compatibility.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ExpirationTimestamp" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The date and time the transaction expires.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AvailableRetries" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The retries remaining for the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsGPSUsed" type="xsd:boolean" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Will GPS be accounted for with the transation</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TotalRetriesAllowed" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>How many retries are allowed for a transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsTransactionBlocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the Transaction is blocked from use or not.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isTransactionComplete" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes if the transaction has been executed</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProcessedTimestamp" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Date/time the transaction was approved, declined, fraud or attempted. If the transaction is still pending, the ProcessedTimestamp will contain the transaction creation date/time.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationResult" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Whether or not the transaction has been verified or been marked as fraud or denied</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isNotifyServiceProviderUser" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Does the user get notified when the transaction is made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isNotifyServiceProvider" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Does the service provider get notified when the transaction is made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isSMSTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is SMS used for the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isOATHTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is OATH used for the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isOTPTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Does a one time password get generated with the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isVirtualCardTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is this a virtual card transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isUpdateProfileTransaction" type="xsd:boolean" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Is this a update profile transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionCreationTimestamp" type="xsd:dateTime">
            <xsd:annotation>
              <xsd:documentation>The date and time the transaction was generated</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Recurring" type="xsd:boolean" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Indicates whether its a client initiated recurring transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionCreationTimestampAsString" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The date and time the transaction was generated and passed as string</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="textDependentUtterances" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>The text dependent utterances used during enrollment.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CaptureScript" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>The capture scripts to send to the device</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="DeviceCompatible" type="xsd:boolean" nillable="true">
            <xsd:annotation>
              <xsd:documentation>Denotes if the transaction can be verified on the device.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Language" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>The desired language for the profile</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="EnrollData">
        <xsd:sequence>
          <xsd:element name="Name" type="xsd:string" />
          <xsd:element name="Value" type="xsd:string" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="CheckFaceLivenessResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response for face liveness check.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for face liveness check.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="Passed" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the face liveness check is passed.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="AudioCodecs">
        <xsd:annotation>
          <xsd:documentation>Codecs supported by the device</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="AudioCodecName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Name of the codec</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SampleRate" type="xsd:int" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Sample rate</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="BitDepth" type="xsd:int" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Bit depth</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Channels" type="xsd:int" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Channels supported</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CodecContainer" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Codec container supported</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ListAuthenticationFactorsRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the list of authentication factors that profile has already enrolled</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetTransactionRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the transactions to be
					validated for a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Device Info</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>Name of the service provider</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string" minOccurs="1" maxOccurs="1">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="TransactionStatus">
        <xsd:annotation>
          <xsd:documentation>Descibes the status of IdentityX Transaction.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="VERIFICATION_PENDING">
            <xsd:annotation>
              <xsd:documentation>Transaction is pending to be verified.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VERIFICATION_SUCCESSFUL">
            <xsd:annotation>
              <xsd:documentation>Transaction is sucessfully verified</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VERIFICATION_FAILED">
            <xsd:annotation>
              <xsd:documentation>Transaction verification is unsuccessful.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="FRAUD">
            <xsd:annotation>
              <xsd:documentation>Transaction is claimed as fraud</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="EXPIRED_WITH_VERIFICATION_PENDING">
            <xsd:annotation>
              <xsd:documentation>Transaction expired without verification.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="EXPIRED_WITH_VERIFICATION_FAILED">
            <xsd:annotation>
              <xsd:documentation>Transaction expired with verification failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="DECLINED">
            <xsd:annotation>
              <xsd:documentation>Transaction is declined.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="TransformRequest">
        <xsd:annotation>
          <xsd:documentation>Request object to transform one or more piece of data using the specified policy</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractDeviceGatewayRequest">
            <xsd:sequence>
              <xsd:element name="DeviceInfo" type="DeviceInfo" minOccurs="0" />
              <xsd:element name="PolicyName" type="xsd:string" minOccurs="0" />
              <xsd:element name="Data" type="DataItem" maxOccurs="unbounded" />
              <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0" />
              <xsd:element name="TransactionIdentifier" type="xsd:string" minOccurs="0" />
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="CheckDeviceStatusResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response for device bound status check.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for CheckDeviceStatusResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="DeviceInfo" type="DeviceInfo" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Contains the Device details.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="DeviceBound" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the device is bound to IdentityX.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="EnrollmentPolicy" type="EnrollmentPolicy" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Indicates the enrollment policy to be used.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Identity or user identifier enrolled in IdentityX.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="TransactionInfo" type="TransactionInfo" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Transaction information for the user defined by the service provider</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="VideoCodecs">
        <xsd:annotation>
          <xsd:documentation>Codecs supported by the device</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="VideoCodecName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Name of the codec</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CodecContainer" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Bit depth</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="FramesPerSecond" type="xsd:int" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Frames per second</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Size" type="xsd:long" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation />
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="VideoAttributes">
        <xsd:annotation>
          <xsd:documentation>Describes video features of the device</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="VideoCodecs" type="VideoCodecs" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
              <xsd:documentation>Denotes Codecs supported</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="AbstractAdminGatewayResponse">
        <xsd:annotation>
          <xsd:documentation>Response object from which all other Admin Gateway response inherit</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractIdentityXResponse" />
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:simpleType name="SponsorshipStatus">
        <xsd:annotation>
          <xsd:documentation>Descibes the status of IdentityX sposnorship.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="VALIDATION_PENDING">
            <xsd:annotation>
              <xsd:documentation>Sponsorship is pending to be verified.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VALIDATION_SUCCESSFUL">
            <xsd:annotation>
              <xsd:documentation>Sponsorship is sucessfully verified</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VALIDATION_FAILED">
            <xsd:annotation>
              <xsd:documentation>Sponsorship verification is unsuccessful.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="EXPIRED_WITH_VALIDATIONPENDING">
            <xsd:annotation>
              <xsd:documentation>Sponsorship expired without validation.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="EXPIRED_WITH_VALIDATION_FAILED">
            <xsd:annotation>
              <xsd:documentation>Sponsorship expired with failed validation .</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="Administrator">
        <xsd:annotation>
          <xsd:documentation>Contains the verification result details for a transaction that is verified.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="AdministratorIID" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>The internal identifier of the administrator</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIID" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Internal Service Provider Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderUniqueName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Service Provider Unique Name</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderFriendlyName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Service Provider Name (differs from ServiceProviderUniqueName)</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ProfileIID" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>Internal Profile Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AdminRole" type="AdminRole">
            <xsd:annotation>
              <xsd:documentation>The permissions an administrator has (M = Master Level, S = ServiceProvider Level, U = User Level)</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="UserName" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>The username of the Administrator</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="VerifyFaceResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Verification response for face verification.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for face liveness check.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="Verified" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>True indicates a successful verification.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ListTransactionRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the transactions to be validated for a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Device Info</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ReturnOffset" type="xsd:int">
                  <xsd:annotation>
                    <xsd:documentation>The starting index of the required list.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="NumberToReturn" type="xsd:int">
                  <xsd:annotation>
                    <xsd:documentation>The number of records required.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="IncludeHistoric" type="xsd:boolean" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>If IncludeHistoric is set to true, response will only include historic transactions in the TransactionInfo list,</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="ReturnCodeEnum">
        <xsd:annotation>
          <xsd:appinfo>
            <jaxb:typesafeEnumClass name="ReturnCode" />
          </xsd:appinfo>
          <xsd:documentation>SOAP Operation Return Codes</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="wsde:ReturnCode">
          <xsd:enumeration value="601">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICE_NOT_SUPPORTED" />
              </xsd:appinfo>
              <xsd:documentation>The Identity x service is not supported
						by this implementation of Identity x.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="602">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NON_EXISTENT_PROFILE_ID" />
              </xsd:appinfo>
              <xsd:documentation>A required profiles id does not exist.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="603">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DATA_ACCESS_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Data .</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="604">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SYSTEM_FAILURE" />
              </xsd:appinfo>
              <xsd:documentation>Unknown System Failure.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="605">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CHECK_DEVICE_STATUS_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Check Device status method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="606">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DEVICE_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Device identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="607">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DECLINE_TRANSACTION_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Decline Transaction method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="608">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_IMAGE_TYPE_UNSUPPORTED" />
              </xsd:appinfo>
              <xsd:documentation>Image type is unsupported.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="609">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CHECK_FACE_QUALITY_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Check face quality method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="610">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CHECK_PROFILE_STATUS_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Check Profile Status Failed</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="611">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_SPONSORSHIP_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Failed to retrieve Sponsor ships</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="612">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_TRANSACTION_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Failed to retrieve Transactions</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="613">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VALIDATE_SPONSOR_CODE_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Failed to validate Sponsor Code</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="614">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DEVICE_BOUND_TO_DIFFERENT_PROFILE" />
              </xsd:appinfo>
              <xsd:documentation>Device is bound to another profile.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="615">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROFILE_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Profile identifier is empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="616">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PIN_DURESSPIN_MATCH" />
              </xsd:appinfo>
              <xsd:documentation>PIN and Duress PIN cannot match.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="617">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_FACE_BIOMETRIC" />
              </xsd:appinfo>
              <xsd:documentation>Face biometric is missing.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="618">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_VOICE_BIOMETRIC" />
              </xsd:appinfo>
              <xsd:documentation>Voice biometric is missing.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="619">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_DECOMPRESS_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Voice decompression failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="620">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDER_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Service Provider Identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="621">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDER_CUSTOMER_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>ServiceProvider user or customer identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="622">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_ID_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship Identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="623">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROFILE_ID_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Profile Identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="624">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_RETRIES_ALLOWED_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Retries Allowed is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="625">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PHONE_NUMBER_OR_COUNTRY_CODE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Phone Number or Country Code is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="626">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDER_TRANSACTION_ID_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>ServiceProvider Transaction Identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="627">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDER_TRANSACTION_DESCRIPTION_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>ServiceProvider Transaction Description is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="628">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_POLICY_NOT_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>ServiceProvider Policy Identifier is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="629">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_EXPIRATION_DATE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Transaction Expiration Date is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="630">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VIRTUAL_CARD_NUMBER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Virtual Card number is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="631">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VIRTUAL_CVV_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Virtual Card CVV is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="632">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VIRTUAL_CARD_EXPIRATION_DATE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Virtual Card expiration date is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="633">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GPS_LONGITUDE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>GPS longitude is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="634">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GPS_LATITUDE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>GPS latitude is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="635">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GPS_ALLOWABLE_DISTANCE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>GPS Allowable distance is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="636">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_AUTHENTICATION_CODE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Transaction authentication code is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="637">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_CODE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship code is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="638">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SMS_SEND_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Failed to send SMS.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="639">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship has Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="640">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_STATUS_REQUEST_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship status request has Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="641">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_VERIFICATION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship verification has Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="642">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Transaction has Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="643">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_STATUS_REQUEST_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Transaction status request has Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="644">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_VERIFICATION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Transaction verification has Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="645">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_REQUEST_FOR_POLICIES_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Request for Policies has Failed</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="646">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_BIND_SERVICEPROVIDER_TO_USER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Binding ServiceProvider to User has failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="647">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_SPONSORSHIP_INFO_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Sponsorship information found for customer.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="648">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_TOKEN_EXPIRED" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship token has expired.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="649">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_NOT_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Transaction information found for customer.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="650">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_POLICIES_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Policies found for Service Provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="651">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_EXPIRED" />
              </xsd:appinfo>
              <xsd:documentation>Transaction has expired.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="652">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROFILE_ALREADY_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>Profile Already exists.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="653">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_EXPIRATION_BEFORE_CURRENT_DATE" />
              </xsd:appinfo>
              <xsd:documentation>Transaction Expiration Date is before Current Date.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="654">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CUSTOMER_BOUND_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Customer bound method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="655">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VERIFY_TRANSACTION_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Verify Transaction method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="656">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDER_NOT_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>Unable to find service provider for a given serviceprovider identifier.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="657">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDERUSER_NOT_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>Unable to find service provider user or customer for a given serviceprovider identifier and customer identifier .</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="658">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_PIN_INFORMATION" />
              </xsd:appinfo>
              <xsd:documentation>PIN information missing.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="659">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_EXCEEDED_AVAILABLE_RETRIES" />
              </xsd:appinfo>
              <xsd:documentation>Reached the allowed retry limit.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="660">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ENROLL_PROFILE_FOR_IDENTITYXLITE_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Enroll profile for IdentityX lite failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="661">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUTHENTICATION_TOKEN_ALREADY_CONSUMED" />
              </xsd:appinfo>
              <xsd:documentation>Authentication Token Already Consumed</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="662">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_TYPE_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>IdentitytX Transaction Type is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="663">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ENROLL_PROFILE_FOR_IDENTITYX_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Enroll profile for IdentityX failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="664">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CUSTOMER_ALREADY_BOUND" />
              </xsd:appinfo>
              <xsd:documentation>Customer is already bound to Service Provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="665">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_DEVICE_BOUND_TO_PROFILE" />
              </xsd:appinfo>
              <xsd:documentation>No Device Info found for this profile(No device bound for profile).</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="666">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INVALID_DATE_FORMAT" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Date Format Supplied.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="667">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_POLICY_ALREADY_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>The policy identifier, service provider combination you are trying to create already exists.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="668">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_POLICY_NOT_CREATED" />
              </xsd:appinfo>
              <xsd:documentation>The policy was not successfully created.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="669">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICE_PROVIDER_NAME_ALREADY_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>The unique service provider name you are using already exists.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="670">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICE_PROVIDER_NOT_CREATED" />
              </xsd:appinfo>
              <xsd:documentation>The service provider was not successfully created.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="671">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_REQUEST_FOR_SERVICE_PROVIDERS_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>An error occurred while trying to retrieve service providers.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="672">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_SERVERTIME_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Server Time Method has failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="673">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CUSTOMER_NOT_BOUND" />
              </xsd:appinfo>
              <xsd:documentation>Customer not bound to Service Provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="674">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROFILE_ALREADY_BOUND_TO_SERVICEPROVIDER" />
              </xsd:appinfo>
              <xsd:documentation>Profile is already bound to Service Provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="675">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_FACE_IMAGE_CONSOLIDATION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Creating consolidated Image template from live face images failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="676">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CHECK_FACE_LIVENESS_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Check Face Liveness Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="677">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PHONE_ALREADY_LINKED_TO_DIFFERENT_PROFILE" />
              </xsd:appinfo>
              <xsd:documentation>Phone number already linked to another Profile</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="678">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_PROFILES_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No profiles were retrieved</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="679">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICE_PROVIDER_USER_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>The mandatory service provider user identifier field is empty</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="680">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_REQUEST_FOR_TRANSACTIONS_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>An error occurred retrieving transactions</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="681">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_TRANSACTIONS_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No transactions were retrieved</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="682">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>The mandatory transaction identifier field is empty</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="683">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_VERIFICATION_ATTEMPTS_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No verification attempts were retrieved</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="684">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VERIFICATION_ATTEMPT_IDENTIFIER_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>The mandatory verification attempt identifier is empty</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="685">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_REQUEST_FOR_VERIFICATION_ATTEMPTS_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>An error occurred retrieving verification attempts</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="686">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DELETE_SP_REFERENCES_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>An error occurred deleting all references to a service provider</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="687">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DELETE_PROFILE_REFERENCES_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>An error occurred deleting all references to a profile</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="688">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_PALM_BIOMETRIC" />
              </xsd:appinfo>
              <xsd:documentation>Palm information must be included as part of the request</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="689">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CHECK_VOICE_LIVENESS_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Voice liveness check failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="690">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CLIENT_CERTIFICATE_DATA_MISMATCH" />
              </xsd:appinfo>
              <xsd:documentation>Client Certificate Data Mismatch</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="691">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_CLIENT_CERTIFICATE_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Client Certificate data found</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="692">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_DEVICES_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Devices were found</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="693">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_ADMINISTRATOR_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Administrators were found</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="694">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADMINISTRATOR_NOT_SUCCESSFULLY_CREATED" />
              </xsd:appinfo>
              <xsd:documentation>Administrator was unable to be created</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="695">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DELETE_ADMINISTRATOR_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Administrator was unable to be created</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="696">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADD_DEVICE_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Add Device Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="697">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADD_DEVICE_PROFILE_VERIFICATION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Add Device Profile Verification Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="698">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_BLOCKED_DEVICE" />
              </xsd:appinfo>
              <xsd:documentation>Device has been blocked.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="699">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_BLOCKED_PROFILE" />
              </xsd:appinfo>
              <xsd:documentation>Profile has been blocked.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="700">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADMINISTRATOR_NOT_SUCCESSFULLY_ADDED" />
              </xsd:appinfo>
              <xsd:documentation>Administrator was not added to service provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="701">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADMIN_NOT_ALLOWED_OPERATION" />
              </xsd:appinfo>
              <xsd:documentation>Administrator is not allowed to call this web service with their current permissions.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="702">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADMIN_NOT_ALLOWED_OPERATION_ON_SERVICE_PROVIDER" />
              </xsd:appinfo>
              <xsd:documentation>Administrator does not have permission to operate on this specific service provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="703">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DELETE_CUSTOMER_REFERENCES_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>All the references to customer were not able to be deleted.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="704">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_CERTIFICATE_PASSWORD" />
              </xsd:appinfo>
              <xsd:documentation>The password for the certificate was not passed in to the request.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="705">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_CERTIFICATE_SERVICE_PROVIDER" />
              </xsd:appinfo>
              <xsd:documentation>The service provider for the certificate was not passed in to the request.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="706">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CERTIFICATE_ALREADY_CREATED" />
              </xsd:appinfo>
              <xsd:documentation>A certificate has already been generated for this service provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="707">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_CERTIFICATE_REQUEST" />
              </xsd:appinfo>
              <xsd:documentation>The P10 Data for the certificate was not passed in to the request.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="708">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_CERTIFICATE_SUBJECT_NAME" />
              </xsd:appinfo>
              <xsd:documentation>The Subject Name information for the certificate was not passed in to the request.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="709">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_REFRESH_KEYS_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Refresh Keys Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="710">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UPDATE_ENROL_POLICY_FAILED_AS_PROFILES_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>Update Enrollment Policy failed, as profiles exist with this enrollment.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="711">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UPDATE_POLICY_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Update Policy Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="712">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_KEYS_EXPIRED" />
              </xsd:appinfo>
              <xsd:documentation>Shared Secret Expired, obtain new keys.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="713">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VERIFICATION_POLICY_DOESNOT_MATCH_ANY_ENROLLMENT_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>The Verification Policy does not meet or match any enrollment policy on the system.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="714">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CERTIFICATE_FINGER_PRINTS_DO_NOT_MATCH" />
              </xsd:appinfo>
              <xsd:documentation>Certificate Finger Prints do not match.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="715">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VERIFY_WITH_PERSISTED_CERTIFICATE_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Verify with Persisted Certificate Method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="716">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_GPS_COORDINATES" />
              </xsd:appinfo>
              <xsd:documentation>Missing GPS coordinates.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="717">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_ADMINISTRATOR_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Administrator Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="718">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_CERTIFICATE_FOR_SERVICE_PROVIDER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Certficate for Service Provider Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="719">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_DEVICE_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Device Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="720">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_P12_FOR_SERVICE_PROVIDER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get P12 for Service Provider Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="721">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_POLICY_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Policy Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="722">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_PROFILE_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Profile Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="723">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_ADMINISTRATORS_BY_SERVICE_PROVIDER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>List Administrators By Service Provider Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="724">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_PROFILES_BY_CUSTOMER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>List Profiles By Customer Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="725">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_PROFILES_BY_SERVICE_PROVIDER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>List Profiles By Service Provider Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="726">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SEARCH_PROFILES_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Search Profiles Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="727">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSER_ADMIN_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Sponser Admin Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="728">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UPDATE_DEVICE_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Update Device Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="729">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UPDATE_PROFILE_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Update Profile Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="730">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UPDATE_SERVICE_PROVIDER_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Update Service Provider Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="731">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VERIFY_IDENTITY_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Verify Identity Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="732">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADMINISTRATOR_ALREADY_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>Administrator already exists</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="733">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_GET_LAST_VERIFICATION_STATUS_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Get Last Verification Status Failed</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="734">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_IS_VERIFIED" />
              </xsd:appinfo>
              <xsd:documentation>Cannot Update Transaction. Because transaction is already verified.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="735">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_IS_DECLINED" />
              </xsd:appinfo>
              <xsd:documentation>Cannot Update Transaction. Because transaction is already declined</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="736">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_IS_FRAUD" />
              </xsd:appinfo>
              <xsd:documentation>Cannot Update Transaction. Because transaction is marked as fraud</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="737">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UPDATE_TRANSACTION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Update Transaction Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="738">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_CANCEL_TRANSACTION_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Cancel Transaction Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="739">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_USER_NOT_AUTHORIZED" />
              </xsd:appinfo>
              <xsd:documentation>User not Authorized.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="740">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_POLICY_NOT_COMPATIBLE_FOR_PROFILE" />
              </xsd:appinfo>
              <xsd:documentation>Policy not compatible for Profile</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="741">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROFILE_DELETED_OR_BLOCKED" />
              </xsd:appinfo>
              <xsd:documentation>Your profile is either blocked or deleted.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="742">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ROLE_NOT_ALLOWED_FOR_PROFILE" />
              </xsd:appinfo>
              <xsd:documentation>Profile cannot be sponsored for this role.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="743">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ADMINISTRATOR_IS_ALREADY_MASTER_ADMIN" />
              </xsd:appinfo>
              <xsd:documentation>Profile already has a role of master admin.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="744">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSOR_DEVICE_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Sponsor device method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="745">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSOR_DEVICE_FOR_PROFILE_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Sponsor device for profile method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="746">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROFILE_LOCKED" />
              </xsd:appinfo>
              <xsd:documentation>Profile has been temporarily locked.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="747">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DEVICE_FEATURES_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Device Features is null or empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="748">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_FACE_FOUND_IN_IMAGE" />
              </xsd:appinfo>
              <xsd:documentation>No face could be found in the image.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="749">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MULTIPLE_FACES_FOUND_IN_IMAGE" />
              </xsd:appinfo>
              <xsd:documentation>More than one face was found in the image.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="750">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_COULD_NOT_LOCATE_EYES_IN_IMAGE" />
              </xsd:appinfo>
              <xsd:documentation>The eyes could not be located in the image.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="751">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_VOICE_IN_RECORDING" />
              </xsd:appinfo>
              <xsd:documentation>No Voice could be heard in the recording.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="752">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_TOO_SOFT_FOR_PROCESSING" />
              </xsd:appinfo>
              <xsd:documentation>The voice was too soft to be processed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="753">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITY_MULTIPLE_VOICE_IN_RECORDING" />
              </xsd:appinfo>
              <xsd:documentation>More than one voice could be heard in the recording.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="754">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TOO_MUCH_BACKGROUND_NOISE" />
              </xsd:appinfo>
              <xsd:documentation>There was too much background noise in the recording.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="755">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UNABLE_TO_PROCESS_RECORDING" />
              </xsd:appinfo>
              <xsd:documentation>Unable to process the voice recording.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="756">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_TOO_LONG" />
              </xsd:appinfo>
              <xsd:documentation>Audio too long.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="757">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_TOO_LOUD" />
              </xsd:appinfo>
              <xsd:documentation>Audio too loud.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="758">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_TOO_SHORT" />
              </xsd:appinfo>
              <xsd:documentation>No Voice in the sample or sample is too short.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="759">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INVALID_AUDIO" />
              </xsd:appinfo>
              <xsd:documentation>Invalid audio.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="760">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_ENROLLMENT_AUDIO_PLAYEDBACK" />
              </xsd:appinfo>
              <xsd:documentation>Enrollment audio used in verification.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="761">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_PLAYBACK_FRAUD" />
              </xsd:appinfo>
              <xsd:documentation>Audio playback fraud occurred.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="762">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DETECTED_TONE" />
              </xsd:appinfo>
              <xsd:documentation>Audio from a tone phone.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="763">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INVALID_AUDIO_FILE_FORMAT" />
              </xsd:appinfo>
              <xsd:documentation>Invalid audio format.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="764">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_DEVICE_INCOMPATIBLE_FOR_TRANSACTION" />
              </xsd:appinfo>
              <xsd:documentation>Device is incompatible to process the transaction.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="765">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_DEVICES_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>List devices method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="766">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PROCESS_TRANSACTION_DATA_BLOCK_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Process transaction data block method failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="767">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ACTIVE_DIRECTORY_ACCOUNT_DISABLED" />
              </xsd:appinfo>
              <xsd:documentation>Active Directory account has been disabled.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="768">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ACTIVE_DIRECTORY_ACCOUNT_LOCKED" />
              </xsd:appinfo>
              <xsd:documentation>Active Directory account locked.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="769">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ACTIVE_DIRECTORY_ACCOUNT_EXPIRED" />
              </xsd:appinfo>
              <xsd:documentation>Active Directory account expired.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="770">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ACTIVE_DIRECTORY_CREDENTIALS_EXPIRED" />
              </xsd:appinfo>
              <xsd:documentation>Active Directory credentials expired.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="771">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ACTIVE_DIRECTORY_PASSWORD_RESET_REQUIRED" />
              </xsd:appinfo>
              <xsd:documentation>Active Directory password reset required.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="772">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ACTIVE_DIRECTORY_LOGON_NOT_PERMITTED" />
              </xsd:appinfo>
              <xsd:documentation>Active Directory logon not permitted.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="774">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_ENROLLMENT_DATA" />
              </xsd:appinfo>
              <xsd:documentation>The profile creation or update cannot proceed as none 
						of the not all required data was received.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="775">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPEECH_NOT_RECOGNIZED" />
              </xsd:appinfo>
              <xsd:documentation>Speech was not recognized.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="776">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICE_PROVIDER_INVALID" />
              </xsd:appinfo>
              <xsd:documentation>Only IdentityX service provider is valid.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="777">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_TYPE_INVALID" />
              </xsd:appinfo>
              <xsd:documentation>Only IDENTITYXENROLL sponsorship type is valid.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="778">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_LIST_SECURITY_QUESTIONS_METHOD_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>List Security Questions Method Failed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="779">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_PERMISSION_DENIED" />
              </xsd:appinfo>
              <xsd:documentation>This data or operation cannot be accessed or performed by user.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="780">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NO_SECURITY_QUESTIONS_FOUND" />
              </xsd:appinfo>
              <xsd:documentation>No Security Questions found.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="781">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SERVICEPROVIDER_NAME_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Service Provider Name is missing from request.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="782">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_REENROLLMENT_FAILED" />
              </xsd:appinfo>
              <xsd:documentation>Failed to Re Enroll Voice Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="783">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPEAKER_NOT_ENROLLED" />
              </xsd:appinfo>
              <xsd:documentation>Speaker is not enrolled</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="784">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_SERVER_UNINITIALIZED" />
              </xsd:appinfo>
              <xsd:documentation>Voice system is uninitialized.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="785">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_SERVER_UNAVAILABLE" />
              </xsd:appinfo>
              <xsd:documentation>Voice system is unavailable.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="786">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_SERVICEPROVIDER_POLICY_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Missing Service Provider Policy Identifier.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="787">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INVALID_FIELD_SIZE" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Field Size, Exceeds system supported field length.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="788">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NON_NUMERIC_PIN_VALUE" />
              </xsd:appinfo>
              <xsd:documentation>Invalid PIN value.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="789">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NON_NUMERIC_DURESS_PIN_VALUE" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Duress PIN value</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="790">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INVALID_OTP_CONFIGURATION" />
              </xsd:appinfo>
              <xsd:documentation>Invalid OTP Configuration</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="791">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_TOKEN" />
              </xsd:appinfo>
              <xsd:documentation>Token not provided</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="792">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_TRANSACTION_ALREADY_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>Transaction with given identifier already exists</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="793">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INVALID_TRANSACTION_TYPE" />
              </xsd:appinfo>
              <xsd:documentation>Transaction type is invalid</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="794">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_EXPIRATION_BEFORE_CURRENT_DATE" />
              </xsd:appinfo>
              <xsd:documentation>Sponsorship expiration date is before current date</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="795">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_SPONSORSHIP_IDENTIFIER_ALREADY_EXISTS" />
              </xsd:appinfo>
              <xsd:documentation>SponsorShip Identifier Already exists</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="796">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_POLICY_NOT_AN_ENROLLMENT_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Given Policy is not an enrollment policy.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="797">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_FACE_NOT_FRONTAL" />
              </xsd:appinfo>
              <xsd:documentation>Face not Frontal. Please look directly at the camera.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="798">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_NON_UNIFORM_LIGHTING" />
              </xsd:appinfo>
              <xsd:documentation>Face lighting not uniform.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="799">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_FACE_TILT_DETECTED" />
              </xsd:appinfo>
              <xsd:documentation>Face is tilted. Please look directly at the camera.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="800">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_BLURRED_IMAGE_DETECTED" />
              </xsd:appinfo>
              <xsd:documentation>Image blur detected. Reduce motion or improve lighting.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="801">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_IMAGE_TOO_DARK" />
              </xsd:appinfo>
              <xsd:documentation>Image too dark. Please improve lighting.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="802">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_FACE_TOO_SMALL" />
              </xsd:appinfo>
              <xsd:documentation>Face too small. Please move closer to camera.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="803">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_FACE_IMAGE_TOO_SOFT" />
              </xsd:appinfo>
              <xsd:documentation>Face image too soft, lacking details. Please improve lighting.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="804">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_POLICY_NOT_A_VERIFICATION_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Policy not a verification policy.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="805">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_UNSUPPORTED_CHARACTER_IN_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Unsupported character found in Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="806">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_INCOMPLETE_DATA_IN_CAPTURE_SCRIPT" />
              </xsd:appinfo>
              <xsd:documentation>Incomplete data provided in Capture Script.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="807">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_TEXT_PROMPTED_BIOMETRIC" />
              </xsd:appinfo>
              <xsd:documentation>Text Prompted biometric is missing.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="808">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_FINGERPRINT_BIOMETRIC" />
              </xsd:appinfo>
              <xsd:documentation>Fingerprint biometric is missing.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="809">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MISSING_DEVICE_CHALLENGE_KEY" />
              </xsd:appinfo>
              <xsd:documentation>Device challenge key is missing.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="810">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_MODALITY_NOT_ALLOWED" />
              </xsd:appinfo>
              <xsd:documentation>Modality not allowed.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="851">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_TOO_SOFT" />
              </xsd:appinfo>
              <xsd:documentation>Audio too soft.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="852">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_TOO_NOISY" />
              </xsd:appinfo>
              <xsd:documentation>Audio too noisy.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="853">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_TOO_SOFT_SPEECH_CLARITY" />
              </xsd:appinfo>
              <xsd:documentation>Audio speech clarity too soft.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="854">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_UTTERANCE_TOO_SHORT" />
              </xsd:appinfo>
              <xsd:documentation>Utterance too short.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="855">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_TOO_LOUD" />
              </xsd:appinfo>
              <xsd:documentation>Audio too loud.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="856">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_FILE_IS_CORRUPTED" />
              </xsd:appinfo>
              <xsd:documentation>Audio file is corrupted.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="857">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_FILE_IS_EMPTY" />
              </xsd:appinfo>
              <xsd:documentation>Audio file is empty.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="858">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_WRONG_UTTERANCE_DETECTED" />
              </xsd:appinfo>
              <xsd:documentation>Audio wrong utterance detected.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="859">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_AUDIO_VALIDATION_FAILED_UNSUPPORTED_FORMAT" />
              </xsd:appinfo>
              <xsd:documentation>Audio unsupported format.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="860">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_UNKNOWN_BACKGROUND_MODEL" />
              </xsd:appinfo>
              <xsd:documentation>Audio unkown background model.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="861">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_BACKGROUND_MODEL_MISMATCH" />
              </xsd:appinfo>
              <xsd:documentation>Voice background model mismatch.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="862">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_MULTIPLE_FREQUENCIES" />
              </xsd:appinfo>
              <xsd:documentation>Voice has multiple frequencies.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="863">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_UNKOWN_UTTERANCE_MODEL" />
              </xsd:appinfo>
              <xsd:documentation>Voice unkown utterance model.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="864">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_UNKOWN_NON_UTTERANCE_MODEL" />
              </xsd:appinfo>
              <xsd:documentation>Voice unkown non utterance model.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="865">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_UNKOWN_LIVENESS_MODEL" />
              </xsd:appinfo>
              <xsd:documentation>Voice unkown liveness model.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="866">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_ERROR_INCORRECT_PARAMETERS" />
              </xsd:appinfo>
              <xsd:documentation>Voice incorrect parameters.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="867">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_ERROR_EXCEPTION_OCCURRED" />
              </xsd:appinfo>
              <xsd:documentation>Voice exception occurred.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="868">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_ERROR_NON_INITIALIZED" />
              </xsd:appinfo>
              <xsd:documentation>Voice not initialized.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="869">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_UNKNOWN_LIVENESS_PHRASE" />
              </xsd:appinfo>
              <xsd:documentation>Voice unkown liveness phrase.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="870">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITYX_VOICE_FAILED_INVALID_TEMPLATE" />
              </xsd:appinfo>
              <xsd:documentation>Voice invalid template.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="CaptureMetaData">
        <xsd:annotation>
          <xsd:documentation>Meta data returned by the capture client</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="MetaDataItem" type="CaptureMetaDataItem" minOccurs="0" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="ChangeKeyStorageKeyInfo">
        <xsd:annotation>
          <xsd:documentation>Contains information related to Changing a Key Storage Key</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="RevokedReason" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Reason of revoke or change</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NewKeyTypeIdentifierSet" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicates whether or not this request is for a new Key type</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NewKeyTypeIdentifier" type="KeyType" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>The new Key type identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="AdministratorRole">
        <xsd:annotation>
          <xsd:documentation>Type of Privileges an Administrator has.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="IXADMIN">
            <xsd:annotation>
              <xsd:documentation>Master Role</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="SPADMIN">
            <xsd:annotation>
              <xsd:documentation>Service Provider Admin</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="SPUSER">
            <xsd:annotation>
              <xsd:documentation>Service Provider User</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="TransformRequest" type="TransformRequest" />
      <xsd:element name="GetPalmStencilRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the palm stencil</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="PalmStencil" type="PalmStencil">
                  <xsd:annotation>
                    <xsd:documentation>Palm stencil data</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="VerificationType">
        <xsd:annotation>
          <xsd:documentation>Contains the policy attributes defined by service provider</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="isVideo" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Verifcation Type is Video, i.e. IdentityX Standard 
						with video capture instead of camera capture for face and voice.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isIdentityXStandard" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Verifcation Type is the default standard IdentityX</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isLandline" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Verifcation Type is Landline</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isOffline" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Verifcation Type is Offline</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isSMS" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if Verifcation Type is SMS</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="Scores">
        <xsd:annotation>
          <xsd:documentation>Score Information of a modality.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="FMR" type="xsd:double" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>FMR score</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="RawScore" type="xsd:double" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Raw Score</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="VerifiedTransactionInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the verification result details for a transaction that is verified.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Verified" type="xsd:boolean">
            <xsd:annotation>
              <xsd:documentation>True indicates a successful verification.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionReturnType" type="TransactionReturnType" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Indicates the return type whether it an OTP or virual card info etc.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AuthenticationCode" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>One time password to complete the transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VirtualCardDetails" type="VirtualCardDetails" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Virtual card details</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="BiometricScoreInfo" type="BiometricScoreInfo" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Biometric score information</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="ChangeDatabasePrivateInformationKeyInfo">
        <xsd:annotation>
          <xsd:documentation>Contains information related to Changing a Database Private Information Key</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="RevokedReason" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Reason of revoke or change</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NewKeyTypeIdentifierSet" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicates whether or not this request is for a new Key type</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NewKeyTypeIdentifier" type="KeyType" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>The new Key type identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="SystemKeyType">
        <xsd:annotation>
          <xsd:documentation>The System Key Type being requested</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="MASTER_KEY" />
          <xsd:enumeration value="KEY_STORAGE_KEY" />
          <xsd:enumeration value="DATABASE_PRIVATE_INFORMATION_KEY" />
          <xsd:enumeration value="DATABASE_MAC_KEY" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="AbstractDeviceGatewayRequest">
        <xsd:annotation>
          <xsd:documentation>Basic Abstract Device Request</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractIdentityXRequest">
            <xsd:sequence>
              <xsd:element name="DeviceVersion" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Application version number.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="DeviceFrameworkVersion" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Device framework version number.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="CheckPalmQualityResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Verification response for check palm quality.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for palm quality check.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="PalmQualityCheckPassed" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the palm quality check passed.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ServiceProviderInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the transaction information for the user.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ServiceProviderName" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Name of the service provider</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIcon" type="xsd:base64Binary" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Providers's icon or logo</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderUserIdentifier" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider's customer or user Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="VoiceFormat">
        <xsd:annotation>
          <xsd:documentation>Format of voice data.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="WAV">
            <xsd:annotation>
              <xsd:documentation>Wav format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="AIFF">
            <xsd:annotation>
              <xsd:documentation>AIFF format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="WAVPACK">
            <xsd:annotation>
              <xsd:documentation>Wavpack compressed voice data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="AMR">
            <xsd:annotation>
              <xsd:documentation>AIFF format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="OTHER">
            <xsd:annotation>
              <xsd:documentation>Other voice format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="ListSponsorshipRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get the sponsorships to be validated for a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="SponsorShipType" type="SponsorshipType">
                  <xsd:annotation>
                    <xsd:documentation>Indicates SponsorShip Type [Possible values (IDENTITYX, SMSENROLL, IDENTITYXLITE)]</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ReturnOffset" type="xsd:int">
                  <xsd:annotation>
                    <xsd:documentation>The starting index of the required list.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="NumberToReturn" type="xsd:int">
                  <xsd:annotation>
                    <xsd:documentation>The number of records required.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="CustomParametersType">
        <xsd:annotation>
          <xsd:documentation>Object for storing custom parameters in the request</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ApplicationIdentifier" type="xsd:string" minOccurs="0" maxOccurs="1" default="IdentityXClient">
            <xsd:annotation>
              <xsd:documentation>Unique Identifier for the application calling the IdentityX operation</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="UserIdentifier" type="xsd:string" minOccurs="0" maxOccurs="1" default="IdentityXClientUser">
            <xsd:annotation>
              <xsd:documentation>Unique Identifier for the user calling the IdentityX operation</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="TransformResponse">
        <xsd:annotation>
          <xsd:documentation>Transform response</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractDeviceGatewayResponse">
            <xsd:sequence>
              <xsd:element name="ResponseData" minOccurs="0">
                <xsd:complexType>
                  <xsd:sequence>
                    <xsd:element name="Data" type="DataItem" minOccurs="0" maxOccurs="unbounded" />
                    <xsd:element name="ProcessingResults" type="ProcessingResult" maxOccurs="unbounded" />
                  </xsd:sequence>
                </xsd:complexType>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="CheckFaceQualityAndLivenessRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request to check the quality of face image as well as liveness of the user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="FullFaceFrontalSample" type="Face">
                  <xsd:annotation>
                    <xsd:documentation>Face data with face looking directly into the camera.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="LiveFaceSamples" type="Face" minOccurs="1" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Live Face data with several face images captured with micro-movements.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="UpdatePushTokenRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to update the device token</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Token" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>The token required by server for sending client push notification</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ListDevicesResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to list Devices associated with a profile.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for list devices operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="DeviceInfo" type="DeviceInfo" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>List of Device's associated with the profile.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ServiceProvider">
        <xsd:annotation>
          <xsd:documentation>Contains the policy attributes defined by service provider</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ServiceProviderUniqueID" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Provider Unique Identifier.  Typically a name of a company.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Internal Service Provider IID.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ServiceProviderIcon" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Service Providers's icon or logo</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Name" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Name of the Service Provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="LongName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Long Name of the Service Provider</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryFirstName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>First name of the primary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryLastName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Last name of the primary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryEmail" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Email address of the primary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryPhone" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Phone number of the primary point of contact</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryPhoneCountryCode" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Country code of the primary phone number.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryAlternatePhone" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Alternate phone number of the primary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryAlternatePhoneCountryCode" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Alternate country code of the alternate phone number.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="PrimaryFax" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Fax number for the primary point of contact</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryFirstName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>First name of the secondary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryLastName" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Last name of the secondary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryEmail" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Email address of the secondary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryPhone" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Phone number of the secondary point of contact</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryPhoneCountryCode" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Country code of the secondary phone number.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryAlternatePhone" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Alternate phone number of the secondary point of contact.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryAlternatePhoneCountryCode" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Alternate country code of the secondary phone number.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="SecondaryFax" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Fax number for the secondary point of contact</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Address" type="Address" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Address of the service provider.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CertificateCreated" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Whether or not the certificate was created</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="IsServiceProviderBlocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Denotes if the Service Provider is blocked from use or not.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ListServiceProvidersRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to get a list of all the service
					providers.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>User identifier enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Device Info</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="TransactionPushNotificationType">
        <xsd:annotation>
          <xsd:documentation>Transaction Push Notification Type</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="VERIFY_WITH_CONFIRMATION">
            <xsd:annotation>
              <xsd:documentation>
                <xsd:documentation>The user will be prompted a choice to verify, decline or fraud the transaction.</xsd:documentation>
              </xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="VERIFY_WITHOUT_CONFIRMATION">
            <xsd:annotation>
              <xsd:documentation>
                <xsd:documentation>The user will be immediately prompted to verify the transaction.</xsd:documentation>
              </xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="REFRESH">
            <xsd:annotation>
              <xsd:documentation>
                <xsd:documentation>The app will be refreshed and the transaction will be listed in the user's transaction list.</xsd:documentation>
              </xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="AbstractServiceProviderGatewayRequest">
        <xsd:annotation>
          <xsd:documentation>Basic Abstract Service Provider Request</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractIdentityXRequest" />
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="ProcessTransactionDataBlockResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for ProcessTransactionDataBlock.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for ProcessTransactionDataBlockResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="ResponseScripts" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The scripts to send to the device to capture any further information.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:simpleType name="TransactionType">
        <xsd:annotation>
          <xsd:documentation>Type of IdentityX Transaction.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="IDENTITYX">
            <xsd:annotation>
              <xsd:documentation>Biometric/Non-Biometric based verification</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="SMSENROLL">
            <xsd:annotation>
              <xsd:documentation>SMS based verfication</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="IDENTITYXLITE">
            <xsd:annotation>
              <xsd:documentation>OATH based verification</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="PHONECALLBACKENROLL">
            <xsd:annotation>
              <xsd:documentation>Phone callback based verification</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="DEVICELIFECYCLE">
            <xsd:annotation>
              <xsd:documentation>Device life cycle based verification</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="UPDATEPROFILE">
            <xsd:annotation>
              <xsd:documentation>Update Profile based verifcation</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="CheckFaceQualityResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response for ace quality check.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for face quality check.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="Passed" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if the face quality is sufficient.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="ListAuthenticationFactorsResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for List Authentication Factor for a specific profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for list authentication facotr operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="PinEnrolled" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if PIN is present</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="DuressPinEnrolled" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if DURESS PIN is present</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="FaceEnrolled" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if Face Data is present</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="PalmEnrolled" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if Palm Data is present</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="VoiceEnrolled" type="xsd:boolean">
                        <xsd:annotation>
                          <xsd:documentation>Flag to indicate if Voice Data is present</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="EnrolledFactors" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>A JSON array of objects describing the factors enrolled for this profile. A JS client can parse this array to iterate over the factors
                                    			enrolled for this profile. Each object in the array will have at least one property, 'name', which is the identifier for this factor. 
                                    			The following is an example of a value for this property:
                                    			[
                                    				{
                                    					"name" : "securityQuestions",
                                    					"questionsAnswered" : ["q1", q3", q8"]
                                    				},
                                    				{
                                    					"name" : "palmVein"
                                    				}
                                    			]
                                    			The above shows that the profile has two factors enrolled, 'securityQuestions' and 'palmVein'. Also, the object associated with the 
                                    			securityQuestions factor present some additional information regarding the exact questions the profile has provided answers to. Each
                                    			factor listed in this array may have additional properties holding information related only to its factor.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="Devices" type="DeviceInfo" minOccurs="1" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>Information stored on the server for the devices associated with this profile.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Identity or user identifier of the enrolled profile in IdentityX. 
                        						This is unique in IdentityX Realm.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="Voice">
        <xsd:annotation>
          <xsd:documentation>Contains the voice information.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="BiometricData">
            <xsd:sequence>
              <xsd:element name="VoiceFormat" type="VoiceFormat" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>Denotes the format of voice data. Wav or AIFF or other formats.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="Utterance" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>Text data that was said.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:simpleType name="KeyType">
        <xsd:annotation>
          <xsd:documentation>The Key Type being requested</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="SYMMETRIC_KEY_AES_128" />
          <xsd:enumeration value="SYMMETRIC_KEY_AES_192" />
          <xsd:enumeration value="SYMMETRIC_KEY_AES_256" />
          <xsd:enumeration value="ASYMMETRIC_KEY_RSA_1024" />
          <xsd:enumeration value="ASYMMETRIC_KEY_RSA_512" />
          <xsd:enumeration value="ASYMMETRIC_KEY_RSA_2048" />
          <xsd:enumeration value="ASYMMETRIC_KEY_DSA_512" />
          <xsd:enumeration value="ASYMMETRIC_KEY_DSA_1024" />
          <xsd:enumeration value="ASYMMETRIC_KEY_RSA_1024_SHA256" />
          <xsd:enumeration value="ASYMMETRIC_KEY_RSA_1024_SHA512" />
          <xsd:enumeration value="MAC_KEY" />
          <xsd:enumeration value="MAC_SHA256" />
          <xsd:enumeration value="MAC_SHA512" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:element name="UpdateProfileDetailsResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object to update a profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for UpdateProfileResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="DeclineTransactionRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to decline a transaction in IdentityX for a service provider user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CustomerIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the user within the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Fradulent" type="xsd:boolean">
                  <xsd:annotation>
                    <xsd:documentation>Flag to indicate if the transaction is fradulent.
                        			The transaction is marked declined and fradulent if the flag is true.
                        			If it is not, it is marked as declined.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Reason" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Reason to decline.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="AbstractKeyChangeRequest">
        <xsd:annotation>
          <xsd:documentation>Request object from which all other Admin Gateway requests inherit</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractAdminGatewayRequest">
            <xsd:sequence>
              <xsd:element name="RevokedReason" type="xsd:string" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>The reason for change of key</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="NameValuePair">
        <xsd:sequence>
          <xsd:element name="Name" type="xsd:string" />
          <xsd:element name="Value" type="xsd:anySimpleType" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="UpdateDeviceInfoResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object to update a profile</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for UpdateDeviceResponse operation.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetServerTimeResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Servers as a ping operation to ensure the web service is available.
					Returns the current date and time on the server.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for GetServerTime operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="ServerTime" type="xsd:dateTime">
                        <xsd:annotation>
                          <xsd:documentation>The current data and time on the identity x server.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ExtendedAbstractServiceProviderGatewayRequest">
        <xsd:annotation>
          <xsd:documentation>Abstract Service Provider Request which includes ServiceProviderIID</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractServiceProviderGatewayRequest">
            <xsd:sequence>
              <xsd:element name="ServiceProviderIdentifier" type="xsd:string">
                <xsd:annotation>
                  <xsd:documentation>Identifier to uniquely identify the user within the service provider.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:element name="CheckPalmQualityRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to verify a face data of a service provider user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="Palm" type="Palm" minOccurs="0" maxOccurs="unbounded">
                  <xsd:annotation>
                    <xsd:documentation>Palm biometric data with format of the data.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderTransactionID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>ServiceProvider transaction identifier.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIID" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identifier in IdentitySRP to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="CaptureScriptResults">
        <xsd:annotation>
          <xsd:documentation>The results of the capture script for the script with the identifier:
				CaptureScriptIdentifier</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="CaptureScriptIdentifier" type="xsd:string" />
          <xsd:element name="MetaData" type="CaptureMetaData" minOccurs="0" />
          <xsd:element name="CaptureStepResult" type="CaptureStepResult" minOccurs="1" maxOccurs="unbounded" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ProcessDataResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for ProcessTransactionDataBlock.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for EnrollProfileResponse operation.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="Token" type="xsd:string" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Sponsorship Token.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ChangeDatabaseMACKeyInfo">
        <xsd:annotation>
          <xsd:documentation>Contains information related to Changing a Database Mac Key</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="RevokedReason" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Reason of revoke or change</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NewKeyTypeIdentifierSet" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicates whether or not this request is for a new Key type</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NewKeyTypeIdentifier" type="KeyType" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>The new Key type identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="BiometricScoreInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the biometric score details</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="FusedFMR" type="xsd:double" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Fused FMR score.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="FusedRawScore" type="xsd:double" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Fused Raw score.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:sequence minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="Modality" type="Modality">
              <xsd:annotation>
                <xsd:documentation>Information about each modality used in the biometric fusion</xsd:documentation>
              </xsd:annotation>
            </xsd:element>
          </xsd:sequence>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="ValidateSponsorshipCodeRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to validate a sponsorship code in IdentityX for a service provider user.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="SponsorshipIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely the user's sponsorship id within the service provider realm.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="CustomerIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the user within the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ProfileID" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="SponsorshipCode" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Token or code returned by the server via SMS.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="GetTransactionResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to list transactions</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for a call to list transactions.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="TransactionInfo" type="TransactionInfo" minOccurs="0" maxOccurs="1">
                        <xsd:annotation>
                          <xsd:documentation>Transaction information for the user defined by the service provider</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ReturnStatusType">
        <xsd:annotation>
          <xsd:documentation>Return Status indicating the results of the any Identity X request</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Code" type="xsd:int" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Return code</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Message" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Return message describing any error codes</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="VerifyTransactionResponse">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Response object for a call to verify the transaction</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayResponse">
              <xsd:sequence>
                <xsd:element name="ResponseData" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Response data for transaction verification.</xsd:documentation>
                  </xsd:annotation>
                  <xsd:complexType>
                    <xsd:sequence>
                      <xsd:element name="VerifiedTransactionInfo" type="VerifiedTransactionInfo" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>Verification response in the form of OTP, virtual card details sent to the device.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="ResponseScripts" type="xsd:string" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The scripts to send to the device to capture any further information.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="CapturedDataProcessingResults" type="CaptureScriptResults" minOccurs="0" maxOccurs="unbounded">
                        <xsd:annotation>
                          <xsd:documentation>The results of the capture script server processing.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                      <xsd:element name="EnrollmentPolicy" type="EnrollmentPolicy" minOccurs="0">
                        <xsd:annotation>
                          <xsd:documentation>Enrollment Policy for getting the utterence words in case of Update Profile Transaction.</xsd:documentation>
                        </xsd:annotation>
                      </xsd:element>
                    </xsd:sequence>
                  </xsd:complexType>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="ContactInfo">
        <xsd:annotation>
          <xsd:documentation>Contains the contact information details</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="FirstName" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Firstname of the contact</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="LastName" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Lastname of the contact</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Email" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Contact's email address</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Phone" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Phone Number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CountryCode" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Country code</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Fax" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Fax number</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="InitTransactionRequest">
        <xsd:complexType>
          <xsd:annotation>
            <xsd:documentation>Request object to create a transaction in IdentityX.</xsd:documentation>
          </xsd:annotation>
          <xsd:complexContent>
            <xsd:extension base="AbstractDeviceGatewayRequest">
              <xsd:sequence>
                <xsd:element name="ProfileIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identity or user identifier enrolled or to be enrolled in IdentityX.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="DeviceInfo" type="DeviceInfo">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the device. Generally, device identifier
                        			is UDID in the context of IPhone, PIN in Blackberry etc</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="ServiceProviderIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely identify the service provider.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="PolicyIdentifier" type="xsd:string">
                  <xsd:annotation>
                    <xsd:documentation>Policy identifier that dictates the capture instructions along 
                        			with other verification instructions like GPS.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="TransactionIdentifier" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Identifier to uniquely the user's transaction within the service provider realm.
                        			This is system generated if identifier is not passed in.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="TransactionDescription" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Description of user's transaction.</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
                <xsd:element name="TransactionExpirationDate" type="xsd:dateTime" minOccurs="0">
                  <xsd:annotation>
                    <xsd:documentation>Transaction expiry datetime in UTC</xsd:documentation>
                  </xsd:annotation>
                </xsd:element>
              </xsd:sequence>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:complexType name="VerificationAttempt">
        <xsd:annotation>
          <xsd:documentation>Contains the transaction information for the user.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="VerificationIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Internal identifier for the verification attempt (used in getVerificationAttemptByID)</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionUniqueID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Transaction unique identifier that the verification attempt goes against</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="TransactionIID" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Internal Transaction identifier that the verification attempt goes against</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationDeviceID" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Identifier of the device that made the verification attempt</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationLongitude" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Longitude of where the verification attempt was made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationLatitude" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Latitude of where the verification attempt was made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="isValidated" type="xsd:boolean" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Whether or not the verification passed</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ReasonCode" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Short description of if a verification succeeded or if it failed and why</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AttemptTimestamp" type="xsd:dateTime" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The date and time the verification attempt was made</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AttemptNumber" type="xsd:string" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The number of the attempt</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ReasonDescription" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Describes the outcome of the verification as well as the reason if it failed</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationFace" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Face biometric data with the type of image</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationVoice" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Voice biometric data with format of the data.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationPalm" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Palm biometric data with format of the data.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="VerificationVideo" type="xsd:base64Binary" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Video biometric data with format of the data.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="AttemptTimestampAsString" type="xsd:string" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>The date and time the verification attempt was made as string</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="BiometricScoreInfo" type="BiometricScoreInfo" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Biometric score information</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="Face">
        <xsd:annotation>
          <xsd:documentation>Contains the Face information.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="BiometricData">
            <xsd:sequence>
              <xsd:element name="ImageType" type="ImageType" minOccurs="0" maxOccurs="1">
                <xsd:annotation>
                  <xsd:documentation>Type of the image. Denotes JPG or BMP or JPG2000 etc</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="AbstractIdentityXRequest">
        <xsd:annotation>
          <xsd:documentation>Basic Abstract IdentityX Request</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="wsde:AbstractRequest">
            <xsd:sequence>
              <xsd:element name="Version" type="Version" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>Server's version number which the client is written against.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
    </xsd:schema>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.daon.com/ws/de" xmlns:jaxb="http://java.sun.com/xml/ns/jaxb" targetNamespace="http://www.daon.com/ws/de" elementFormDefault="qualified" jaxb:version="2.0">
      <xsd:import namespace="http://www.daon.com/ws/identityx" />
      <xsd:simpleType name="CustomString2">
        <xsd:annotation>
          <xsd:documentation>A custom string value which may be set by the client
				application.

				The length of this value must be between 0 and 1024
				characters in length.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:minLength value="0" />
          <xsd:maxLength value="1024" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:simpleType name="TypeQualifier">
        <xsd:annotation>
          <xsd:documentation>Biometric data type qualifier.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:int" />
      </xsd:simpleType>
      <xsd:simpleType name="DomainUniqueName">
        <xsd:annotation>
          <xsd:documentation>Identifying Name assigned to the Identity. This is
            	unique within the Domain.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string" />
      </xsd:simpleType>
      <xsd:complexType name="CustomParameters">
        <xsd:annotation>
          <xsd:documentation>This is a set of custom parameters which can be set any
    			arbitrary value.

    			The values can be used in conjunction with the
    			ApplicationIdentifier, ApplicationUserIdentifier and
    			Timestamp to help identify requests submitted to the
    			system.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="CustomLong1" type="xsd:long" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>A custom long value which may be set by the
            			client application.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CustomString1" type="CustomString1" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>A custom string value which may be set by the
            			client application.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="CustomString2" type="CustomString2" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>A custom string value which may be set by the
            			client application.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="AbstractListRequest" abstract="true">
        <xsd:annotation>
          <xsd:documentation>Base type for list Requests.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractRequest">
            <xsd:sequence>
              <xsd:element name="ReturnSetCriteria" type="ReturnSetCriteria" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The return set criteria controls the
                    			returned set of items - number of items
                    			to return and the starting offset.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:complexType name="GroupMembership">
        <xsd:annotation>
          <xsd:documentation>Group membership details</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="GroupIdentifier" type="GroupIdentifier">
            <xsd:annotation>
              <xsd:documentation>Group identifier.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Alias" type="Alias" minOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Alias for the Group.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Blocked" type="xsd:boolean" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Indicates if the GroupMembership is blocked -
            			i.e. the Identity is blocked for this Group.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="UsageQualifier">
        <xsd:annotation>
          <xsd:documentation>Biometric data usage qualifier.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:int" />
      </xsd:simpleType>
      <xsd:complexType name="GenericRequestParameters">
        <xsd:annotation>
          <xsd:documentation>Common request parameters - these values can be used to
            	help identify requests.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="Timestamp" type="xsd:dateTime" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>Timestamp can optionally be populated.

            			This value will not be processed, it is only
            			provided for request logging purposes.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ApplicationIdentifier" type="ApplicationIdentifier" minOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Identifies the submitting client application.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="ApplicationUserIdentifier" type="ApplicationUserIdentifier" minOccurs="1">
            <xsd:annotation>
              <xsd:documentation>Identifies the client application user or
            			application instance.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="AbstractResponse" abstract="true">
        <xsd:annotation>
          <xsd:documentation>Base type for Responses.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="ResponseStatus" type="ResponseStatus">
            <xsd:annotation>
              <xsd:documentation>Returned status for the operation.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="ApplicationUserIdentifier">
        <xsd:annotation>
          <xsd:documentation>Identifies an application user.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:minLength value="1" />
          <xsd:maxLength value="255" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="DomainIdentifier">
        <xsd:annotation>
          <xsd:documentation>A system Domain identifier.

            	The format for the Domain identifier is
            	[identifier-namespace]:[identifier-type]:[identifier].

            	The identifier can be either iid (a Domain's instance
            	id) or uname (a Domain's unique name).

            	Examples of well formed identifiers are 'daon:iid:9001'
            	or 'daon:uname:Admin Domain'.
            	
            	The legacy identifier is the iid e.g. '9001'</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleContent>
          <xsd:extension base="xsd:string" />
        </xsd:simpleContent>
      </xsd:complexType>
      <xsd:simpleType name="ApplicationIdentifier">
        <xsd:annotation>
          <xsd:documentation>Identifies an application.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:minLength value="1" />
          <xsd:maxLength value="255" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:simpleType name="ReturnCode">
        <xsd:restriction base="xsd:int" />
      </xsd:simpleType>
      <xsd:complexType name="IdentityIdentifier">
        <xsd:annotation>
          <xsd:documentation>A system Identity identifier.

            	The format for the Identity identifier is
            	[identifier-namespace]:[identifier-type]:[identifier].

            	The identifier can be either iid (an Identity's instance
            	id) or uname (an Identity's unique name).

            	Examples of well formed identifiers are 'daon:iid:5001'
            	or 'daon:uname:jsmith'.

				The legacy identifier is the iid e.g. '5001'

            	Note: Since an Identity's unique name can be updated
            	identifiers of type uname are not immutable. Identifiers
            	of type iid are immutable since an Identity's instance
            	id is cannot be updated.</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleContent>
          <xsd:extension base="xsd:string" />
        </xsd:simpleContent>
      </xsd:complexType>
      <xsd:complexType name="AbstractOrderedListRequest" abstract="true">
        <xsd:annotation>
          <xsd:documentation>Base type for ordered list Requests.</xsd:documentation>
        </xsd:annotation>
        <xsd:complexContent>
          <xsd:extension base="AbstractListRequest">
            <xsd:sequence>
              <xsd:element name="ReturnSetOrder" type="ReturnSetOrder" default="ASC" minOccurs="0">
                <xsd:annotation>
                  <xsd:documentation>The return set sort order.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
              <xsd:element name="OrderByField" type="Field" minOccurs="0" maxOccurs="unbounded">
                <xsd:annotation>
                  <xsd:documentation>The fields used to sort the return set.</xsd:documentation>
                </xsd:annotation>
              </xsd:element>
            </xsd:sequence>
          </xsd:extension>
        </xsd:complexContent>
      </xsd:complexType>
      <xsd:simpleType name="Alias">
        <xsd:annotation>
          <xsd:documentation>Alias by which Identity is uniquely known within the
            	Group</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string" />
      </xsd:simpleType>
      <xsd:complexType name="AbstractRequest" abstract="true">
        <xsd:annotation>
          <xsd:documentation>Base type for Requests.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
          <xsd:element name="GenericRequestParameters" type="GenericRequestParameters" />
          <xsd:element name="CustomParameters" type="CustomParameters" minOccurs="0" />
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="ResponseStatus">
        <xsd:sequence>
          <xsd:element name="ReturnCode" type="ReturnCode">
            <xsd:annotation>
              <xsd:documentation>The return code indicates the return status of
            			the operation.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Message" type="xsd:string">
            <xsd:annotation>
              <xsd:documentation>A short message corresponding to the return
            			code.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="Description" type="xsd:string" minOccurs="0">
            <xsd:annotation>
              <xsd:documentation>A descriptive message giving more detailed
            			information on the status of the operation.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="ReturnSetOrder">
        <xsd:restriction base="xsd:string">
          <xsd:enumeration value="ASC">
            <xsd:annotation>
              <xsd:documentation>Denotes ascending order.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="DESC">
            <xsd:annotation>
              <xsd:documentation>Denotes descending order.</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:simpleType name="Field">
        <xsd:annotation>
          <xsd:documentation>A searchable field.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:maxLength value="255" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="ReturnSetCriteria">
        <xsd:sequence>
          <xsd:element name="StartOffset" type="xsd:int">
            <xsd:annotation>
              <xsd:documentation>A starting offset for the return set.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
          <xsd:element name="NumberToReturn" type="xsd:int">
            <xsd:annotation>
              <xsd:documentation>The max number of items that are returned in the
            			return set. If not set the max number of
            			defaults to a system setting.</xsd:documentation>
            </xsd:annotation>
          </xsd:element>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="ReturnCodeEnum">
        <xsd:annotation>
          <xsd:appinfo>
            <jaxb:typesafeEnumClass name="ReturnCode" />
          </xsd:appinfo>
          <xsd:documentation>SOAP Operation Return Codes</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="ReturnCode">
          <xsd:enumeration value="0">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="SUCCESS" />
              </xsd:appinfo>
              <xsd:documentation>Success</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="200">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="SYSTEM_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>System Error</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="201">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="UNCATEGORIZED_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>Uncategorized Error</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="202">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="RESOURCE_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>Resource Error</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="203">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_SNAP_IN" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Snap In</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="204">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INSUFFICIENT_PRIVILEGES" />
              </xsd:appinfo>
              <xsd:documentation>Insufficient Privileges</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="205">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_REQUEST" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Request</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="206">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="UNSUPPORTED_OPERATION" />
              </xsd:appinfo>
              <xsd:documentation>Unsupported Operation</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="207">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_QUERY_SYNTAX" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Query Syntax</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="208">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="APPLICATION_IDENTIFIER_NOT_SPECIFIED" />
              </xsd:appinfo>
              <xsd:documentation>Application Identifier Not Specified</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="209">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="APPLICATION_USER_NOT_SPECIFIED" />
              </xsd:appinfo>
              <xsd:documentation>Application User Not Specified</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="210">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_CUSTOM_PARAMETER" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Custom Parameter</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="211">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_TRANSACTION_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Existent Transaction Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="212">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_TRANSACTION_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Transaction Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="213">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_CANDIDATE_LIST_SIZE" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Candidate List Size</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="220">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_IDENTITY" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Identity</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="221">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_IDENTITY_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Identity Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="222">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_IDENTITY" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Identity</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="223">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_IDENTITY" />
              </xsd:appinfo>
              <xsd:documentation>Existent Identity</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="224">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITY_BLOCKED" />
              </xsd:appinfo>
              <xsd:documentation>Identity Blocked</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="225">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITY_INCOMPATIBLE_WITH_DOMAIN" />
              </xsd:appinfo>
              <xsd:documentation>Identity Incompatible With Domain</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="230">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_DOMAIN_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Domain Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="231">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_DOMAIN" />
              </xsd:appinfo>
              <xsd:documentation>Existent Domain</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="232">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_DOMAIN" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Domain</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="233">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="TYPE_QUALIFIER_NOT_SUPPORTED_BY_DOMAIN" />
              </xsd:appinfo>
              <xsd:documentation>Type Qualifier Not Supported By Domain</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="240">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="241">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Existent Policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="242">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="243">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="TYPE_USAGE_NOT_SUPPORTED_BY_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Type/Usage Qualifier Combination Not Supported By Policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="244">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_POLICY_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Policy Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="245">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="POLICY_INCOMPATIBLE_WITH_DOMAIN" />
              </xsd:appinfo>
              <xsd:documentation>Policy Incompatible With Domain</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="246">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="POLICY_INCOMPATIBLE_WITH_GROUP" />
              </xsd:appinfo>
              <xsd:documentation>Policy Incompatible With Group</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="247">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_POLICY_TYPE" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Policy Type</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="248">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ERROR_DELETING_POLICY" />
              </xsd:appinfo>
              <xsd:documentation>Error deleting Policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="250">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_GROUP_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Group Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="251">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_GROUP" />
              </xsd:appinfo>
              <xsd:documentation>Existent Group</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="252">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_GROUP" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Group</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="253">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_GROUP_MEMBERSHIP" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Group Membership</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="254">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_USER_ALIAS" />
              </xsd:appinfo>
              <xsd:documentation>Existent User Alias</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="255">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_USER_ALIAS" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent User Alias</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="256">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="GROUP_NOT_SPECIFIED" />
              </xsd:appinfo>
              <xsd:documentation>Group Not Specified</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="257">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="IDENTITY_NOT_REGISTERED_FOR_GROUP" />
              </xsd:appinfo>
              <xsd:documentation>Identity Not Registered For Group</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="258">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="GROUP_BLOCKED" />
              </xsd:appinfo>
              <xsd:documentation>Group Blocked</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="259">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="GROUP_INCOMPATIBLE_WITH_DOMAIN" />
              </xsd:appinfo>
              <xsd:documentation>Group Incompatible With Domain</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="260">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_BIOMETRIC_DATA" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Biometric Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="261">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_BIOMETRIC_DATA" />
              </xsd:appinfo>
              <xsd:documentation>Existent Biometric Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="262">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_BIOMETRIC_DATA" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Biometric Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="263">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INCOMPLETE_BIOMETRIC_DATA_SET" />
              </xsd:appinfo>
              <xsd:documentation>Incomplete Biometric Data Set</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="264">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="UNSUPPORTED_BIOMETRIC_DATA_FORMAT" />
              </xsd:appinfo>
              <xsd:documentation>Unsupported Biometric Data Format</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="265">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="UNSUPPORTED_USAGE_QUALIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Unsupported Usage Qualifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="266">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_USAGE_QUALIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Usage Qualifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="267">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="UNSUPPORTED_TYPE_QUALIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Unsupported Type Qualifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="268">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_TYPE_QUALIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Type Qualifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="269">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="TYPE_QUALIFIER_NOT_SUPPORTED_FOR_OPERATION" />
              </xsd:appinfo>
              <xsd:documentation>Type Qualifier Not Supported For Operation</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="272">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="BIOMETRIC_DATA_TRANSFORM_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>Error transforming biometric data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="273">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INCOMPATIBLE_BIOMETRIC_DATA_SET" />
              </xsd:appinfo>
              <xsd:documentation>Incompatible Biometric Data Set</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="274">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INCOMPATIBLE_TYPE_USAGE_QUALIFIERS" />
              </xsd:appinfo>
              <xsd:documentation>Type/Usage Qualifier Combination Not Compatible</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="275">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="USAGE_QUALIFIER_NOT_SUPPORTED_FOR_OPERATION" />
              </xsd:appinfo>
              <xsd:documentation>Usage Qualifier Not Supported For Operation</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="276">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="TRANSFORM_POLICY_EXECUTION_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>Error executing transform policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="277">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="TRANSFORM_POLICY_SYNTAX_ERROR" />
              </xsd:appinfo>
              <xsd:documentation>Error parsing transform policy</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="280">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_BIOGRAPHIC_DATA" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Biographic Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="281">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="EXISTENT_BIOGRAPHIC_DATA" />
              </xsd:appinfo>
              <xsd:documentation>Existent Biographic Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="282">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_BIOGRAPHIC_DATA" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Biographic Data</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="283">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="UNDEFINED_BIOGRAPHIC_DATA_ELEMENT" />
              </xsd:appinfo>
              <xsd:documentation>Biographic Data Element Not Defined</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="284">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="BIOGRAPHIC_DATA_ELEMENT_NOT_SUPPORTED_FOR_OPERATION" />
              </xsd:appinfo>
              <xsd:documentation>Biographic Data Element Not Supported For Operation</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="290">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_KEY_IDENTIFIER" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Key Identifier</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="291">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="NON_EXISTENT_KEY" />
              </xsd:appinfo>
              <xsd:documentation>Non Existent Key</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="292">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_PRIVATE_INFORMATION_NONCE" />
              </xsd:appinfo>
              <xsd:documentation>Invalid Private Information Nonce</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="300">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="INVALID_NIST_TRANSACTION" />
              </xsd:appinfo>
              <xsd:documentation>Invalid NIST Transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
          <xsd:enumeration value="301">
            <xsd:annotation>
              <xsd:appinfo>
                <jaxb:typesafeEnumMember name="ERROR_PROCESSING_NIST_TRANSACTION" />
              </xsd:appinfo>
              <xsd:documentation>Error Processing NIST Transaction</xsd:documentation>
            </xsd:annotation>
          </xsd:enumeration>
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:simpleType name="CustomString1">
        <xsd:annotation>
          <xsd:documentation>A custom string value which may be set by the client
				application.

				The length of this value must be between 0 and 255
				characters in length.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:string">
          <xsd:minLength value="0" />
          <xsd:maxLength value="255" />
        </xsd:restriction>
      </xsd:simpleType>
      <xsd:complexType name="GroupIdentifier">
        <xsd:annotation>
          <xsd:documentation>A system Group identifier.

            	The format for the Group identifier is
            	[identifier-namespace]:[identifier-type]:[identifier].

            	The identifier can be either iid (a Group's instance
            	id) or uname (a Group's unique name).

            	Examples of well formed identifiers are 'daon:iid:8005'
            	or 'daon:uname:HelpDesk'.
            	
            	The legacy identifier is the iid e.g. '8005'</xsd:documentation>
        </xsd:annotation>
        <xsd:simpleContent>
          <xsd:extension base="xsd:string" />
        </xsd:simpleContent>
      </xsd:complexType>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="ListSponsorshipRequestMessage">
    <wsdl:part name="ListSponsorshipRequest" element="schema:ListSponsorshipRequest" />
  </wsdl:message>
  <wsdl:message name="ListSponsorshipResponseMessage">
    <wsdl:part name="ListSponsorshipResponse" element="schema:ListSponsorshipResponse" />
  </wsdl:message>
  <wsdl:message name="RefreshKeysRequestMessage">
    <wsdl:part name="RefreshKeysRequest" element="schema:RefreshKeysRequest" />
  </wsdl:message>
  <wsdl:message name="RefreshKeysResponseMessage">
    <wsdl:part name="RefreshKeysResponse" element="schema:RefreshKeysResponse" />
  </wsdl:message>
  <wsdl:message name="UpdatePushTokenRequestMessage">
    <wsdl:part name="UpdatePushTokenRequest" element="schema:UpdatePushTokenRequest" />
  </wsdl:message>
  <wsdl:message name="UpdatePushTokenResponseMessage">
    <wsdl:part name="UpdatePushTokenResponse" element="schema:UpdatePushTokenResponse" />
  </wsdl:message>
  <wsdl:message name="VerifyTransactionRequestMessage">
    <wsdl:part name="VerifyTransactionRequest" element="schema:VerifyTransactionRequest" />
  </wsdl:message>
  <wsdl:message name="VerifyTransactionResponseMessage">
    <wsdl:part name="VerifyTransactionResponse" element="schema:VerifyTransactionResponse" />
  </wsdl:message>
  <wsdl:message name="SponsorDeviceForProfileRequestMessage">
    <wsdl:part name="SponsorDeviceForProfileRequest" element="schema:SponsorDeviceForProfileRequest" />
  </wsdl:message>
  <wsdl:message name="SponsorDeviceForProfileResponseMessage">
    <wsdl:part name="SponsorDeviceForProfileResponse" element="schema:SponsorDeviceForProfileResponse" />
  </wsdl:message>
  <wsdl:message name="CheckProfileStatusRequestMessage">
    <wsdl:part name="CheckProfileStatusRequest" element="schema:CheckProfileStatusRequest" />
  </wsdl:message>
  <wsdl:message name="CheckProfileStatusResponseMessage">
    <wsdl:part name="CheckProfileStatusResponse" element="schema:CheckProfileStatusResponse" />
  </wsdl:message>
  <wsdl:message name="EnrollProfileForIdentityXLiteRequestMessage">
    <wsdl:part name="EnrollProfileForIdentityXLiteRequest" element="schema:EnrollProfileForIdentityXLiteRequest" />
  </wsdl:message>
  <wsdl:message name="EnrollProfileForIdentityXLiteResponseMessage">
    <wsdl:part name="EnrollProfileForIdentityXLiteResponse" element="schema:EnrollProfileForIdentityXLiteResponse" />
  </wsdl:message>
  <wsdl:message name="InitTransactionRequestMessage">
    <wsdl:part name="InitTransactionRequest" element="schema:InitTransactionRequest" />
  </wsdl:message>
  <wsdl:message name="InitTransactionResponseMessage">
    <wsdl:part name="InitTransactionResponse" element="schema:InitTransactionResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateProfileDetailsRequestMessage">
    <wsdl:part name="UpdateProfileDetailsRequest" element="schema:UpdateProfileDetailsRequest" />
  </wsdl:message>
  <wsdl:message name="UpdateProfileDetailsResponseMessage">
    <wsdl:part name="UpdateProfileDetailsResponse" element="schema:UpdateProfileDetailsResponse" />
  </wsdl:message>
  <wsdl:message name="ProcessDataRequestMessage">
    <wsdl:part name="ProcessDataRequest" element="schema:ProcessDataRequest" />
  </wsdl:message>
  <wsdl:message name="ProcessDataResponseMessage">
    <wsdl:part name="ProcessDataResponse" element="schema:ProcessDataResponse" />
  </wsdl:message>
  <wsdl:message name="VerifyFaceRequestMessage">
    <wsdl:part name="VerifyFaceRequest" element="schema:VerifyFaceRequest" />
  </wsdl:message>
  <wsdl:message name="VerifyFaceResponseMessage">
    <wsdl:part name="VerifyFaceResponse" element="schema:VerifyFaceResponse" />
  </wsdl:message>
  <wsdl:message name="TransformRequestMessage">
    <wsdl:part name="TransformRequest" element="schema:TransformRequest" />
  </wsdl:message>
  <wsdl:message name="TransformResponseMessage">
    <wsdl:part name="TransformResponse" element="schema:TransformResponse" />
  </wsdl:message>
  <wsdl:message name="ProcessTransactionDataBlockRequestMessage">
    <wsdl:part name="ProcessTransactionDataBlockRequest" element="schema:ProcessTransactionDataBlockRequest" />
  </wsdl:message>
  <wsdl:message name="ProcessTransactionDataBlockResponseMessage">
    <wsdl:part name="ProcessTransactionDataBlockResponse" element="schema:ProcessTransactionDataBlockResponse" />
  </wsdl:message>
  <wsdl:message name="DeclineTransactionRequestMessage">
    <wsdl:part name="DeclineTransactionRequest" element="schema:DeclineTransactionRequest" />
  </wsdl:message>
  <wsdl:message name="DeclineTransactionResponseMessage">
    <wsdl:part name="DeclineTransactionResponse" element="schema:DeclineTransactionResponse" />
  </wsdl:message>
  <wsdl:message name="GetPalmStencilRequestMessage">
    <wsdl:part name="GetPalmStencilRequest" element="schema:GetPalmStencilRequest" />
  </wsdl:message>
  <wsdl:message name="GetPalmStencilResponseMessage">
    <wsdl:part name="GetPalmStencilResponse" element="schema:GetPalmStencilResponse" />
  </wsdl:message>
  <wsdl:message name="GetServerTimeRequestMessage">
    <wsdl:part name="GetServerTimeRequest" element="schema:GetServerTimeRequest" />
  </wsdl:message>
  <wsdl:message name="GetServerTimeResponseMessage">
    <wsdl:part name="GetServerTimeResponse" element="schema:GetServerTimeResponse" />
  </wsdl:message>
  <wsdl:message name="CheckDeviceStatusRequestMessage">
    <wsdl:part name="CheckDeviceStatusRequest" element="schema:CheckDeviceStatusRequest" />
  </wsdl:message>
  <wsdl:message name="CheckDeviceStatusResponseMessage">
    <wsdl:part name="CheckDeviceStatusResponse" element="schema:CheckDeviceStatusResponse" />
  </wsdl:message>
  <wsdl:message name="ListTransactionRequestMessage">
    <wsdl:part name="ListTransactionRequest" element="schema:ListTransactionRequest" />
  </wsdl:message>
  <wsdl:message name="ListTransactionResponseMessage">
    <wsdl:part name="ListTransactionResponse" element="schema:ListTransactionResponse" />
  </wsdl:message>
  <wsdl:message name="CheckVoiceLivenessRequestMessage">
    <wsdl:part name="CheckVoiceLivenessRequest" element="schema:CheckVoiceLivenessRequest" />
  </wsdl:message>
  <wsdl:message name="CheckVoiceLivenessResponseMessage">
    <wsdl:part name="CheckVoiceLivenessResponse" element="schema:CheckVoiceLivenessResponse" />
  </wsdl:message>
  <wsdl:message name="EnrollProfileRequestMessage">
    <wsdl:part name="EnrollProfileRequest" element="schema:EnrollProfileRequest" />
  </wsdl:message>
  <wsdl:message name="EnrollProfileResponseMessage">
    <wsdl:part name="EnrollProfileResponse" element="schema:EnrollProfileResponse" />
  </wsdl:message>
  <wsdl:message name="ValidateSponsorshipCodeRequestMessage">
    <wsdl:part name="ValidateSponsorshipCodeRequest" element="schema:ValidateSponsorshipCodeRequest" />
  </wsdl:message>
  <wsdl:message name="ValidateSponsorshipCodeResponseMessage">
    <wsdl:part name="ValidateSponsorshipCodeResponse" element="schema:ValidateSponsorshipCodeResponse" />
  </wsdl:message>
  <wsdl:message name="CheckPalmQualityRequestMessage">
    <wsdl:part name="CheckPalmQualityRequest" element="schema:CheckPalmQualityRequest" />
  </wsdl:message>
  <wsdl:message name="CheckPalmQualityResponseMessage">
    <wsdl:part name="CheckPalmQualityResponse" element="schema:CheckPalmQualityResponse" />
  </wsdl:message>
  <wsdl:message name="ValidateDeviceEnablementCodeRequestMessage">
    <wsdl:part name="ValidateDeviceEnablementCodeRequest" element="schema:ValidateDeviceEnablementCodeRequest" />
  </wsdl:message>
  <wsdl:message name="ValidateDeviceEnablementCodeResponseMessage">
    <wsdl:part name="ValidateDeviceEnablementCodeResponse" element="schema:ValidateDeviceEnablementCodeResponse" />
  </wsdl:message>
  <wsdl:message name="GetTransactionRequestMessage">
    <wsdl:part name="GetTransactionRequest" element="schema:GetTransactionRequest" />
  </wsdl:message>
  <wsdl:message name="GetTransactionResponseMessage">
    <wsdl:part name="GetTransactionResponse" element="schema:GetTransactionResponse" />
  </wsdl:message>
  <wsdl:message name="CheckFaceQualityAndLivenessRequestMessage">
    <wsdl:part name="CheckFaceQualityAndLivenessRequest" element="schema:CheckFaceQualityAndLivenessRequest" />
  </wsdl:message>
  <wsdl:message name="CheckFaceQualityAndLivenessResponseMessage">
    <wsdl:part name="CheckFaceQualityAndLivenessResponse" element="schema:CheckFaceQualityAndLivenessResponse" />
  </wsdl:message>
  <wsdl:message name="VerifyIdentityRequestMessage">
    <wsdl:part name="VerifyIdentityRequest" element="schema:VerifyIdentityRequest" />
  </wsdl:message>
  <wsdl:message name="VerifyIdentityResponseMessage">
    <wsdl:part name="VerifyIdentityResponse" element="schema:VerifyIdentityResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateDeviceInfoRequestMessage">
    <wsdl:part name="UpdateDeviceInfoRequest" element="schema:UpdateDeviceInfoRequest" />
  </wsdl:message>
  <wsdl:message name="UpdateDeviceInfoResponseMessage">
    <wsdl:part name="UpdateDeviceInfoResponse" element="schema:UpdateDeviceInfoResponse" />
  </wsdl:message>
  <wsdl:message name="ListDevicesRequestMessage">
    <wsdl:part name="ListDevicesRequest" element="schema:ListDevicesRequest" />
  </wsdl:message>
  <wsdl:message name="ListDevicesResponseMessage">
    <wsdl:part name="ListDevicesResponse" element="schema:ListDevicesResponse" />
  </wsdl:message>
  <wsdl:message name="CheckFaceLivenessRequestMessage">
    <wsdl:part name="CheckFaceLivenessRequest" element="schema:CheckFaceLivenessRequest" />
  </wsdl:message>
  <wsdl:message name="CheckFaceLivenessResponseMessage">
    <wsdl:part name="CheckFaceLivenessResponse" element="schema:CheckFaceLivenessResponse" />
  </wsdl:message>
  <wsdl:message name="ListServiceProvidersRequestMessage">
    <wsdl:part name="ListServiceProvidersRequest" element="schema:ListServiceProvidersRequest" />
  </wsdl:message>
  <wsdl:message name="ListServiceProvidersResponseMessage">
    <wsdl:part name="ListServiceProvidersResponse" element="schema:ListServiceProvidersResponse" />
  </wsdl:message>
  <wsdl:message name="AddDeviceRequestMessage">
    <wsdl:part name="AddDeviceRequest" element="schema:AddDeviceRequest" />
  </wsdl:message>
  <wsdl:message name="AddDeviceResponseMessage">
    <wsdl:part name="AddDeviceResponse" element="schema:AddDeviceResponse" />
  </wsdl:message>
  <wsdl:message name="CheckFaceQualityRequestMessage">
    <wsdl:part name="CheckFaceQualityRequest" element="schema:CheckFaceQualityRequest" />
  </wsdl:message>
  <wsdl:message name="CheckFaceQualityResponseMessage">
    <wsdl:part name="CheckFaceQualityResponse" element="schema:CheckFaceQualityResponse" />
  </wsdl:message>
  <wsdl:message name="ListAuthenticationFactorsRequestMessage">
    <wsdl:part name="ListAuthenticationFactorsRequest" element="schema:ListAuthenticationFactorsRequest" />
  </wsdl:message>
  <wsdl:message name="ListAuthenticationFactorsResponseMessage">
    <wsdl:part name="ListAuthenticationFactorsResponse" element="schema:ListAuthenticationFactorsResponse" />
  </wsdl:message>
  <wsdl:portType name="DeviceGateway_v1r1">
    <wsdl:operation name="ListSponsorship">
      <wsdl:input message="tns:ListSponsorshipRequestMessage" name="ListSponsorshipRequest" />
      <wsdl:output message="tns:ListSponsorshipResponseMessage" name="ListSponsorshipResponse" />
    </wsdl:operation>
    <wsdl:operation name="RefreshKeys">
      <wsdl:input message="tns:RefreshKeysRequestMessage" name="RefreshKeysRequest" />
      <wsdl:output message="tns:RefreshKeysResponseMessage" name="RefreshKeysResponse" />
    </wsdl:operation>
    <wsdl:operation name="UpdatePushToken">
      <wsdl:input message="tns:UpdatePushTokenRequestMessage" name="UpdatePushTokenRequest" />
      <wsdl:output message="tns:UpdatePushTokenResponseMessage" name="UpdatePushTokenResponse" />
    </wsdl:operation>
    <wsdl:operation name="VerifyTransaction">
      <wsdl:input message="tns:VerifyTransactionRequestMessage" name="VerifyTransactionRequest" />
      <wsdl:output message="tns:VerifyTransactionResponseMessage" name="VerifyTransactionResponse" />
    </wsdl:operation>
    <wsdl:operation name="SponsorDeviceForProfile">
      <wsdl:input message="tns:SponsorDeviceForProfileRequestMessage" name="SponsorDeviceForProfileRequest" />
      <wsdl:output message="tns:SponsorDeviceForProfileResponseMessage" name="SponsorDeviceForProfileResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckProfileStatus">
      <wsdl:input message="tns:CheckProfileStatusRequestMessage" name="CheckProfileStatusRequest" />
      <wsdl:output message="tns:CheckProfileStatusResponseMessage" name="CheckProfileStatusResponse" />
    </wsdl:operation>
    <wsdl:operation name="EnrollProfileForIdentityXLite">
      <wsdl:input message="tns:EnrollProfileForIdentityXLiteRequestMessage" name="EnrollProfileForIdentityXLiteRequest" />
      <wsdl:output message="tns:EnrollProfileForIdentityXLiteResponseMessage" name="EnrollProfileForIdentityXLiteResponse" />
    </wsdl:operation>
    <wsdl:operation name="InitTransaction">
      <wsdl:input message="tns:InitTransactionRequestMessage" name="InitTransactionRequest" />
      <wsdl:output message="tns:InitTransactionResponseMessage" name="InitTransactionResponse" />
    </wsdl:operation>
    <wsdl:operation name="UpdateProfileDetails">
      <wsdl:input message="tns:UpdateProfileDetailsRequestMessage" name="UpdateProfileDetailsRequest" />
      <wsdl:output message="tns:UpdateProfileDetailsResponseMessage" name="UpdateProfileDetailsResponse" />
    </wsdl:operation>
    <wsdl:operation name="ProcessData">
      <wsdl:input message="tns:ProcessDataRequestMessage" name="ProcessDataRequest" />
      <wsdl:output message="tns:ProcessDataResponseMessage" name="ProcessDataResponse" />
    </wsdl:operation>
    <wsdl:operation name="VerifyFace">
      <wsdl:input message="tns:VerifyFaceRequestMessage" name="VerifyFaceRequest" />
      <wsdl:output message="tns:VerifyFaceResponseMessage" name="VerifyFaceResponse" />
    </wsdl:operation>
    <wsdl:operation name="Transform">
      <wsdl:input message="tns:TransformRequestMessage" name="TransformRequest" />
      <wsdl:output message="tns:TransformResponseMessage" name="TransformResponse" />
    </wsdl:operation>
    <wsdl:operation name="ProcessTransactionDataBlock">
      <wsdl:input message="tns:ProcessTransactionDataBlockRequestMessage" name="ProcessTransactionDataBlockRequest" />
      <wsdl:output message="tns:ProcessTransactionDataBlockResponseMessage" name="ProcessTransactionDataBlockResponse" />
    </wsdl:operation>
    <wsdl:operation name="DeclineTransaction">
      <wsdl:input message="tns:DeclineTransactionRequestMessage" name="DeclineTransactionRequest" />
      <wsdl:output message="tns:DeclineTransactionResponseMessage" name="DeclineTransactionResponse" />
    </wsdl:operation>
    <wsdl:operation name="GetPalmStencil">
      <wsdl:input message="tns:GetPalmStencilRequestMessage" name="GetPalmStencilRequest" />
      <wsdl:output message="tns:GetPalmStencilResponseMessage" name="GetPalmStencilResponse" />
    </wsdl:operation>
    <wsdl:operation name="GetServerTime">
      <wsdl:input message="tns:GetServerTimeRequestMessage" name="GetServerTimeRequest" />
      <wsdl:output message="tns:GetServerTimeResponseMessage" name="GetServerTimeResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckDeviceStatus">
      <wsdl:input message="tns:CheckDeviceStatusRequestMessage" name="CheckDeviceStatusRequest" />
      <wsdl:output message="tns:CheckDeviceStatusResponseMessage" name="CheckDeviceStatusResponse" />
    </wsdl:operation>
    <wsdl:operation name="ListTransaction">
      <wsdl:input message="tns:ListTransactionRequestMessage" name="ListTransactionRequest" />
      <wsdl:output message="tns:ListTransactionResponseMessage" name="ListTransactionResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckVoiceLiveness">
      <wsdl:input message="tns:CheckVoiceLivenessRequestMessage" name="CheckVoiceLivenessRequest" />
      <wsdl:output message="tns:CheckVoiceLivenessResponseMessage" name="CheckVoiceLivenessResponse" />
    </wsdl:operation>
    <wsdl:operation name="EnrollProfile">
      <wsdl:input message="tns:EnrollProfileRequestMessage" name="EnrollProfileRequest" />
      <wsdl:output message="tns:EnrollProfileResponseMessage" name="EnrollProfileResponse" />
    </wsdl:operation>
    <wsdl:operation name="ValidateSponsorshipCode">
      <wsdl:input message="tns:ValidateSponsorshipCodeRequestMessage" name="ValidateSponsorshipCodeRequest" />
      <wsdl:output message="tns:ValidateSponsorshipCodeResponseMessage" name="ValidateSponsorshipCodeResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckPalmQuality">
      <wsdl:input message="tns:CheckPalmQualityRequestMessage" name="CheckPalmQualityRequest" />
      <wsdl:output message="tns:CheckPalmQualityResponseMessage" name="CheckPalmQualityResponse" />
    </wsdl:operation>
    <wsdl:operation name="ValidateDeviceEnablementCode">
      <wsdl:input message="tns:ValidateDeviceEnablementCodeRequestMessage" name="ValidateDeviceEnablementCodeRequest" />
      <wsdl:output message="tns:ValidateDeviceEnablementCodeResponseMessage" name="ValidateDeviceEnablementCodeResponse" />
    </wsdl:operation>
    <wsdl:operation name="GetTransaction">
      <wsdl:input message="tns:GetTransactionRequestMessage" name="GetTransactionRequest" />
      <wsdl:output message="tns:GetTransactionResponseMessage" name="GetTransactionResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckFaceQualityAndLiveness">
      <wsdl:input message="tns:CheckFaceQualityAndLivenessRequestMessage" name="CheckFaceQualityAndLivenessRequest" />
      <wsdl:output message="tns:CheckFaceQualityAndLivenessResponseMessage" name="CheckFaceQualityAndLivenessResponse" />
    </wsdl:operation>
    <wsdl:operation name="VerifyIdentity">
      <wsdl:input message="tns:VerifyIdentityRequestMessage" name="VerifyIdentityRequest" />
      <wsdl:output message="tns:VerifyIdentityResponseMessage" name="VerifyIdentityResponse" />
    </wsdl:operation>
    <wsdl:operation name="UpdateDeviceInfo">
      <wsdl:input message="tns:UpdateDeviceInfoRequestMessage" name="UpdateDeviceInfoRequest" />
      <wsdl:output message="tns:UpdateDeviceInfoResponseMessage" name="UpdateDeviceInfoResponse" />
    </wsdl:operation>
    <wsdl:operation name="ListDevices">
      <wsdl:input message="tns:ListDevicesRequestMessage" name="ListDevicesRequest" />
      <wsdl:output message="tns:ListDevicesResponseMessage" name="ListDevicesResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckFaceLiveness">
      <wsdl:input message="tns:CheckFaceLivenessRequestMessage" name="CheckFaceLivenessRequest" />
      <wsdl:output message="tns:CheckFaceLivenessResponseMessage" name="CheckFaceLivenessResponse" />
    </wsdl:operation>
    <wsdl:operation name="ListServiceProviders">
      <wsdl:input message="tns:ListServiceProvidersRequestMessage" name="ListServiceProvidersRequest" />
      <wsdl:output message="tns:ListServiceProvidersResponseMessage" name="ListServiceProvidersResponse" />
    </wsdl:operation>
    <wsdl:operation name="AddDevice">
      <wsdl:input message="tns:AddDeviceRequestMessage" name="AddDeviceRequest" />
      <wsdl:output message="tns:AddDeviceResponseMessage" name="AddDeviceResponse" />
    </wsdl:operation>
    <wsdl:operation name="CheckFaceQuality">
      <wsdl:input message="tns:CheckFaceQualityRequestMessage" name="CheckFaceQualityRequest" />
      <wsdl:output message="tns:CheckFaceQualityResponseMessage" name="CheckFaceQualityResponse" />
    </wsdl:operation>
    <wsdl:operation name="ListAuthenticationFactors">
      <wsdl:input message="tns:ListAuthenticationFactorsRequestMessage" name="ListAuthenticationFactorsRequest" />
      <wsdl:output message="tns:ListAuthenticationFactorsResponseMessage" name="ListAuthenticationFactorsResponse" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding type="tns:DeviceGateway_v1r1" name="DeviceGateway_v1r1HttpBinding">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="ListSponsorship">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ListSponsorship" />
      <wsdl:input name="ListSponsorshipRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ListSponsorshipResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RefreshKeys">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/RefreshKeys" />
      <wsdl:input name="RefreshKeysRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="RefreshKeysResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdatePushToken">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/UpdatePushToken" />
      <wsdl:input name="UpdatePushTokenRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="UpdatePushTokenResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="VerifyTransaction">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/VerifyTransaction" />
      <wsdl:input name="VerifyTransactionRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="VerifyTransactionResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SponsorDeviceForProfile">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/SponsorDeviceForProfile" />
      <wsdl:input name="SponsorDeviceForProfileRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="SponsorDeviceForProfileResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckProfileStatus">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckProfileStatus" />
      <wsdl:input name="CheckProfileStatusRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckProfileStatusResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnrollProfileForIdentityXLite">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/EnrollProfileForIdentityXLite" />
      <wsdl:input name="EnrollProfileForIdentityXLiteRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="EnrollProfileForIdentityXLiteResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InitTransaction">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/InitTransaction" />
      <wsdl:input name="InitTransactionRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="InitTransactionResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateProfileDetails">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/UpdateProfileDetails" />
      <wsdl:input name="UpdateProfileDetailsRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="UpdateProfileDetailsResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProcessData">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ProcessData" />
      <wsdl:input name="ProcessDataRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ProcessDataResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="VerifyFace">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/VerifyFace" />
      <wsdl:input name="VerifyFaceRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="VerifyFaceResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Transform">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/Transform" />
      <wsdl:input name="TransformRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="TransformResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ProcessTransactionDataBlock">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ProcessTransactionDataBlock" />
      <wsdl:input name="ProcessTransactionDataBlockRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ProcessTransactionDataBlockResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeclineTransaction">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/DeclineTransaction" />
      <wsdl:input name="DeclineTransactionRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="DeclineTransactionResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPalmStencil">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/GetPalmStencil" />
      <wsdl:input name="GetPalmStencilRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetPalmStencilResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetServerTime">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/GetServerTime" />
      <wsdl:input name="GetServerTimeRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetServerTimeResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckDeviceStatus">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckDeviceStatus" />
      <wsdl:input name="CheckDeviceStatusRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckDeviceStatusResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListTransaction">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ListTransaction" />
      <wsdl:input name="ListTransactionRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ListTransactionResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckVoiceLiveness">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckVoiceLiveness" />
      <wsdl:input name="CheckVoiceLivenessRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckVoiceLivenessResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EnrollProfile">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/EnrollProfile" />
      <wsdl:input name="EnrollProfileRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="EnrollProfileResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateSponsorshipCode">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ValidateSponsorshipCode" />
      <wsdl:input name="ValidateSponsorshipCodeRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidateSponsorshipCodeResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckPalmQuality">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckPalmQuality" />
      <wsdl:input name="CheckPalmQualityRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckPalmQualityResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidateDeviceEnablementCode">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ValidateDeviceEnablementCode" />
      <wsdl:input name="ValidateDeviceEnablementCodeRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ValidateDeviceEnablementCodeResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTransaction">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/GetTransaction" />
      <wsdl:input name="GetTransactionRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="GetTransactionResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckFaceQualityAndLiveness">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckFaceQualityAndLiveness" />
      <wsdl:input name="CheckFaceQualityAndLivenessRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckFaceQualityAndLivenessResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="VerifyIdentity">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/VerifyIdentity" />
      <wsdl:input name="VerifyIdentityRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="VerifyIdentityResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateDeviceInfo">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/UpdateDeviceInfo" />
      <wsdl:input name="UpdateDeviceInfoRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="UpdateDeviceInfoResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListDevices">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ListDevices" />
      <wsdl:input name="ListDevicesRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ListDevicesResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckFaceLiveness">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckFaceLiveness" />
      <wsdl:input name="CheckFaceLivenessRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckFaceLivenessResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListServiceProviders">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ListServiceProviders" />
      <wsdl:input name="ListServiceProvidersRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ListServiceProvidersResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddDevice">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/AddDevice" />
      <wsdl:input name="AddDeviceRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="AddDeviceResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckFaceQuality">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/CheckFaceQuality" />
      <wsdl:input name="CheckFaceQualityRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="CheckFaceQualityResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListAuthenticationFactors">
      <soap:operation soapAction="http://www.daon.com/ws/identityx/DeviceGateway/ListAuthenticationFactors" />
      <wsdl:input name="ListAuthenticationFactorsRequest">
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output name="ListAuthenticationFactorsResponse">
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="DeviceGateway_v1r1">
    <wsdl:port name="DeviceGateway_v1r1HttpBindingPort" binding="tns:DeviceGateway_v1r1HttpBinding">
      <soap:address location="http://localhost:8080/DaonEngine/services/" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>

