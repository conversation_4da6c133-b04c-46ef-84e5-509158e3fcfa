<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
    http://www.springframework.org/schema/beans/spring-beans.xsd 
    http://www.springframework.org/schema/context 
    http://www.springframework.org/schema/context/spring-context.xsd
    http://www.springframework.org/schema/util 
    http://www.springframework.org/schema/util/spring-util.xsd">
	<bean id="checkProfileStatusRequestGenerator" class="com.cit.mpayment.profile.CheckProfileStatusRequestGenerator" />

	<bean id="checkProfileStatusResponseValidator" class="com.cit.mpayment.profile.CheckProfileStatusResponseValidator" />

	<bean id="enrollProfileRequestGenerator" class="com.cit.mpayment.profile.EnrollProfileRequestGenerator" />

	<bean id="enrollProfileResponseValidator" class="com.cit.mpayment.profile.EnrollProfileResponseValidator" />

	<bean id="updateProfileDetailsRequestGenerator" class="com.cit.mpayment.profile.UpdateProfileDetailsRequestGenerator" />

	<bean id="updateProfileDetailsResponseValidator" class="com.cit.mpayment.profile.UpdateProfileDetailsResponseValidator" />

	<bean id="checkFaceQualityRequestGenerator" class="com.cit.mpayment.face.CheckFaceQualityRequestGenerator" />

	<bean id="checkFaceQualityResponseValidator" class="com.cit.mpayment.face.CheckFaceQualityResponseValidator" />
	<bean id="checkDeviceStatusRequestGenerator" class="com.cit.mpayment.device.CheckDeviceStatusRequestGenerator" />

	<bean id="checkDeviceStatusResponseValidator" class="com.cit.mpayment.device.CheckDeviceStatusResponseValidator" />

	<bean id="enrollDeviceRequestGenerator" class="com.cit.mpayment.device.EnrollDeviceRequestGenerator" />

	<bean id="enrollDeviceResponseValidator" class="com.cit.mpayment.device.EnrollDeviceResponseValidator" />
	<bean id="bindCustomerRequestGenerator" class="com.cit.service.provider.BindCustomerRequestGenerator">
		<property name="faceRecognitionProperties">
            <util:properties location="classpath:properties/face-recognition.properties" />
        </property>
	</bean>

	<bean id="bindCustomerResponseValidator" class="com.cit.service.provider.BindCustomerResponseValidator" />

	<bean id="isCustomerBoundRequestGenerator" class="com.cit.service.provider.IsCustomerBoundRequestGenerator">
	    <property name="faceRecognitionProperties">
            <util:properties location="classpath:properties/face-recognition.properties" />
        </property>
	</bean>

	<bean id="isCustomerBoundResponseValidator" class="com.cit.service.provider.IsCustomerBoundResponseValidator" />
	<bean id="verifyCustomerFaceRequestGenerator" class="com.cit.mpayment.face.VerifyCustomerFaceRequestGenerator">
	    <property name="faceRecognitionProperties">
            <util:properties location="classpath:properties/face-recognition.properties" />
        </property>
	</bean>

	<bean id="verifyCustomerFaceResponseValidator" class="com.cit.mpayment.face.VerifyCustomerFaceResponseValidator" />
	
	<bean id="initializationComponent" class="com.cit.mpayment.util.InitializationComponent"/>

</beans>
