####### SERVICE_PROVIDER_IDENTIFIER #####
SERVICE_PROVIDER_IDENTIFIER=vericash
##### Check Profile Status #####
jms.service.face.recognition.check.profile.status.request=face/recognition/check/profile/status/inbound
jms.service.face.recognition.check.profile.status.response=face/recognition/check/profile/status/outbound
##### Enroll Profile #####
jms.service.face.recognition.enroll.profile.request=face/recognition/enroll/profile/inbound
jms.service.face.recognition.enroll.profile.response=face/recognition/enroll/profile/outbound
##### Check Device Status #####
jms.service.face.recognition.check.device.status.request=face/recognition/check/device/status/inbound
jms.service.face.recognition.check.device.status.response=face/recognition/check/device/status/outbound
##### Enroll Device #####
jms.service.face.recognition.enroll.device.request=face/recognition/enroll/device/inbound
jms.service.face.recognition.enroll.device.response=face/recognition/enroll/device/outbound
##### Is Customer Bound #####
jms.service.face.recognition.is.customer.bound.request=face/recognition/is/customer/bound/inbound
jms.service.face.recognition.is.customer.bound.response=face/recognition/is/customer/bound/outbound
##### Bind Customer To Service Provider #####
jms.service.face.recognition.bind.customer.request=face/recognition/bind/customer/inbound
jms.service.face.recognition.bind.customer.response=face/recognition/bind/customer/outbound
##### Check Face Quality #####
jms.service.face.recognition.check.face.quality.request=face/recognition/check/face/quality/inbound
jms.service.face.recognition.check.face.quality.response=face/recognition/check/face/quality/outbound
##### Update Profile Details #####
jms.service.face.recognition.update.profile.details.request=face/recognition/update/profile/details/inbound
jms.service.face.recognition.update.profile.details.response=face/recognition/update/profile/details/outbound
#### Verify Customer Face ####
jms.service.face.recognition.verify.customer.face.request=face/recognition/verify/customer/face/inbound
jms.service.face.recognition.verify.customer.face.response=face/recognition/verify/customer/face/outbound
DEVICE.GATEWAY=com.daon.ws.identityx.devicegateway.DeviceGatewayV1R1_Service
SERVICE.PROVIDER.GATEWAY=com.daon.ws.identityx.serviceprovidergateway.ServiceProviderGatewayV1R1_Service
DEVICE.GATEWAY.PORT=DeviceGateway_v1r1HttpBindingPort
SERVICE.PROVIDER.GATEWAY.PORT=ServiceProviderGateway_v1r1HttpBindingPort
VERIFYFACESERVICE=VerifyIdentity
VERIFY.FACE.RESPONSE.TIMEOUT=60000
#####################################
