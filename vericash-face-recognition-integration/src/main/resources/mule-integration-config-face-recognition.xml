<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms"
	xmlns:spring="http://www.mulesoft.org/schema/mule/spring"
	xmlns:java="http://www.mulesoft.org/schema/mule/java"
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xsi:schemaLocation=
	"http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd 
	http://www.mulesoft.org/schema/mule/spring http://www.mulesoft.org/schema/mule/spring/current/mule-spring.xsd 
	http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd">

		<spring:config name="faceRecognitionIntegrationBeans" files="beans/mule-config-beans-face-recognition.xml" />		
		<configuration-properties file="properties/face-recognition.properties" />
		
		<import file="flows/check-profile-status-face-recognition-external-flow.xml"/>
		<import file="flows/enroll-profile-face-recognition-external-flow.xml"/>
		<import file="flows/check-device-status-face-recognition-external-flow.xml"/>
		<import file="flows/enroll-device-face-recognition-external-flow.xml"/>
		<import file="flows/is-customer-bound-face-recognition-external-flow.xml"/>
		<import file="flows/bind-customer-face-recognition-external-flow.xml"/>
		<import file="flows/check-face-quality-face-recognition-external-flow.xml"/>
		<import file="flows/update-profile-details-face-recognition-external-flow.xml"/>
		<import file="flows/verify-customer-face-external-flow.xml"/>

	</mule>


	
