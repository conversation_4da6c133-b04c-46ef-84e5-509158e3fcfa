<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">
	 
	 
	<wsc:config name="FACE_RECOGNITION_SERVICE_PROVIDER_WSC_CONFIG">
		<wsc:connection wsdlLocation="wsdl/ServiceProviderGateway_v1r1.wsdl" service="ServiceProviderGateway_v1r1" port="${SERVICE.PROVIDER.GATEWAY.PORT}"  address="${face_recognition_service_provider_gateway}">
	        <wsc:custom-transport-configuration>
	            <wsc:default-http-transport-configuration timeout="${BALANCEINQUIRY.TIMEOUT}"/>
	        </wsc:custom-transport-configuration>		
		</wsc:connection>
	</wsc:config>
	
	<flow name="Bind Customer External Flow">

		<jms:listener config-ref="JMS_Config" destination="${jms.service.face.recognition.bind.customer.request}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>
		<logger level="INFO" message="----------------------Enroll Profile External Flow --------------"/>

		<set-variable value="#[correlationId]" variableName="correlationId"/>

		<choice>
			<when expression="#[payload.softFields.'customer_bound' == 'false']">


				<set-variable variableName="businessMessage" value="#[payload]"/>

				<java:invoke
					class="com.cit.service.provider.BindCustomerRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="bindCustomerRequestGenerator">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>				
				<set-variable variableName="requestObject" value="#[payload]" />


				<logger level="INFO" message="---------------------- Bind Customer Request #[payload] --------------" />

				<set-payload value="#[vars.requestObject]" />
				
				<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
				<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
		
				<wsc:consume config-ref="FACE_RECOGNITION_SERVICE_PROVIDER_WSC_CONFIG" operation="BindCustomerToServiceProvider" />
		
				<set-variable variableName="responseSOAPMessage" value="#[payload]" />
				<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
				<set-variable variableName="responseObject" value="#[payload]" />


				<logger level="INFO" message="------------------ Face recognition Bind Customer response is : #[payload] -------------------------"/>


				<set-payload value="#[vars.responseObject]" />

				<java:invoke
					class="com.cit.service.provider.BindCustomerResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.daon.ws.identityx.BindCustomerToServiceProviderResponse)"
					instance="bindCustomerResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
		 	 				arg1: payload as Object {class: "com.daon.ws.identityx.BindCustomerToServiceProviderResponse"}}]]]>
					</java:args>
				</java:invoke>
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>

			</when>

			<otherwise>

				<logger level="INFO" message="------------------ Customer already bound -------------------------"/>
			</otherwise>
		</choice>

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.service.face.recognition.bind.customer.response}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="BindCustomerExternalExceptionStrategy" />
	</flow>

	<error-handler name="BindCustomerExternalExceptionStrategy">
		<on-error-continue>
			<logger level="ERROR" message=" ------------------ Start Bind Customer External Exception Strategy ---------------------" />
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
			<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
			<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
			<logger level="ERROR" message=" ------------------ End Bind Customer External Exception Strategy ---------------------" />
	
<!--			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.service.face.recognition.bind.customer.response}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--				<jms:message correlationId="#[correlationId]">-->
<!--					<jms:body>#[payload]</jms:body>-->
<!--					<jms:properties>-->
<!--						#[{-->
<!--						MULE_CORRELATION_ID: vars.correlationId,-->
<!--						MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--						MULE_CORRELATION_SEQUENCE:'-1'-->
<!--						}]</jms:properties>-->
<!--				</jms:message>-->
<!--			</jms:publish>		-->
		</on-error-continue>	
	</error-handler>

</mule>


	
