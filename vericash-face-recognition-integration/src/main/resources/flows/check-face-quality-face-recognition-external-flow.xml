<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

	<flow name="Check Face Quality External Flow">

		<jms:listener config-ref="JMS_Config" destination="${jms.service.face.recognition.check.face.quality.request}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>		
			
		<logger level="INFO" message="---------------------- Check Face Quality External Flow --------------"/>
		<set-variable variableName="businessMessage" value="#[message.payload]"/>
		<set-variable value="#[correlationId]" variableName="correlationId"/>

		<java:invoke
			class="com.cit.mpayment.face.CheckFaceQualityRequestGenerator"
			method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
			instance="checkFaceQualityRequestGenerator">
			<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
		</java:invoke>		
		<set-variable variableName="requestObject" value="#[payload]" />
		<set-variable variableName="faceData" value="#[payload.face.data]" />
		<script:execute engine="groovy">
			<script:code>
				payload.getFace().setData(null)
				return payload
			</script:code>
		</script:execute>
		
		<logger level="INFO" message="---------------------- Check Face Quality Request #[payload] --------------"/>

		<set-payload value="#[vars.requestObject]" />

		<script:execute engine="groovy">
			<script:code>
				payload.getFace().setData(vars.faceData)
				return payload
			</script:code>
		</script:execute>
		
		<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
		<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />

		<wsc:consume config-ref="FACE_RECOGNITION_DEVICE_GATEWAY_WSC_CONFIG" operation="CheckFaceQuality" />

		<set-variable variableName="responseSOAPMessage" value="#[payload]" />
		<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
		<set-variable variableName="responseObject" value="#[payload]" />

		<logger level="INFO" message="------------------ Face recognition Check Face Quality response is : #[payload] -------------------------" />

		<set-payload value="#[vars.responseObject]" />

		<java:invoke
			class="com.cit.mpayment.face.CheckFaceQualityResponseValidator"
			method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.daon.ws.identityx.CheckFaceQualityResponse)"
			instance="checkFaceQualityResponseValidator">
			<java:args>
				<![CDATA[#[{ arg0: vars.businessMessage,
 	 				arg1: payload as Object {class: "com.daon.ws.identityx.CheckFaceQualityResponse"}}]]]>
			</java:args>
		</java:invoke>		
		<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>



		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.service.face.recognition.check.face.quality.response}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="CheckFaceQualityExternalExceptionStrategy" />
	</flow>

	<error-handler name="CheckFaceQualityExternalExceptionStrategy">
		<on-error-continue>
			<logger level="ERROR" message=" ------------------ Start Check Face Quality External Exception Strategy ---------------------" />
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
			<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
			<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
			<logger level="ERROR" message=" ------------------ End Check Face Quality External Exception Strategy ---------------------"/>
	
<!--			<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.service.face.recognition.check.face.quality.response}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--				<jms:message correlationId="#[correlationId]">-->
<!--					<jms:body>#[payload]</jms:body>-->
<!--					<jms:properties>-->
<!--						#[{-->
<!--						MULE_CORRELATION_ID: vars.correlationId,-->
<!--						MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--						MULE_CORRELATION_SEQUENCE:'-1'-->
<!--						}]</jms:properties>-->
<!--				</jms:message>-->
<!--			</jms:publish>	-->
		</on-error-continue>		
	</error-handler>

</mule>


	
