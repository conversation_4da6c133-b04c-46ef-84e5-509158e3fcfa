<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.cit.mpaymentapp.vericash.face.recognition</groupId>
		<artifactId>vericash-face-recognition</artifactId>
		<version>1.0.0.0</version>
		<relativePath>../vericash-face-recognition</relativePath>
	</parent>
	<artifactId>vericash-face-recognition-integration</artifactId>
	<name>vericash-face-recognition-integration</name>

	<properties>
		<muleVersion>3.4.0</muleVersion>
		<maven.compiler.source>11</maven.compiler.source>
		<maven.compiler.target>11</maven.compiler.target>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.mule</groupId>
			<artifactId>mule-core</artifactId>
			<version>${muleVersion}</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-core</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-api</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-management</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-ws-security</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-local</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-databinding-aegis</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-databinding-jaxb</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-tools-common</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-ws-rm</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-wstx-msv-validation</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-databinding-jibx</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-common</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax.xml</groupId>
			<artifactId>jaxrpc-api</artifactId>
			<version>1.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.cit.mpaymentapp</groupId>
			<artifactId>mpaymentapp-common</artifactId>
			<version>1.0.3-SNAPSHOT</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.cit.mpaymentapp.vericash.face.recognition</groupId>
			<artifactId>vericash-face-recognition-common</artifactId>
			<version>1.0.0.0</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.cit.shared</groupId>
			<artifactId>ErrorHandler</artifactId>
			<version>1.0.3-SNAPSHOT</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.9</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
            <groupId>com.daon</groupId>
            <artifactId>identityx</artifactId>
            <version>0.0.1</version>
        	<type>jar</type>
        	<scope>compile</scope>
        </dependency>
        <dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-core-asl</artifactId>
			<version>1.9.2</version>
			<scope>provided</scope>
		</dependency>
        <dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-engine</artifactId>
			<version>5.8</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bctsp-jdk14</artifactId>
			<version>1.46</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.2.2</version>
			<scope>provided</scope>
		</dependency>
		
				<dependency>
  <groupId>jakarta.xml.bind</groupId>
  <artifactId>jakarta.xml.bind-api</artifactId>
  <version>3.0.0</version>
  <scope>provided</scope>
</dependency>

<dependency>
  <groupId>com.sun.xml.bind</groupId>
  <artifactId>jaxb-impl</artifactId>
  <version>3.0.0</version>
 <scope>provided</scope>
</dependency>
		
        
	</dependencies>
</project>
