package com.cit.moneyrequest.services.viewingMoneyRequests.viewingSingleDetailedRequestForParent;

import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.vericash.apis.dto.response.ResponseStatus;
import com.cit.vericash.jetty.Response;
import com.google.gson.Gson;
import org.springframework.stereotype.Service;

@Service
public class ViewParentDetailedRequestPayForMeService implements ViewParentDetailedRequestStrategy {

    @Override
    public String viewSingleRequestWithDetails(Long moneyRequestId) {
        Gson gson = new Gson();
        Response response = new Response();
        response.setErrorCode(GeneralFailureException.VIEW_MONEY_REQUEST_HAS_ERROR);
        response.setFailureReason("Sorry This Service Has Not Been Implemented Yet");
        response.setResponseStatus(ResponseStatus.Failed);
        return gson.toJson(response);
    }
}
