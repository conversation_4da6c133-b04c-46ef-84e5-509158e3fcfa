package com.cit.mpaymentapp.interceptors;

import javax.interceptor.AroundInvoke;
import javax.interceptor.InvocationContext;

import org.apache.log4j.Logger;

public class DefaultInterceptor {

	private static org.apache.log4j.Logger log = Logger.getLogger (DefaultInterceptor.class);
	
	 @ AroundInvoke
	    public Object log (InvocationContext ctx) throws Exception
	    {
		   log.info ("*** by Default Interception  "+ctx.getMethod().getName()+" ...Started");
	       long start = System.currentTimeMillis();
	       try
	       {
	    	   return ctx.proceed();
	       }
	       catch (Exception e)
	       {
	          throw e;
	       }
	       finally
	       {
	          long time = System.currentTimeMillis() - start;
	          String method = ctx.getClass(). getName() + "."  + ctx.getMethod().getName()+ "()";
	          log.info ("*** TracingInterceptor: the invocation of "+ method +" took "+ time +" ms ");
	       }
	    }
}
