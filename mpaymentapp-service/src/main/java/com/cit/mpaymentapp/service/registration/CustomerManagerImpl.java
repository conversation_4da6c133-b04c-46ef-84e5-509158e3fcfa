package com.cit.mpaymentapp.service.registration;

import com.cit.mpaymentapp.common.allowed.list.AllowedList;
import com.cit.mpaymentapp.common.allowed.list.AllowedListConfig;
import com.cit.mpaymentapp.common.audit.AuditManager;
import com.cit.mpaymentapp.common.billers.BillersLoaderManager;
import com.cit.mpaymentapp.common.blocking.BlockingService;
import com.cit.mpaymentapp.common.customer.devices.CustomerDevicesManager;
import com.cit.mpaymentapp.common.customer.message.*;
import com.cit.mpaymentapp.common.customer.preferences.CustomerSettings;
import com.cit.mpaymentapp.common.projection.CustomerNameAddressProjection;
import com.cit.mpaymentapp.common.projection.CustomerProfilesProjection;
import com.cit.mpaymentapp.common.customer.security.rules.SecurityRulesManager;
import com.cit.mpaymentapp.common.customer.unique.identifier.UserIdManager;
import com.cit.mpaymentapp.common.fees.FeesService;
import com.cit.mpaymentapp.common.foreign.currency.TransferData;
import com.cit.mpaymentapp.common.format.MsisdnFormatterImpl;
import com.cit.mpaymentapp.common.gar.GarManager;
import com.cit.mpaymentapp.common.genericServices.LimitExceededRetrialsManager;
import com.cit.mpaymentapp.common.message.*;
import com.cit.mpaymentapp.common.multi.devices.MultiDevicesConfig;
import com.cit.mpaymentapp.common.notification.NotificationManagerLocal;
import com.cit.mpaymentapp.common.projection.CustomerTypeProjection;
import com.cit.mpaymentapp.common.registration.BusinessEntityManager;
import com.cit.mpaymentapp.common.registration.CustomerManager;
import com.cit.mpaymentapp.common.remote.registration.CustomerRemoteRegistration;
import com.cit.mpaymentapp.common.risk.RiskService;
import com.cit.mpaymentapp.common.scheduler.SchedulerManager;
import com.cit.mpaymentapp.common.security.EncryptionDecryptionManager;
import com.cit.mpaymentapp.common.security.RSASecurity;
import com.cit.mpaymentapp.common.sme.SMEManager;
import com.cit.mpaymentapp.common.transaction.TransactionDefManager;
import com.cit.mpaymentapp.common.transaction.TransactionExecutionManager;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.*;
import com.cit.mpaymentapp.model.Enums.CustomerProfileType;
import com.cit.mpaymentapp.model.WalletInfo;
import com.cit.mpaymentapp.model.Enums.*;
import com.cit.mpaymentapp.model.account.AccountStatus;
import com.cit.mpaymentapp.model.account.BusinessAccount;
import com.cit.mpaymentapp.model.account.GeneralAccountType;
import com.cit.mpaymentapp.model.account.MSISDN;
import com.cit.mpaymentapp.model.allowed.list.AllowedBeneficiaryList;
import com.cit.mpaymentapp.model.biller.AirTimeTSQInquiry;
import com.cit.mpaymentapp.model.biller.BillerStock;
import com.cit.mpaymentapp.model.blocking.BlackListRecord;
import com.cit.mpaymentapp.model.blocking.RegistrationBlackList;
import com.cit.mpaymentapp.model.bulk.BulkCustomerEntity;
import com.cit.mpaymentapp.model.bulk.CustomerBulkRiskProfile;
import com.cit.mpaymentapp.model.businessentities.BusinessEntity;
import com.cit.mpaymentapp.model.businessentities.BusinessEntityStatus;
import com.cit.mpaymentapp.model.businessentities.BusinessUser;
import com.cit.mpaymentapp.model.businessservice.BusinessServiceConfig;
import com.cit.mpaymentapp.model.customer.*;
import com.cit.mpaymentapp.model.customer.Customer.GenderEnum;
import com.cit.mpaymentapp.model.customertypes.CustomerType;
import com.cit.mpaymentapp.model.family.payment.FamilyCustomer;
import com.cit.mpaymentapp.model.fees.FeeProfile;
import com.cit.mpaymentapp.model.fees.FeeValueModel;
import com.cit.mpaymentapp.model.foreign.currency.InternationalBankDetails;
import com.cit.mpaymentapp.model.multi.devices.CustomerDevices;
import com.cit.mpaymentapp.model.nibss.NibssBVNEnquiry;
import com.cit.mpaymentapp.model.payment.PaymentMethod;
import com.cit.mpaymentapp.model.payment.PaymentMethodAttribute;
import com.cit.mpaymentapp.model.payment.PaymentMethodStatus;
import com.cit.mpaymentapp.model.payment.SourcePaymentMethods;
import com.cit.mpaymentapp.model.promo.pricing.CustomerPromoProfile;
import com.cit.mpaymentapp.model.promo.pricing.PromoProfile;
import com.cit.mpaymentapp.model.registration.RegistrationType;
import com.cit.mpaymentapp.model.scheduler.MPJob;
import com.cit.mpaymentapp.model.scheduler.Scheduler;
import com.cit.mpaymentapp.model.security.UserProfile;
import com.cit.mpaymentapp.model.service.CustomerDataChangeLog;
import com.cit.mpaymentapp.model.service.EndToEndTimeRequest;
import com.cit.mpaymentapp.model.service.ServiceConfigMap;
import com.cit.mpaymentapp.model.service.ServiceLog;
import com.cit.mpaymentapp.model.sme.enrollment.BusinessEntityCustomer;
import com.cit.mpaymentapp.model.transaction.TransactionDefinitionSummary;
import com.cit.mpaymentapp.model.transaction.account.ExternalReceiverData;
import com.cit.mpaymentapp.security.des.AESEncryption;
import com.cit.mpaymentapp.security.util.Encoder;
import com.cit.mpaymentapp.service.security.rules.SecurityRulesManagerImpl;
import com.cit.mpaymentapp.service.util.ProjectionsLoaderUtil;
import com.cit.mpaymentapp.service.util.SplitUtils;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PropertyLoaderInt;
import com.cit.shared.error.exception.SecurityException;
import com.cit.shared.error.exception.*;
import com.cit.shared.error.util.ExceptionResolver;
import com.cit.shared.error.util.ExceptionUtil;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import com.citglobal.notifications.Notification;
import com.citglobal.notifications.SMS;
import com.citglobal.notifications.Service;
import com.citglobal.notifications.ServicesDocument;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.NonNull;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.xmlbeans.XmlException;
import org.hibernate.exception.ConstraintViolationException;
import org.jboss.crypto.CryptoUtil;
import org.mvel2.MVEL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.persistence.*;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.PrivateKey;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

//@Remote({ CustomerManagerRemote.class })
//@Local({ CustomerManagerLocal.class })
//@Stateless


//@org.springframework.stereotype.Service("customerManager")
    @Transactional
    public class CustomerManagerImpl implements CustomerManager {

    public static final String CUSTOMER_KEY = "customerKey";
    public static final String THE_RECIPIENT = "the recipient";
    public static final String RECIPIENT_MOBILE_NUMBER = "recipientMobileNumber";
    public static final String RECIPIENT_AMOUNT = "recipientAmount";
    private CustomerRemoteRegistration customerRemoteManager;
    private GarManager garManager;

    private IBaseDao baseDao;

    private EncryptionDecryptionManager encryptionDecryptionMgr;

    private AuditManager auditManager;

    private RiskService riskService;

    private FeesService feesService;

    private TransactionDefManager transactionDefManager;

    private BlockingService blockingManager;

    private TransactionExecutionManager transactionExecutionManager;

    private MessageService messageManager;

    private PropertyLoaderInt propertyLoader;

    private NotificationManagerLocal notificationManager;

    private SchedulerManager schedulerManager;

    private BillersLoaderManager billersLoaderLocal;

    private BusinessEntityManager businessEntityManager;
    private SMEManager smeManager;

    ServicesDocument servicesDocument;

    private SecurityRulesManagerImpl securityRulesManager;

    private final static Logger logger = Logger.getLogger(CustomerManagerImpl.class);
    private static final String FORGET_PASSWORD_SERVICE_CODE = "3005";
    private static final String CHANGE_PIN_SERVICE_CODE = "2901";
    private static final String UPDATE_CUSTOMER_STATMENT_SET_PART = "UPDATE CUSTOMER customer SET ";
    private static final String UPDATE_CUSTOMER_STATMENT_WHERE_ID_KEY = " WHERE customer.identification_Key =:identificationKey";
    private static final String LOGIN_SERVICE_CODE = "4260";
    private static final String EXCEEDED_NUMBER_OF_PIN_RETRIALS_REASON = "Exceed Number of PIN retrials";
    public static final String BLOCKED_BY_PASSWORD = "blocked by password";
    public static final String INVALID_IMEI = "Invalid IMEI";
    private static final String USSD_RESET_PIN_SERVICE_CODE = "42234";
    public static final String FORGET_PIN = "2902";
    private static boolean isFromAddNewDevice = false;
    public static final String NUMBER_NOT_FOUND = "1";
    public static final String NUMBER_INACTIVE = "2";
    public static final String AMOUNT_MUST_BE_GREATER_THAN_ZERO = "3";
    public static final String ENTRY_MOBILE_NUMBER = "entryMobileNumber";
    private volatile Map<String, UsersRiskProfile> maxDailyRiskLimits = null;

    private static final String EMAIL_PATTERN = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
            + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";

    private final String DEFAULTUSERNAMEPATTERNKEY = "USER_NAME_PATTERN";
    private final String DEFAULPASSWORDPATTERNKEY = "PASSWORD_PATTERN";
    private final String PASSWORDVALIDITYPERIOD = "PASSWORD_VALIDITY_PERIOD";
    private final String OLDPASSWORDTRACKED = "OLD_PASSWORD_TRACKED";
    private final static String SYSTEM = "SYSTEM";
    private final static String VERIFY_PASSWORD = "verify password";
    private static final String EXTERNAL_SERVICE_EXCEPTION_MESSAGE = "EXTERNAL_SERVICE_EXCEPTION_MESSAGE";
    private static final String CUSTOMER_QUERY_TEMPLATE =
            "SELECT new com.cit.mpaymentapp.common.projection.CustomerNameAddressProjection( " +
                    "c.firstName, c.middleName, c.lastName, c.gender, c.occupation, " +
                    "c.permanentAddress.street, c.permanentAddress.zipCode, c.permanentAddress.city, c.accountNumber) " +
                    "FROM Customer c WHERE %s = %s";

    private PrivateKey privateKey;
    private RSASecurity rsaSecurity;
    private final SplitUtils splitUtils;
    private final ProjectionsLoaderUtil projectionsLoaderUtil;

    private final String ApiScreensPropertyFileName = "ApiScreens.properties";

    public CustomerManagerImpl(GarManager garManagerBeanFromConfig, IBaseDao baseDao, EncryptionDecryptionManager encryptionDecryptionMgr, AuditManager auditManager, RiskService riskService, FeesService feesService, TransactionDefManager transactionDefManager, BlockingService blockingManager, TransactionExecutionManager transactionExecutionManager, MessageService messageManager, PropertyLoaderInt propertyLoader, NotificationManagerLocal notificationManager, SchedulerManager schedulerManager, BillersLoaderManager billersLoaderLocal, BusinessEntityManager businessEntityManager, SMEManager smeManager, SplitUtils splitUtils, ProjectionsLoaderUtil projectionsLoaderUtil, SecurityRulesManagerImpl securityRulesManager) {
        this.garManager = garManagerBeanFromConfig;
        this.baseDao = baseDao;
        this.encryptionDecryptionMgr = encryptionDecryptionMgr;
        this.auditManager = auditManager;
        this.riskService = riskService;
        this.feesService = feesService;
        this.transactionDefManager = transactionDefManager;
        this.blockingManager = blockingManager;
        this.transactionExecutionManager = transactionExecutionManager;
        this.messageManager = messageManager;
        this.propertyLoader = propertyLoader;
        this.notificationManager = notificationManager;
        this.schedulerManager = schedulerManager;
        this.billersLoaderLocal = billersLoaderLocal;
        this.businessEntityManager = businessEntityManager;
        this.smeManager = smeManager;
        this.splitUtils = splitUtils;
        this.projectionsLoaderUtil = projectionsLoaderUtil;
        this.securityRulesManager=  securityRulesManager;
    }


//	@PostConstruct
//	    public void init() {
//		 System.out.println("fatma start bean");
//		 customerRemoteManager.setCustomerManager(this);
//		 customerDevicesManager.setCustomerManager(this);
//		 garManager.setCustomerManager(this);
//		 customerRemoteManager.setGarManager(this.garManager);
//
//		 System.out.println("fatma start bean"+customerRemoteManager.getCustomerManager());
//		 System.out.println("fatma start bean"+customerDevicesManager.getCustomerManager());
//		 System.out.println("khairala start bean"+garManager.getCustomerManager());
//		 System.out.println("khairala start bean"+customerRemoteManager.getGarManager());
//
//	    }

    @NonNull
    @Qualifier("encryptionDecryptionManagerImpl")
    private EncryptionDecryptionManager encryptionDecryptionManager;
    @NonNull
    @Qualifier("customerDevicesManager")
    private CustomerDevicesManager customerDevicesManager;
    @NonNull
    @Qualifier("limitexceededretrialsManagerImpl")
    LimitExceededRetrialsManager limitExceededRetrialsManager;

    @NonNull
    @Qualifier("userIdManager")
    UserIdManager uniqueIdentifierManager;

 /*   @NonNull
    @Qualifier("securityRulesManager")
    SecurityRulesManager securityRulesManager;*/

    DateFormat frmt = new SimpleDateFormat("yyyy-MM-dd");

    private final long CUSTOMER_ENTITY_TYPE_ID = 1L;
    private static final String PAYMENT_METHOD_JOB_NAME = "exportCustomerPaymentMethod";
    private static final String DEFAULT_LANGUAGE = "en";
    private static final String SELF_REGISTRATION_BY_MOBILE = "4250";
    private static final Long ACTIVE_FAMILY_PROFILE = 1l;

    @Autowired
    DynamicPayloadTransformer dynamicPayloadTransformer;

    private void registerSvaAccountForRegisteredCustomer(Customer customer,BusinessMessage businessMessages) {
        String garShortCode = customer.getCustomerID() + "_SVA";
        boolean isDefault = true;
        boolean allowNotification = true;
        AccountStatus status = AccountStatus.ACTIVE;
        Query query = this.baseDao.getEntityManager().createQuery("select model from SourcePaymentMethods  model where model.isDefault=1 and model.wallet.businessEntityID= :wallet");
        query.setParameter("wallet", customer.getWallet());
        List paymentMethodList = new ArrayList(query.getResultList());
        if ((paymentMethodList != null) && (paymentMethodList.size() > 0)) {
            SourcePaymentMethods paymentMethod = (SourcePaymentMethods) paymentMethodList.get(0);
            BusinessEntity institution = (BusinessEntity) paymentMethod.getInstitutions().iterator().next();
            GeneralAccountType accountType = (GeneralAccountType) paymentMethod.getAccountTypes().iterator().next();
            Map moreAttributes = getPaymentMethodAttributes(paymentMethod.getAttributes());
            try {
                this.garManager.register(customer, garShortCode, isDefault, allowNotification, status, paymentMethod,
                        institution, accountType, null, moreAttributes, null, null,businessMessages);
            } catch (GarException e) {
                e.printStackTrace();
            }
        }
    }

    public BusinessMessage saveAirTimeTsqRecord(BusinessMessage businessMessage) {
        String trxId = String.valueOf(businessMessage.getTransactionInfo().getTransactionId());
        String userReference = getUserRefernce();
        AirTimeTSQInquiry airtimeTSQInquiry = new AirTimeTSQInquiry();
        airtimeTSQInquiry.setUserRefernce(userReference);
        airtimeTSQInquiry.setTrxSummaryID(Long.valueOf(trxId));
        baseDao.save(airtimeTSQInquiry);
        return businessMessage;
    }


    private String getUserRefernce() {
        Date date = new Date();
        Calendar rightNowDate = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("ddMMyyHHmmss");
        String strDate = sdf.format(rightNowDate.getTime());
        long first12digit = generateRandom(12);
        String userRefernce = strDate + first12digit;
        System.out.println("userRefernce is : " + userRefernce);
        return userRefernce;
    }

    public static long generateRandom(int length) {
        Random random = new Random();
        char[] digits = new char[length];
        digits[0] = (char) (random.nextInt(9) + '1');
        for (int i = 1; i < length; i++) {
            digits[i] = (char) (random.nextInt(10) + '0');
        }
        return Long.parseLong(new String(digits));
    }

    public void updateCustomerMsisdn(long customerId, String msisdn, long countryId) {
        this.baseDao.executeDynamicQuery(
                "update BlackListRecord set mobileDeviceNumber='" + msisdn + "' where blockedUserID=" + customerId,
                BlackListRecord.class, true, false);
    }

    public List<com.cit.mpaymentapp.model.risk.RiskProfile> getRiskProfilesByCustomerType(long customerTypeId) {
        String sb = ServiceQueryEngine.getQueryStringToExecute("getRiskProfilesByCustomerType", this.getClass(), String.valueOf((Object) customerTypeId));
        Query query = (Query) this.baseDao.executeNativeQuery(sb);
        List profiles = query.getResultList();
        return profiles;
    }

    public boolean isCustomerAuthorizedForThisServiceType(String keyIdentification, String requiredServiceType) {
        String sb = ServiceQueryEngine.getQueryStringToExecute("isCustomerAuthorizedForThisServiceType", this.getClass(), keyIdentification, requiredServiceType);

        Query query = (Query) this.baseDao.executeNativeQuery(sb);

        return query.getResultList().size() != 0;
    }


    public List<FeeProfile> getFeeProfilesByCustomerType(long customerTypeId) {
        String sb = ServiceQueryEngine.getQueryStringToExecute("getFeeProfilesByCustomerType", this.getClass(), String.valueOf((Object) customerTypeId));
        Query query = (Query) this.baseDao.executeNativeQuery(sb);
        List profiles = query.getResultList();
        return profiles;
    }

    private Map<PaymentMethodAttribute, String> getPaymentMethodAttributes(Set<PaymentMethodAttribute> attributes) {
        Map attributesValues = new HashMap();
        for (Iterator iterator = attributes.iterator(); iterator.hasNext(); ) {
            PaymentMethodAttribute paymentMethodAttribute = (PaymentMethodAttribute) iterator.next();
            attributesValues.put(paymentMethodAttribute, "null");
        }
        return attributesValues;
    }

    public void checkExistanceOfCustomerMSISDN(String msisdn) throws CustomerException {
        String queryStr = "select model from Customer model where model.msisdn='" + msisdn + "'";
        List list = this.baseDao.executeDynamicQuery(queryStr, Customer.class, false, false);
        if ((list != null) && (list.size() > 0)) {
            throw new CustomerException(CustomerException.CUSTOMER_MSISDN_ALREADY_EXISTS);
        }
    }

    public Customer createCustomer(Customer customer) throws CustomerException {
        checkExistanceOfCustomerMSISDN(customer.getMsisdn());
        String activationCode = RegistrationUtils.generateActivationCode();
        String encActivationCode = AESEncryption.encrypt(activationCode);
        customer.setActivationCode(encActivationCode);
        customer.setCreationDate(new Date());
        customer.setLastModifiedDate(new Date());
        if (customer.getIdentificationKey() == null) {
            customer.setIdentificationKey(RegistrationUtils
                    .generateIdentficationKeyFromFormatedMSISDN(customer.getWalletShortCode(), customer.getMsisdn()));

        }
        try {
            this.baseDao.save(customer);

            BusinessEntity wallet = null;
            if (customer.getCreatedBy() != null)
                wallet = (BusinessEntity) baseDao.executeNamedQueryForSingleResult(
                        "BusinessUser.getBusinessEntity.getWallet.getBusinessEntityID", BusinessEntity.class, false,
                        false, customer.getCreatedBy());

            customer.setWallet(customer.getCreatedBy() != null ? wallet.getBusinessEntityID() : customer.getWallet());

            if (customer.getCreatedBy() != null)
                ;
            this.customerRemoteManager.registerSVAAccountAndBankAccountForCustomer(null, customer);
        } catch (Exception exception) {
            if ((exception.getCause().getCause() instanceof ConstraintViolationException)) {
                Throwable cause = exception.getCause().getCause();
                if (((cause instanceof SQLException))
                        && ((cause.getMessage().contains("Violation of UNIQUE KEY constraint"))
                        || (cause.getMessage().contains("Cannot insert duplicate key in object")))) {
                    throw new CustomerException("VAL01022");
                }
            }
        }

        try {
            sendApplicationURL(customer);
        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return customer;
    }

    private boolean checkExistedCustomerAlias(String customerAlias) {
        String queryStr = "select model from Customer model where model.alias= '" + customerAlias + "'";
        List list = this.baseDao.executeDynamicQuery(queryStr, Customer.class, false, false);

        return (list != null) && (list.size() > 0);
    }

    @Override
    @Deprecated
    public Customer createCustomer(Customer customer, UnregisteredCustomer unregisteredCustomer,
                                   boolean createFromPortal) throws CustomerException {
        if (checkExistedCustomerAlias(customer.getAlias())) {
            throw new CustomerException("VAL01050");
        }
        String activationCode = RegistrationUtils.generateActivationCode();
        String encActivationCode = AESEncryption.encrypt(activationCode);
        customer.setActivationCode(encActivationCode);
        customer.setCreationDate(new Date());
        customer.setLastModifiedDate(new Date());
        customer.setIdentificationKey(RegistrationUtils
                .generateIdentficationKeyFromFormatedMSISDN(customer.getWalletShortCode(), customer.getMsisdn()));
        this.baseDao.save(customer);
        if (unregisteredCustomer != null) {
            unregisteredCustomer.setRegisteredId(customer.getCustomerID());
            unregisteredCustomer.setRegisteredDate(new Date());
            this.baseDao.update(unregisteredCustomer);
        }
        return customer;
    }

    public void sendApplicationURL(Customer customer) {
        HashMap parameters = new HashMap();
        parameters.put("customer", customer);
        parameters.put("activation_code", AESEncryption.decrypt(customer.getActivationCode()));
        parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());

        this.notificationManager.sendNotification("SendURLWithActivationCode", parameters);

    }

    public void sendURLWithoutActivationCode(Customer customer) {
        HashMap parameters = new HashMap();
        parameters.put("customer", customer);
        parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());

        this.notificationManager.sendNotification("SendURLWithoutActivationCode", parameters);

    }

    public String reactivate(Long customerId) throws CustomerException {
        if (customerId == null) {
            throw new CustomerException("VAL01023");
        }
        Customer customer = getCustomer(customerId);
        if (customer.getStatus() != Enums.UserStatus.ACTIVE) {
            throw new CustomerException("BUS01011");
        }
        String activationCode = RegistrationUtils.generateActivationCode();
        String encActivationCode = AESEncryption.encrypt(activationCode);
        customer.setActivationCode(encActivationCode);
        customer.setLastModifiedDate(new Date());
        customer.setStatus(Enums.UserStatus.IN_ACTIVE);

        this.baseDao.update(customer);
        this.auditManager.addHistoricalData(customer.getCustomerID().longValue(), 1L, Enums.UserStatus.ACTIVE.name(),
                Enums.UserStatus.IN_ACTIVE.name());
        sendApplicationURL(customer);
        return activationCode;
    }

    public String reactivateWithoutSendingActivationCode(Long customerId) throws CustomerException {
        if (customerId == null) {
            throw new CustomerException("VAL01023");
        }
        Customer customer = getCustomer(customerId);
        if (customer.getStatus() != Enums.UserStatus.ACTIVE) {
            throw new CustomerException("BUS01011");
        }
        String activationCode = RegistrationUtils.generateActivationCode();
        String encActivationCode = AESEncryption.encrypt(activationCode);
        customer.setActivationCode(encActivationCode);
        customer.setLastModifiedDate(new Date());
        customer.setStatus(Enums.UserStatus.IN_ACTIVE);
        this.baseDao.update(customer);
        sendURLWithoutActivationCode(customer);
        return activationCode;
    }


    public void updateCustomer(Customer customer) throws CustomerException {
        customer.setLastModifiedDate(new Date());
        reflectDetailsRelations(customer);
        this.baseDao.update(customer);
    }

    private void reflectDetailsRelations(Customer customer) {
        Customer existedCustomer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        customer.setUserProfile(existedCustomer.getUserProfile());
        customer.setFeeProfile(existedCustomer.getFeeProfile());
        customer.setRiskProfile(existedCustomer.getRiskProfile());
        customer.setDocuments(existedCustomer.getDocuments());
        customer.setBusinessServiceConfigs(existedCustomer.getBusinessServiceConfigs());
    }

    public void updateCustomerDocuments(Long customerId, Set<CustomerDocument> documents) throws CustomerException {
        if (customerId == null) {
            throw new CustomerException("VAL01023");
        }
        Customer customer = (Customer) this.baseDao.findById(Customer.class, customerId);
        customer.setDocuments(documents);
        customer.setLastModifiedDate(new Date());
        this.baseDao.update(customer);
    }

    public void removeCustomer(Customer customer, String reasonOfClosing) throws CustomerException {
        if (customer == null) {
            throw new CustomerException("VAL01023");
        }
        customer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        customer.setStatus(Enums.UserStatus.CLOSED);
        customer.setReasonOfClosing(reasonOfClosing);
        try {
            Set<CustomerGar> gars = this.garManager.getCustomerGarList(customer.getCustomerID());
            if ((gars != null) && (!gars.isEmpty())) {
                for (CustomerGar gar : gars) {
                    gar.setStatus(AccountStatus.CLOSED);
                }
            }
        } catch (GarException e) {
            throw new CustomerException("SYS00002");
        }
        customer.setLastModifiedDate(new Date());
        this.baseDao.update(customer);
        notifyCustomerStatus(customer, "DeleteSubscriber");
    }

    public void reopenCustomer(Customer customer) throws CustomerException {
        if (customer == null) {
            throw new CustomerException("VAL01023");
        }
        customer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        if (customer.getStatus() == Enums.UserStatus.CLOSED) {
            customer.setStatus(Enums.UserStatus.IN_ACTIVE);
            customer.setLastModifiedDate(new Date());
            this.baseDao.update(customer);
            try {
                Set<CustomerGar> gars = this.garManager.getCustomerGarList(customer.getCustomerID());
                if ((gars != null) && (!gars.isEmpty())) {
                    for (CustomerGar gar : gars) {
                        gar.setStatus(AccountStatus.ACTIVE);
                    }
                }
            } catch (GarException e) {
                throw new CustomerException("SYS00002");
            }
            notifyCustomerStatus(customer, "ReopenSubscriber");
        } else {
            throw new CustomerException("BUS01011");
        }
    }

    private void notifyCustomerStatus(Customer customer, String serviceId) {
        HashMap parameters = new HashMap();
        parameters.put("customer", customer);
        parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());

        this.notificationManager.sendNotification(serviceId, parameters);

    }

    public Customer getCustomer(Long customerID) throws CustomerException {
        if (customerID == null) {
            throw new CustomerException("VAL01023");
        }
        Customer customer = this.baseDao.findById(Customer.class, customerID);
        return customer;
    }

    public Customer getCustomer(String userId) throws CustomerException {
        if (userId == null) {
            throw new CustomerException(CustomerException.INVALID_USERNAME_PASSWORD);
        }
        Customer customer = null;
        customer = (Customer) this.baseDao.findSingleResultByProperty(Customer.class, "identificationKey", userId);

        if (customer == null) {
            throw new CustomerException(CustomerException.INVALID_USERNAME_PASSWORD);
        }

        if (customer.getDocuments() != null) {
            customer.getDocuments().size();
        }

        return customer;
    }

    public Customer getCustomerByUserId(String userId) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("BUS01009");
        }
        Customer customer = null;
        customer = (Customer) this.baseDao.findSingleResultByProperty(Customer.class, "userId", userId);
        if (customer.getDocuments() != null) {
            customer.getDocuments().size();
        }
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        return customer;
    }

    public Customer getCustomerMinimalData(String userId) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("BUS01009");
        }
        Customer customer = null;
        String sql = ServiceQueryEngine.getQueryStringToExecute("getCustomerMinimalData", this.getClass(), userId);
        Query query = (Query) this.baseDao.executeNativeQuery(sql);
        try {
            List list = query.getResultList();

            if ((list != null) && (!list.isEmpty())) {

                Object[] result = (Object[]) list.get(0);
                BigDecimal customerID = (BigDecimal) result[0];
                BigDecimal customerAuthenticationType = (BigDecimal) result[1];
                BigDecimal customerStatus = (BigDecimal) result[2];
                Enums.UserStatus status = Enums.UserStatus.values()[customerStatus.intValue()];
                BigDecimal enableIndemnityFlag = (BigDecimal) result[3];
                BigDecimal customerProfileTypeId = (BigDecimal) result[4];

                customer = new Customer();
                customer.setCustomerID(Long.valueOf(customerID.longValue()));
                customer.setCustomerAuthenticationType(Integer.valueOf(customerAuthenticationType.intValue()));
                customer.setStatus(status);
                customer.setCustomerProfileTypeID(customerProfileTypeId.longValue());
                if (enableIndemnityFlag != null)
                    customer.setEnableIndemnityStatus(enableIndemnityFlag.longValue());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        return customer;
    }

    public void assignRiskProfile(Customer customer, com.cit.mpaymentapp.model.risk.RiskProfile riskProfile) {
        Customer currentCustomer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        currentCustomer.setLastModifiedDate(new Date());
        currentCustomer.setRiskProfile(riskProfile.getId());
        this.baseDao.update(currentCustomer);
        this.auditManager.handleAudit(new Object[]{customer, riskProfile},
                Enums.ActivityEnum.RiskAssignProfileToCustomer.id());
    }

    public void assignFeeProfile(Customer customer, FeeProfile feeProfile) {
        Customer currentCustomer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        currentCustomer.setLastModifiedDate(new Date());
        currentCustomer.setFeeProfile(feeProfile.getId());
        this.baseDao.update(currentCustomer);
        this.auditManager.handleAudit(new Object[]{customer, feeProfile},
                Enums.ActivityEnum.FeesAssignProfileToCustomer.id());
    }

    public void assignCustomerProfile(Customer customer, UserProfile userProfile) {
        Customer currentCustomer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        currentCustomer.setLastModifiedDate(new Date());
        currentCustomer.setUserProfile(userProfile.getId());
        this.baseDao.update(currentCustomer);
    }

    @Deprecated
    public void assignTransactionDefinitions(Customer customer,
                                             Set<TransactionDefinitionSummary> transactionDefinitions) {
        Customer currentCustomer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
        currentCustomer.setLastModifiedDate(new Date());
        currentCustomer.setTransactionDefSummaries(transactionDefinitions);
        this.baseDao.update(currentCustomer);
    }

    public void assignServiceConfiguration(Customer customer, Set<BusinessServiceConfig> serviceConfigurations) {
        try {
            Customer currentCustomer = (Customer) this.baseDao.findById(Customer.class, customer.getCustomerID());
            currentCustomer.setBusinessServiceConfigs(serviceConfigurations);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setActivationCode(String userId, String activationCode) throws GeneralFailureException {
        String encActivationCode = AESEncryption.encrypt(activationCode);
        LocalDateTime ldt = LocalDateTime.now();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).format(ldt);
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("setActivationCode", this.getClass(),
                encActivationCode, date, userId);
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr);
        query.executeUpdate();
    }

    public void clearActivationCode(String userId) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("VAL01055");
        }
        LocalDateTime ldt = LocalDateTime.now();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).format(ldt);
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("clearActivationCode", this.getClass(),
                null, date, userId);
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr);

        query.executeUpdate();
    }

    public void setPin(String userId, String PIN) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("VAL01055");
        }
        if (PIN == null) {
            throw new CustomerException("VAL01015");
        }

        validatepinLength(PIN);
        securityRulesManagerLocal.pinNotReptitiveSeqential(PIN);
        LocalDateTime ldt = LocalDateTime.now();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).format(ldt);
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("setPin", this.getClass(),
                hashString(PIN), date, String.valueOf(Integer.valueOf(0)), userId);
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr);

        query.executeUpdate();
    }


    @Override
    public void validatepinLength(String PIN) throws CustomerException {
        String pinRegexKey = "pin.length";
        int pinLength = Integer.parseInt(getPinRegex(pinRegexKey));
        if (PIN.length() != pinLength)
            throw new CustomerException(CustomerException.PIN_DO_NOT_MATCH_PIN_FORMAT);
    }

    public void updateEmail(String userId, String newEmail, String walletShortCode) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("VAL01055");
        }
        if (newEmail == null) {
            throw new CustomerException("VAL809021");
        }
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("updateEmail_select", this.getClass(),
                walletShortCode);
        int userIdType = this.baseDao.executeNativeSingleResultQuery(queryStr);
        String query = "";
        if (userIdType == CustomerUserIdsTypeEnum.EMAIL.ordinal()) {
            query = ServiceQueryEngine.getQueryStringToExecute("updateEmail", this.getClass(), newEmail, userId);
            this.baseDao.executeNativeQuery(query, true);
        } else {
            query = ServiceQueryEngine.getQueryStringToExecute("updateEmailLastModified", this.getClass(), newEmail, userId);
            this.baseDao.executeNativeQuery(query, true);
        }
    }

    private boolean verifyPin(String userId, String customerPin, String PIN, Long serviceLogId)
            throws GeneralFailureException, IOException {
        return verifyPin(userId, customerPin, PIN, false, serviceLogId);
    }

    public boolean verifyPin(String userId, String PIN, Long serviceLogId) throws GeneralFailureException, IOException {
        return verifyPin(userId, PIN, false, serviceLogId);
    }

    private boolean verifyPin(String userId, String customerPin, String enteredPIN, boolean trialsEnabled,
                              Long serviceLogId) throws GeneralFailureException, IOException {
        if (Strings.isNullOrEmpty(enteredPIN)) {
            handleMissingAttributesException("PIN");
        }

        String pinMaxTrials = this.propertyLoader.loadProperty("PIN_MAX_TRIALS");
        int MAX_TRIALS = Integer.parseInt(pinMaxTrials);

        boolean verified = false;

        if (customerPin == null) {
            throw new CustomerException("BUS01009");
        }
        verified = customerPin.equals(hashString(enteredPIN));
        if (trialsEnabled) {
            if (!verified) {
                Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("verifyPin", this.getClass(), userId));
                List results = query.getResultList();
                Integer numberOfTrials = null;
                Long customerID = null;
                if (results.size() > 0) {
                    Object[] result = (Object[]) results.get(0);
                    numberOfTrials = Integer.valueOf(((BigDecimal) result[0]).intValue());
                    customerID = Long.valueOf(((BigDecimal) result[1]).longValue());
                }

                String sb = ServiceQueryEngine.getQueryStringToExecute("verifyPinUpdateLoginTrials", this.getClass(), userId);

                if (numberOfTrials.intValue() + 1 == MAX_TRIALS) {
                    this.blockingManager.blockUser(customerID, Enums.UserType.CUSTOMER, userId, serviceLogId, null,
                            "Exceed Number of PIN retrials");
                } else if (numberOfTrials.intValue() + 1 > MAX_TRIALS) {
                    throw new CustomerException("VAL01013");
                }
                this.baseDao.executeNativeQuery(sb.toString(), true);
            } else {
                String updateQuery = "update Customer model set model.loginTrials=0 , model.loginTrialsPassword=0 where model.identificationKey='"
                        + userId + "'";
                this.baseDao.executeDynamicQuery(updateQuery, String.class, true, false);
            }
        }

        return verified;
    }

    public boolean verifyPin(String userId, String enteredPIN, boolean trialsEnabled, Long serviceLogId)
            throws GeneralFailureException, IOException {
        int noOfTrials = verifyPinCoreImpl(userId, enteredPIN, trialsEnabled, serviceLogId);

        return noOfTrials == 0;
    }

    public int verifyPinCoreImpl(String userId, String enteredPIN, boolean trialsEnabled, Long serviceLogId)
            throws GeneralFailureException, IOException {
        if (Strings.isNullOrEmpty(enteredPIN)) {
            handleMissingAttributesException("PIN");
        }

        String pinMaxTrials = this.propertyLoader.loadProperty("PIN_MAX_TRIALS");
        int MAX_TRIALS = Integer.parseInt(pinMaxTrials);
        boolean isCoolOfEnabled = Boolean.parseBoolean(this.propertyLoader.loadProperty("IS_COOLOFF_ENABLED"));
        String cooloffTimeLimit = this.propertyLoader.loadProperty("Cool_Off_TIME_LIMIT");

        boolean verified = false;

        String customerPin = getCustomerPIN(userId);

        if (customerPin == null) {
            throw new CustomerException("BUS01009");
        }
        verified = customerPin.equals(hashString(enteredPIN));
        if (trialsEnabled) {
            if (!verified) {
                Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("verifyPinCoreImpl", this.getClass(), userId));
                List results = query.getResultList();
                Integer numberOfTrials = null;
                Long customerID = null;
                if (results.size() > 0) {
                    Object[] result = (Object[]) results.get(0);
                    numberOfTrials = Integer.valueOf(((BigDecimal) result[0]).intValue());
                    customerID = Long.valueOf(((BigDecimal) result[1]).longValue());
                }

                String queryStr = ServiceQueryEngine.getQueryStringToExecute("verifyPinCoreImpl_Update", this.getClass(),
                        userId);
                if ((numberOfTrials != null) && ((numberOfTrials = Integer.valueOf(numberOfTrials.intValue() + 1))
                        .intValue() == MAX_TRIALS)) {
                    this.blockingManager.blockUser(customerID, Enums.UserType.CUSTOMER, userId, serviceLogId, null,
                            "Exceed Number of PIN retrials");
                    if (isCoolOfEnabled) {
                        HashMap<String, String> timeRemain = new HashMap<String, String>();
                        timeRemain.put("time", cooloffTimeLimit);
                        throw new CustomerException(CustomerException.COOL_OFF_WAIT_TIME, timeRemain);
                    } else
                        throw new CustomerException("VAL302025");
                }
                System.out.println(getBaseDao().getEntityManager().createNativeQuery(queryStr).executeUpdate());
                return numberOfTrials.intValue();
            }
            String updateQuery = "update Customer model set model.loginTrials=0 where model.identificationKey='"
                    + userId + "'";
            this.baseDao.executeDynamicQuery(updateQuery, String.class, true, false);
            return 0;
        }

        return verified ? 0 : MAX_TRIALS;
    }

    public int verifyPinCoreImplUssd(String userId, String enteredPIN, boolean trialsEnabled, Long serviceLogId)
            throws GeneralFailureException, IOException {
        if (Strings.isNullOrEmpty(enteredPIN)) {
            handleMissingAttributesException("PIN");
        }

        String pinMaxTrials = this.propertyLoader.loadProperty("PIN_MAX_TRIALS");
        int MAX_TRIALS = Integer.parseInt(pinMaxTrials);

        boolean verified = false;

        String customerPin = getCustomerPIN(userId);

        if (customerPin == null) {
            throw new CustomerException("BUS01009");
        }
        verified = customerPin.equals(hashString(enteredPIN));
        if (trialsEnabled) {
            if (!verified) {
                Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("verifyPinCoreImplUssd", this.getClass(), userId));
                List results = query.getResultList();
                Integer numberOfTrials = null;
                Long customerID = null;
                if (results.size() > 0) {
                    Object[] result = (Object[]) results.get(0);
                    numberOfTrials = Integer.valueOf(((BigDecimal) result[0]).intValue());
                    customerID = Long.valueOf(((BigDecimal) result[1]).longValue());
                }

                String queryStr = ServiceQueryEngine.getQueryStringToExecute("verifyPinCoreImpl_Update", this.getClass(),
                        userId);
                if ((numberOfTrials != null) && ((numberOfTrials = Integer.valueOf(numberOfTrials.intValue() + 1))
                        .intValue() == MAX_TRIALS)) {
                    this.blockingManager.blockUser(customerID, Enums.UserType.CUSTOMER, userId, serviceLogId, null,
                            "Exceed Number of PIN retrials");
                    throw new CustomerException("VAL302025");
                }
                System.out.println(getBaseDao().getEntityManager().createNativeQuery(queryStr).executeUpdate());
                return numberOfTrials.intValue();
            }
            String updateQuery = "update Customer model set model.loginTrials=0 where model.identificationKey='"
                    + userId + "'";
            this.baseDao.executeDynamicQuery(updateQuery, String.class, true, false);
            return 0;
        }

        return verified ? 0 : MAX_TRIALS;
    }

    public boolean validate(String userId, String activationCode) throws CustomerException {
        boolean valid = false;
        String encActivationCode = AESEncryption.encrypt(activationCode);
        String sb = ServiceQueryEngine.getQueryStringToExecute("validate", this.getClass(), userId, encActivationCode);
        Query query = (Query) getBaseDao().executeNativeQuery(sb);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            valid = true;
        }
        return valid;
    }

    public boolean isCustomerInNegativeDb(String userKey) throws CustomerException {
        if (userKey == null) {
            throw new CustomerException("VAL01001");
        }
        boolean isCustomerOrPaymentMethodBlocked = false;
        String nativeQuery = ServiceQueryEngine.getQueryStringToExecute("isCustomerInNegativeDb", this.getClass(), userKey);
        Query query = (Query) this.baseDao.executeNativeQuery(nativeQuery);
        List result = query.getResultList();
        if ((result != null) && (result.size() > 0)) {
            throw new CustomerException("VAL01013");
        }

        return isCustomerOrPaymentMethodBlocked;
    }

    @Deprecated
    public boolean isMsisdnAlreadyExists(String msisdn, long walletId) throws CustomerException {
        if (msisdn == null) {
            throw new CustomerException("VAL01001");
        }
        Customer customer = null;
        try {
            customer = findCustomerByMsisdn(msisdn, walletId);
        } catch (CustomerException localCustomerException) {
        }
        return customer != null;
    }

    @Deprecated
    public boolean isMsisdnAlreadyExists(String msisdn) throws CustomerException {
        if (msisdn == null) {
            throw new CustomerException("VAL01001");
        }
        Customer customer = null;
        try {
            customer = findCustomerByMsisdn(msisdn);
        } catch (CustomerException localCustomerException) {
            throw new CustomerException("BUS01009");
        }
        return customer != null;
    }

    public BusinessMessage getAllData(BusinessMessage message) throws CustomerException {

        if (message == null) {
            throw new CustomerException("VAL01023");
        }
        String senderKey = message.getPrimarySenderInfo().getUserKey();
        Long customerId = message.getPrimarySenderInfo().getCustomerId();

        if ((senderKey != null) && customerId != null) {
            CustomerNameAddressProjection senderCustomer = getCustomerNameAddressProjection(customerId);
            updateTargetInformation(message.getPrimarySenderInfo().getPersonalDetails(), senderCustomer);
            message.getSoftFields().put("Customer_Account_Number", senderCustomer.getAccountNumber());
        } else if ((senderKey != null) && (isCustomer(senderKey))) {
            CustomerNameAddressProjection senderCustomer = getCustomerNameAddressProjection(senderKey);
            updateTargetInformation(message.getPrimarySenderInfo().getPersonalDetails(), senderCustomer);
        }

        if (message.getPrimaryReceiverInfo() != null) {
            String receiverKey = message.getPrimaryReceiverInfo().getUserKey();
            if ((receiverKey != null) && (isCustomer(receiverKey))) {
                CustomerNameAddressProjection receiverCustomer = getCustomerNameAddressProjection(receiverKey);
                updateTargetInformation(message.getPrimaryReceiverInfo().getPersonalDetails(), receiverCustomer);
            }
        }


        if (message.getBiller() != null) {
            message.getSoftFields().put("BillerName", message.getBiller().getBillerName());
        }
        return message;
    }

    private CustomerNameAddressProjection getCustomerNameAddressProjection(Long customerId) throws CustomerException {
        if (customerId == null) {
            throw new CustomerException("VAL01023");
        }
        String query = String.format(CUSTOMER_QUERY_TEMPLATE, "c.customerID", customerId);
        return fetchCustomer(query);
    }

    private CustomerNameAddressProjection getCustomerNameAddressProjection(String identificationKey) throws CustomerException {
        if (identificationKey == null) {
            throw new CustomerException(CustomerException.INVALID_USERNAME_PASSWORD);
        }
        String query = String.format(CUSTOMER_QUERY_TEMPLATE, "c.identificationKey", "'" + identificationKey + "'");
        return fetchCustomer(query);
    }

    private CustomerNameAddressProjection fetchCustomer(String query) throws CustomerException {
        CustomerNameAddressProjection customer = this.baseDao.executeDynamicQueryForSingleResult(
                query, CustomerNameAddressProjection.class, false, false);
        if (customer == null) {
            throw new CustomerException(CustomerException.INVALID_USERNAME_PASSWORD);
        }
        return customer;
    }

    private void updateTargetInformation(PersonalDetails personalDetails, CustomerNameAddressProjection customer) {
        if (customer != null){
            personalDetails.setFirstName(customer.getFirstName());
            personalDetails.setMiddleName(customer.getMiddleName());
            personalDetails.setLastName(customer.getLastName());
            if ((customer.getGender() != null)
                    && (com.cit.mpaymentapp.common.message.Gender.values()[customer.getGender().ordinal()] != null)) {
                personalDetails.setGender(com.cit.mpaymentapp.common.message.Gender.values()[customer.getGender().ordinal()]);
            }
            personalDetails.setCityName(customer.getCity());
            personalDetails.setStreet(customer.getStreet());
            personalDetails.setZipCode(customer.getZipCode());
            personalDetails.setOccupation(customer.getOccupation());
        }
    }

    @Deprecated
    public BusinessEntity getBusinessEntityByMSISDN(String msisdn) throws GeneralFailureException {
        BusinessEntity businessEntity = null;
        List MSISDNList = this.baseDao.findByProperty(MSISDN.class, "MSISDN", msisdn, false);
        if ((MSISDNList != null) && (!MSISDNList.isEmpty())) {
            BusinessAccount ba = ((MSISDN) MSISDNList.get(0)).getAccount();
            if (ba == null) {
                throw new BusinessAccountException("BUS02002");
            }
            businessEntity = ba.getBusinessEnityID();

            if (businessEntity == null) {
                throw new GeneralFailureException("SYS01002");
            }
        }

        return businessEntity;
    }

    public void saveCustomerSWK(String userId, String swk) throws Exception {
        if (Strings.isNullOrEmpty(swk)) {
            throw new CustomerException("VAL01026");
        }
        if (Strings.isNullOrEmpty(userId)) {
            throw new CustomerException("VAL01055");
        }
        LocalDateTime ldt = LocalDateTime.now();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).format(ldt);

        String queryStr = ServiceQueryEngine.getQueryStringToExecute("saveCustomerSWK", this.getClass(),
                encryptCustomerSWK(swk), date, userId);
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr);
        query.executeUpdate();
    }

    @Deprecated
    public String getCustomerSWK(String userKey) throws Exception {
        String swk = null;
        if (userKey == null) {
            throw new CustomerException("BUS01009");
        }
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerSWK", this.getClass(), userKey);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            swk = (String) results.get(0);
        } else {
            throw new CustomerException("BUS01009");
        }
        return decryptCustomerSWK(swk);
    }

    @Deprecated
    public String getCustomerNameByMSISDN(String msisdn) throws GeneralFailureException {
        String customerName = null;
        Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("getCustomerNameByMSISDN", this.getClass(), msisdn));
        List results = query.getResultList();
        if (results.size() > 0) {
            Object[] result = (Object[]) results.get(0);
            customerName = result[0] + " " + result[1] + " " + result[2];
        }
        if (customerName == null) {
            query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("getCustomerNameByMSISDNBusinessEntity", this.getClass(), msisdn));

            results = query.getResultList();
            if (results.size() > 0) {
                Object[] result = (Object[]) results.get(0);
                customerName = (String) result[0];
            } else {
                throw new CustomerException("BUS01009");
            }
        }

        return customerName;
    }

    public boolean isCustomer(String userId) {
        Long customerID = getCustomerId(userId);
        return customerID != null;
    }

    public void block(Customer customer, String blockedByName, String blockingReason) throws GeneralFailureException {
        this.blockingManager.blockUser(customer.getCustomerID(), Enums.UserType.CUSTOMER,
                customer.getIdentificationKey(), null, blockedByName, blockingReason);
    }

    public void unblock(Customer customer, String unblockedByName, String unblockingReason)
            throws GeneralFailureException {
        customer = this.blockingManager.releaseUser(customer.getCustomerID(), Enums.UserType.CUSTOMER, unblockedByName,
                unblockingReason);
//
//		int status = customer.getStatus().ordinal();
//
//		Long customerId = customer.getCustomerID();
//		String query = "UPDATE CUSTOMER SET NO_LOGIN_TRIALS = 0 , NO_LOGIN_TRIALS_PASSWORD = 0 , STATUS = "+status+" WHERE CUSTOMER_ID= "+customerId;
//		baseDao.executeNativeQuery(query,true);
        customer.setLoginTrials(Integer.valueOf(0));
        customer.setLoginTrialsPassword(Integer.valueOf(0));
        this.baseDao.update(customer);
    }

    public int getValue() {
        return 3;
    }

    private boolean isOracleDatabaseConnection() {
        Boolean isOracleDatabaseCon = null;
        try {
            isOracleDatabaseCon = Boolean
                    .valueOf(Boolean.parseBoolean(this.propertyLoader.loadProperty("DATABASE_ORACLE_CONNECTION")));
        } catch (IOException e) {
            e.printStackTrace();
        } catch (GeneralFailureException e) {
            e.printStackTrace();
        }
        return isOracleDatabaseCon.booleanValue();
    }

    public Map<Integer, Object> search(CustomerFilter filters, int pagingFrom, int pagingTo, String creationDateStr) {
        Map map = new HashMap();

        String querySb = ServiceQueryEngine.getQueryStringToExecute("search_", this.getClass());
        if (filters.getFirstName() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_FirstName", this.getClass(), filters.getFirstName());
        }
        if (filters.getLastName() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_LastName", this.getClass(), filters.getLastName());
        }
        if (filters.getCreationDate() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_CreationDate", this.getClass(), creationDateStr);
        }
        if (filters.getMsisdn() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_MSISDN", this.getClass(), filters.getMsisdn());
        }
        if (filters.getStatus() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_Status", this.getClass(), String.valueOf(filters.getStatus().ordinal()));
        }
        if (filters.getRegisteringAgent() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_RegistrationAgent", this.getClass(), String.valueOf(filters.getRegisteringAgent()));
        }
        if (filters.getCustomerType() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_CustomerType", this.getClass(), String.valueOf(filters.getCustomerType().getId()));
        }
        if (filters.getWalletId() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_WalletId", this.getClass(), String.valueOf(filters.getWalletId()));
        }
        if (filters.getMiddleName() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_MiddleName", this.getClass(), filters.getMiddleName());
        }
        if (filters.getDateOfBirth() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_DateOfBirth", this.getClass(), frmt.format(filters.getDateOfBirth()));
        }
        if (filters.getCountry() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_Country", this.getClass(), String.valueOf(filters.getCountry().getCountryID()));
        }
        if (filters.getNationality() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_Nationality", this.getClass(), String.valueOf(filters.getNationality().getId()));
        }
        if (filters.getLanguage() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_Language", this.getClass(), String.valueOf(filters.getLanguage().getLanguageID()));
        }
        if (filters.getNationalIdType() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_NationalIdType", this.getClass(), String.valueOf(filters.getNationalIdType().ordinal()));
        }
        if ((filters.getAlias() != null) && (!filters.getAlias().equals(""))) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_Alias", this.getClass(), filters.getAlias());
        }
        if ((filters.getUserId() != null) && (!filters.getUserId().equals(""))) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_UserId", this.getClass(), filters.getUserId());
        }
        if (filters.getGender() != null) {
            querySb += ServiceQueryEngine.getQueryStringToExecute("search_Gender", this.getClass(), String.valueOf(filters.getGender().ordinal()));
        }
        querySb += ServiceQueryEngine.getQueryStringToExecute("search_End", this.getClass());

        Query query = null;
        try {
            query = (Query) getBaseDao().executeNativeQuery(querySb);
            map.put(Integer.valueOf(0), Integer.valueOf(query.getResultList().size()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        querySb += ServiceQueryEngine.getQueryStringToExecute("search_Between", this.getClass(), String.valueOf(pagingFrom + 1), String.valueOf(pagingTo + 1));
        map.put(1, getCustomers(querySb));
        return map;
    }

    private List<Customer> getCustomers(String queryStr) {
        List customers = new ArrayList();
        Query query = null;
        try {
            query = (Query) getBaseDao().executeNativeQuery(queryStr);
            List results = query.getResultList();
            if (results != null) {
                for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
                    Object result = localIterator.next();
                    Object[] r = (Object[]) result;
                    String firstName = (String) r[1];
                    String middleName = (String) r[2];
                    String lastName = (String) r[3];
                    Date creationDate = (Date) r[4];
                    String languageStr = (String) r[5];
                    String countryStr = (String) r[6];
                    Object statusObj = r[7];
                    Integer statusOrdinal = Integer.valueOf(Integer.parseInt("" + statusObj));
                    String msisdn = (String) r[8];
                    Object customerIdObj = r[9];
                    Object walltID = r[10];
                    String walletShortCode = (String) r[11];
                    Customer customer = new Customer();
                    customer.setCustomerID(Long.valueOf(Long.parseLong("" + customerIdObj)));
                    customer.setFirstName(firstName);
                    customer.setMiddleName(middleName);
                    customer.setLastName(lastName);
                    customer.setCreationDate(creationDate);
                    Language language = new Language();
                    language.setLanguage(languageStr);
                    customer.setPreferredLanguage(language);
                    Country country = new Country();
                    country.setCountry(countryStr);
                    customer.setCountry(country);
                    customer.setStatus(Enums.UserStatus.values()[statusOrdinal.intValue()]);
                    customer.setMsisdn(msisdn);
                    customer.setWalletShortCode(walletShortCode);
                    customer.setWallet(Long.valueOf(Long.parseLong("" + walltID)));
                    customers.add(customer);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return customers;
    }

    public IBaseDao getBaseDao() {
        return this.baseDao;
    }

    public void setBaseDao(IBaseDao baseDao) {
        this.baseDao = baseDao;
    }

    public Map<Integer, Object> getTransactionExecutions(Long customerId,
                                                         TransactionExecutionManager.TransactionSide transactionSide, Date fromDate, Date toDate,
                                                         Integer transactionType, Integer transactionStatus, int pagingFrom, int pagingSize) {
        return this.transactionExecutionManager.search(fromDate, toDate, transactionType, transactionStatus,
                transactionSide, pagingFrom, pagingSize, customerId);
    }

    public void refund(Customer customer, String beneficiaryMsisdn) throws CustomerException {
        if (customer.getStatus() != Enums.UserStatus.CLOSED) {
            throw new CustomerException("BUS01011");
        }
        try {
            CustomerGar svaGar = this.garManager.getSvaGar(customer.getCustomerID());
            if (svaGar == null) {
                throw new GarException("BUS202006");
            }
            this.garManager.refund(svaGar.getGarId(), beneficiaryMsisdn);
        } catch (GarException e) {
            throw new CustomerException("BUS01011", e);
        }
    }

    @Deprecated
    public CustomerResponse NIResetPIN(CustomerRequest request) throws CustomerException {
        CustomerResponse response = new CustomerResponse();
        Customer customer = getCustomer(request.getMsisdn());
        customer.setPIN(hashString(request.getNewPin()));
        updateCustomer(customer);
        response.setCode(0);

        return response;
    }

    @Deprecated
    public CustomerResponse NIChangePassword(CustomerRequest request) throws CustomerException {
        CustomerResponse response = new CustomerResponse();
        Customer customer = getCustomer(request.getMsisdn());

        customer.setHandsetPassword(request.getNewHandsetPassword());
        updateCustomer(customer);
        response.setCode(0);

        return response;
    }

    @Deprecated
    public boolean isInNegativeDB(CustomerRequest request) throws CustomerException {
        boolean blocked = false;
        if (this.blockingManager.isBlocked(request.getMsisdn())) {
            throw new CustomerException("VAL01013");
        }
        return blocked;
    }

    @Deprecated
    public boolean isActive(CustomerRequest request) throws GeneralFailureException {
        boolean active = true;
        Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("isActive", this.getClass(), request.getMsisdn(), String.valueOf(UserStatus.ACTIVE.ordinal())));
        List results = query.getResultList();
        if ((results.size() <= 0) || (results == null)) {
            throw new GeneralFailureException("VAL01014");
        }
        return active;
    }

    public String hashString(String string) {
        String passwordHash = CryptoUtil.createPasswordHash("SHA", "BASE64", "UTF-8", null, string, null);
        return passwordHash;
    }

    public Enums.UserStatus getCustomerStatus(String userId) {
        Enums.UserStatus customerStatus = null;
        String dynamicQuery = "select model.status from Customer model where model.identificationKey='" + userId + "'";
        List list = this.baseDao.executeDynamicQuery(dynamicQuery, Enums.UserStatus.class, false, false);
        if ((list != null) && (list.size() > 0)) {
            customerStatus = (Enums.UserStatus) list.get(0);
        }
        return customerStatus;
    }

    public boolean validateCustomer(String userKey, String enteredPIN, Long serviceLogId)
            throws GeneralFailureException, IOException {
        String customerPin = null;
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateCustomer", this.getClass(), userKey);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            Object[] result = (Object[]) results.get(0);
            customerPin = (String) result[1];
        } else {
            return false;
        }
        return (verifyPin(userKey, customerPin, enteredPIN, serviceLogId)) && (isCustomerInNegativeDb(userKey));
    }

    public Long getCustomerId(String userId) {
        Long customerID = null;

        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerId", this.getClass(), userId);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            BigDecimal customerIdBigDecimal = (BigDecimal) resultList.get(0);
            customerID = Long.valueOf(customerIdBigDecimal.longValue());
        }
        return customerID;
    }

    @Override
    public Long getCustomerIdByMsisdn(String msiSdn) throws CustomerException {
        try {
            String nativeQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerIdByMsisdn", this.getClass(), msiSdn);
            Query query = (Query) this.baseDao.executeNativeQuery(nativeQuery);
            return Long.parseLong(query.getSingleResult().toString());
        }catch (Exception ex){
            throw new CustomerException(CustomerException.NUMBER_NOT_FOUND);
        }
    }
    public boolean checkPaymentMethodBelongsToCustomer(Long customerId, Long paymentMethodType,
                                                       Long paymentMethodCode) {

        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("checkPaymentMethodBelongsToCustomer", this.getClass(), String.valueOf(paymentMethodType), String.valueOf(customerId), String.valueOf(AccountStatus.ACTIVE.ordinal()), String.valueOf(paymentMethodCode));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        } else {
            return false;
        }

    }


    public boolean checkIsBusinessOwnerRole(String userId, String WalletShortCode) throws FileNotFoundException, GeneralFailureException {
        boolean is_business_owner = false;

        ArrayList<String> ServiceCodes = new ArrayList(Arrays.asList(smeManager.getApiScreensProperties(ApiScreensPropertyFileName).get("SERVICE_CODES").toString().split(",")));
        List<Long> apiCodes = smeManager.getRoleAndAllowedServices(userId.toUpperCase(), WalletShortCode, ServiceCodes);

        if (apiCodes != null && !apiCodes.isEmpty()) {
            is_business_owner = true;
        }

        return is_business_owner;
    }

    public void validateSenderCanUseSourcePayementMethodAndDestinationPaymentMethodForBusinessOwnerCustomer(
            Long sourcePaymentMethodType, Long destinationPaymentMethodType, Long sourcepaymentMethodCode,
            String userId, Boolean SMEFlag, Long BEParentCustomerId, String CorporateId,
            Long destinationPaymentMethodCode) throws Exception {
        Customer customer = null;
        Customer maincustomer = null;

        customer = getCustomerByUserId(userId);

        BusinessEntityCustomer businessEntityCustomer = baseDao.findById(BusinessEntityCustomer.class,
                customer.getBECustomerId());

        maincustomer = baseDao.findById(Customer.class, businessEntityCustomer.getParentCustomerId());

        if (customer == null || maincustomer == null) {
            throw new CustomerException("BUS01009");
        }

        if (customer.getStatus() != UserStatus.ACTIVE && maincustomer.getStatus() != UserStatus.ACTIVE) {

            throw new CustomerException(CustomerException.Customer_status_is_not_active);
        }

        if (!validateSourecPaymentMethodBelongsToSenderCustomerProfile(sourcePaymentMethodType,
                customer.getCustomerType())
                && !validateSourecPaymentMethodBelongsToSenderCustomerProfile(sourcePaymentMethodType,
                maincustomer.getCustomerType())) {
            throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
        }
        if (!validateDestinationPaymentMethodBelongsToSenderCustomerProfile(destinationPaymentMethodType,
                customer.getCustomerType())
                && !validateDestinationPaymentMethodBelongsToSenderCustomerProfile(destinationPaymentMethodType,
                maincustomer.getCustomerType())) {
            throw new CustomerException(CustomerException.DESTINATION_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
        }
        if (!validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar(sourcePaymentMethodType,
                sourcepaymentMethodCode, customer.getCustomerID())
                && !validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar(sourcePaymentMethodType,
                sourcepaymentMethodCode, maincustomer.getCustomerID())) {
            throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
        }

        if (destinationPaymentMethodCode != null
                && !validateDestinationPaymentMethodIsActive(destinationPaymentMethodType,
                destinationPaymentMethodCode)) {

            throw new CustomerException(CustomerException.DESTINATION_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);

        }

    }

    public void validateSenderCanUseSourcePayementMethodAndDestinationPaymentMethod(Long sourcePaymentMethodType,
                                                                                    Long destinationPaymentMethodType, Long sourcepaymentMethodCode, String userId,
                                                                                    Boolean SMEFlag, Long BEParentCustomerId, String CorporateId, Long destinationPaymentMethodCode) throws Exception {
        CustomerTypeProjection customer = projectionsLoaderUtil.loadCustomerTypeProjectionByUserIdOrBEParentCustomerId(SMEFlag, BEParentCustomerId, userId);

        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        if (customer.getStatus() != UserStatus.ACTIVE) {

            throw new CustomerException(CustomerException.Customer_status_is_not_active);
        }


        if (!validateSourecPaymentMethodBelongsToSenderCustomerProfile(sourcePaymentMethodType,
                customer.getCustomerType())) {
            throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
        }
        if (!validateDestinationPaymentMethodBelongsToSenderCustomerProfile(destinationPaymentMethodType,
                customer.getCustomerType())) {
            throw new CustomerException(CustomerException.DESTINATION_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
        }
        if (!validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar(sourcePaymentMethodType,
                sourcepaymentMethodCode, customer.getCustomerID())) {
            throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
        }

        if (destinationPaymentMethodCode != null && !validateDestinationPaymentMethodIsActive(destinationPaymentMethodType,
                destinationPaymentMethodCode)) {


            throw new CustomerException(CustomerException.DESTINATION_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);


        }


    }

    public void validateSenderPaymentMethod(Long sourcePaymentMethodType,
                                            Long sourcepaymentMethodCode, String userId,
                                            Boolean SMEFlag, Long BEParentCustomerId, String CorporateId) throws Exception {
        Customer customer = null;
        if (SMEFlag != null && SMEFlag) {

            if (BEParentCustomerId != null)
                customer = baseDao.findById(Customer.class, BEParentCustomerId);


        } else {
            customer = getCustomerByUserId(userId);
        }

        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        if (customer.getStatus() != UserStatus.ACTIVE) {

            throw new CustomerException(CustomerException.Customer_status_is_not_active);
        }


        if (!validateSourecPaymentMethodBelongsToSenderCustomerProfile(sourcePaymentMethodType,
                customer.getCustomerType())) {
            throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
        }

        if (!validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar(sourcePaymentMethodType,
                sourcepaymentMethodCode, customer.getCustomerID())) {
            throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
        }


    }

    public void validateSenderPaymentMethodExistAndActive (Long senderPaymentMethodType, Long senderPaymentMethodCode) throws CustomerException {
        if (senderPaymentMethodCode!=null&& senderPaymentMethodType!=null){
            String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateSenderPaymentMethodExistAndActive", this.getClass(), String.valueOf(senderPaymentMethodType), String.valueOf(AccountStatus.ACTIVE.ordinal()), String.valueOf(senderPaymentMethodCode));
            Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
            List resultList = query.getResultList();
            if ((resultList == null) || !(resultList.size() > 0)) {
                throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
            }
        }
    }




    public void validateReceiverPaymentMethodIsActive (Long receiverPaymentMethodType, Long receiverPaymentMethodCode) throws CustomerException {
        if (receiverPaymentMethodCode!=null&& receiverPaymentMethodType!=null){
            String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateDestinationPaymentMethodIsActive", this.getClass(), String.valueOf(receiverPaymentMethodType), String.valueOf(AccountStatus.ACTIVE.ordinal()), String.valueOf(receiverPaymentMethodCode));
            Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
            List resultList = query.getResultList();
            if ((resultList == null) || !(resultList.size() > 0)) {
                throw new CustomerException(CustomerException. DESTINATION_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
            }
        }
    }

    @Override
    public CustomerProfilesProjection getCustomerProfilesProjection(String userKey) {
        TypedQuery<CustomerProfilesProjection> query = baseDao.getEntityManager().createQuery("SELECT new com.cit.mpaymentapp.common.projection.CustomerProfilesProjection(c.customerID, c.identificationKey, c.riskProfile, c.feeProfile) " +
                "FROM Customer c WHERE c.identificationKey = :identificationKey ",
                CustomerProfilesProjection.class);
        query.setParameter("identificationKey", userKey);
        return query.getSingleResult();
    }

    @Override
    public CustomerProfilesProjection findCustomerProfilesByMsisdn(String synonym, String walletShortCode) throws CustomerException {
        CustomerProfilesProjection customer = null;
        try {
            TypedQuery<CustomerProfilesProjection> query = this.baseDao.getEntityManager().createQuery(
                    "select new com.cit.mpaymentapp.common.projection.CustomerProfilesProjection(model.customerID, model.identificationKey, model.riskProfile, model.feeProfile) from Customer model where model.msisdn=:msisdn and model.walletShortCode= :walletShortCode",
                    CustomerProfilesProjection.class);
            query.setParameter("msisdn", synonym);
            query.setParameter("walletShortCode", walletShortCode);
            customer = query.getSingleResult();
        } catch (Exception localException) {
            logger.error(localException.getMessage());
        }
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }
        return customer;
    }

    public boolean checkPaymentMethodBelongsToCustomerAndSVA(Long paymentMethodCode, Long paymentMethodType)
            throws GeneralFailureException {
        CustomerGar customerGar = garManager.getCustomerGar(paymentMethodCode);
        Customer customer = customerGar.getCustomer();
        PaymentMethod paymentMethod = getPaymentMethodById(paymentMethodType);
        if (paymentMethod != null && paymentMethod.getType() != null) {
            if (paymentMethod.getType().ordinal() != PaymentMethodType.STORED_VALUE_ACCOUNT.ordinal()) {
                return false;
            }
        }
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("checkPaymentMethodBelongsToCustomerAndSVA", this.getClass(), String.valueOf(paymentMethodType), String.valueOf(customer.getCustomerID()), String.valueOf(AccountStatus.ACTIVE.ordinal()));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        }
        return false;

    }

    public boolean checkPaymentMethodBelongsToCustomerProfile(Long customerProfileTypeID, Long paymentMethodType,
                                                              Customer customer) {
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("checkPaymentMethodBelongsToCustomerProfile", this.getClass(), String.valueOf(paymentMethodType), String.valueOf(customerProfileTypeID));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        } else {
            return false;
        }
    }

    public String getCustomerPIN(String userId) {
        String customerPin = null;

        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerPIN", this.getClass(), userId);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            customerPin = (String) resultList.get(0);
        }

        return customerPin;
    }

    public CustomerType assignRiskProfilesToCustomerType(CustomerType customerType,
                                                         Set<com.cit.mpaymentapp.model.risk.RiskProfile> riskProfiles, Long defaultRiskId) {
        CustomerType currentCustomerType = (CustomerType) this.baseDao.findById(CustomerType.class,
                customerType.getId());
        currentCustomerType.setRiskProfiles(riskProfiles);
        currentCustomerType.setDefaultRiskId(defaultRiskId);
        currentCustomerType = (CustomerType) this.baseDao.update(currentCustomerType);
        return currentCustomerType;
    }

    public CustomerType assignFeeProfilesToCustomerType(CustomerType agentType, Set<FeeProfile> feeProfiles,
                                                        Long defaultFeeId) {
        CustomerType currentCustomerType = (CustomerType) this.baseDao.findById(CustomerType.class, agentType.getId());
        currentCustomerType.setFeeProfiles(feeProfiles);
        currentCustomerType.setDefaultFeeId(defaultFeeId);
        currentCustomerType = (CustomerType) this.baseDao.update(currentCustomerType);
        return currentCustomerType;
    }

    public List<CustomerType> getAllCustomerTypes(Long walletId) {
        return this.baseDao.findByProperty(CustomerType.class, "wallet.businessEntityID", walletId, false);
    }

    public CustomerType createCustomerType(CustomerType customerType) throws Exception {
        if (customerType == null) {
            throw new GeneralFailureException("VAL01023");
        }
        List customerTypes = this.baseDao.executeDynamicQuery(
                "select model from CustomerType model where model.name='" + customerType.getName()
                        + "' and wallet.businessEntityID=" + customerType.getWallet().getBusinessEntityID(),
                CustomerType.class, false, false);

        if ((customerTypes != null) && (customerTypes.size() > 0)) {
            throw new Exception("Name Already Exists");
        }
        return (CustomerType) this.baseDao.saveEntity(customerType);
    }

    public CustomerType updateCustomerType(CustomerType customerType) throws Exception {
        if (customerType == null) {
            throw new GeneralFailureException("VAL01023");
        }
        List customerTypes = this.baseDao.executeDynamicQuery("select model from CustomerType model where model.name='"
                        + customerType.getName() + "' and wallet.businessEntityID="
                        + customerType.getWallet().getBusinessEntityID() + " and model.id <> " + customerType.getId(),
                CustomerType.class, false, false);

        if ((customerTypes != null) && (customerTypes.size() > 0)) {
            throw new Exception("Name Already Exists");
        }
        return (CustomerType) this.baseDao.update(customerType);
    }

    public void removeCustomerType(CustomerType customerType) {
        try {
            this.baseDao.remove(this.baseDao.update(customerType));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setPassword(String userId, String password) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("VAL01055");
        }
        if (password == null) {
            throw new CustomerException("VAL01023");
        }
        LocalDateTime ldt = LocalDateTime.now();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).format(ldt);
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("setPassword", this.getClass(),
                password, date, userId);
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr);

        query.executeUpdate();
    }

    public boolean verifyPassword(String userId, String password) throws CustomerException {
        boolean isPasswordCorrect = false;
        Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("verifyPassword", this.getClass(), userId, password));
        List results = query.getResultList();
        if (results.size() > 0) {
            isPasswordCorrect = true;
        }
        return isPasswordCorrect;
    }

    public boolean verifyPassword(String userId, String password, boolean trialsEnabled, Long serviceLogId)
            throws GeneralFailureException, IOException {
        if (Strings.isNullOrEmpty(password)) {
            handleMissingAttributesException("PASSWORD");
        }

        String passMaxTrials = this.propertyLoader.loadProperty("PASSWORD_MAX_TRIALS");
        boolean isCoolOfEnabled = Boolean.parseBoolean(this.propertyLoader.loadProperty("IS_COOLOFF_ENABLED"));
        int MAX_TRIALS = Integer.parseInt(passMaxTrials);
        String cooloffTimeLimit = this.propertyLoader.loadProperty("Cool_Off_TIME_LIMIT");

        HashMap<String, String> timeRemain = new HashMap<String, String>();
        timeRemain.put("time", cooloffTimeLimit);

        boolean verified = false;

        Customer customer = getCustomer(userId);
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        if ((customer.getHandsetPassword() == null) && (customer.getCustomerRegistrationType()
                .intValue() == Enums.CustomerRegisterationTypeEnum.USSD.ordinal())) {
            throw new CustomerException("VAL302022");
        }

        if (password != null) {
            String hashedpassword = hashString(password);
            verified = hashedpassword.equals(customer.getHandsetPassword());
        }
        if (trialsEnabled) {
            if (!verified) {
                String hasedPassword = hashString(password);
                verified = hasedPassword.equals(customer.getHandsetPassword());
                if (verified) {
                    String updateQuery = "update Customer model set model.loginTrialsPassword=0 where model.identificationKey='"
                            + userId + "'";
                    this.baseDao.executeDynamicQuery(updateQuery, String.class, true, false);
                    return verified;
                }
                Integer numberOfTrials = customer.getLoginTrialsPassword();
                customer.setLoginTrialsPassword(Integer.valueOf(numberOfTrials.intValue() + 1));

                if (numberOfTrials.intValue() + 2 == MAX_TRIALS) {
                    updateCustomer(customer);
                    throw new CustomerException("VAL012011");
                }
                if (numberOfTrials.intValue() + 1 == MAX_TRIALS) {
                    this.blockingManager.blockUser(customer.getCustomerID(), Enums.UserType.CUSTOMER, userId,
                            serviceLogId, null, "blocked by password");
                    customer.setStatus(Enums.UserStatus.BLOCKED);
                    customer.setBlockingDate(new Date());
                    customer.setReasonOfClosing("blocked by password");
                    customer.setLoginTrialsPassword(Integer.valueOf(MAX_TRIALS));
                    updateCustomer(customer);
                    if (isCoolOfEnabled)
                        throw new CustomerException(CustomerException.COOL_OFF_WAIT_TIME, timeRemain);
                    else
                        throw new CustomerException("VAL302025");
                }
                if (numberOfTrials.intValue() + 1 > MAX_TRIALS) {
                    updateCustomer(customer);
                    if (isCoolOfEnabled)
                        throw new CustomerException(CustomerException.COOL_OFF_WAIT_TIME, timeRemain);
                    else
                        throw new CustomerException("VAL302025");
                }
                if (numberOfTrials.intValue() < MAX_TRIALS) {
                    updateCustomer(customer);
                    throw new CustomerException("VAL01201");
                }
            } else {
                String updateQuery = "update Customer model set model.loginTrialsPassword=0 where model.identificationKey='"
                        + userId + "'";
                this.baseDao.executeDynamicQuery(updateQuery, String.class, true, false);
            }

        }

        return verified;
    }

    public boolean getNewLoginStatus(String userId) throws CustomerException {
        boolean isLogin = false;
        Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("getNewLoginStatus", this.getClass(), userId));
        ;
        List results = query.getResultList();
        if (results.size() > 0) {
            isLogin = true;
        }
        return isLogin;
    }

    @Deprecated
    public Customer findCustomerByMsisdn(String msisdn, long walletId) throws CustomerException {
        Customer customer = null;

        Query query = this.baseDao.getEntityManager().createQuery(
                "select model from Customer model where model.msisdn= :msisdn and model.wallet= :walletId");
        query.setParameter("msisdn", msisdn);

        query.setParameter("walletId", Long.valueOf(walletId));
        List customerList = new ArrayList(query.getResultList());
        if ((customerList != null) && (!customerList.isEmpty())) {
            customer = (Customer) customerList.get(0);
        } else {
            throw new CustomerException("BUS01009");
        }
        return customer;
    }

    public Customer findCustomerByMsisdn(String msisdn, String walletShortCode) throws CustomerException {
        Customer customer = null;

        Query query = this.baseDao.getEntityManager().createQuery(
                "select model from Customer model where model.msisdn=:msisdn and model.walletShortCode= :walletShortCode");
        query.setParameter("msisdn", msisdn);

        query.setParameter("walletShortCode", walletShortCode);
        query.setMaxResults(1);
        try {
            customer = (Customer) query.getSingleResult();
        } catch (Exception localException) {
        }
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }
        return customer;
    }

    public String getCustomerSWKByMsisdn(String msisdn, String walletShortCode) throws CustomerException {
        String swk = null;
        swk = (String) this.baseDao.executeNamedQueryForSingleResult("Customer.getCustomerSWK", String.class, false,
                false, new Object[]{msisdn, walletShortCode});
        if (swk == null) {
            throw new CustomerException("BUS01009");
        }
        return swk;
    }

    public String getCustomerIdentificationKey(String msisdn, String walletShortCode) throws CustomerException {
        String IdentificationKey = null;
        IdentificationKey = (String) this.baseDao.executeNamedQueryForSingleResult(
                "Customer.getCustomerIdentificationKey", String.class, false, false,
                new Object[]{msisdn, walletShortCode});
        if (IdentificationKey == null) {
            throw new CustomerException("BUS01009");
        }
        return IdentificationKey;
    }

    public Customer findCustomerByAlias(String alias, String walletShortCode) throws CustomerException {
        Customer customer = null;
        customer = (Customer) this.baseDao
                .executeDynamicQueryForSingleResult("select model from Customer model where model.alias='" + alias
                        + "' and model.walletShortCode='" + walletShortCode + "'", Customer.class, false, false);

        if (customer == null) {
            throw new CustomerException("BUS01009");
        }
        return customer;
    }

    public Customer findCustomerByMsisdn(String msisdn) throws CustomerException {
        Customer customer = null;

        Query query = this.baseDao.getEntityManager()
                .createQuery("select model from Customer model where model.msisdn=:msisdn");
        query.setParameter("msisdn", msisdn);

        query.setFirstResult(0);
        query.setMaxResults(1);
        try {
            customer = (Customer) query.getSingleResult();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return customer;
    }

    public void updateCustomerWithoutProceed(Customer customer) throws CustomerException {
        customer.setLastModifiedDate(new Date());

        this.baseDao.update(customer);
    }

    public com.cit.mpaymentapp.model.risk.RiskProfile getRiskProfile(String userKey) throws CustomerException {
        com.cit.mpaymentapp.model.risk.RiskProfile riskProfile = null;
        if (userKey != null) {
            riskProfile = (com.cit.mpaymentapp.model.risk.RiskProfile) this.baseDao.executeDynamicQueryForSingleResult(
                    "select riskProfile from RiskProfile riskProfile, Customer customer where customer.riskProfile=riskProfile.id AND customer.identificationKey='"
                            + userKey + "'",
                    com.cit.mpaymentapp.model.risk.RiskProfile.class, false, false); //last flag true is for  cash risk  remove cashing for risk
        } else {
            try {
                List ids = this.baseDao.executeNamedQuery(
                        "SELECT cus.wallet FROM Customer cus WHERE cus.identificationKey='" + userKey + "'", Long.class,
                        false);
                riskProfile = this.riskService.getSystemDefaultRiskProfile(Long.parseLong(ids.get(0).toString()));
            } catch (RiskException e) {
                throw new CustomerException("BUS01010", e);
            }
        }

        return riskProfile;
    }

    public FeeProfile getFeeProfile(String userKey) throws CustomerException {
        FeeProfile feeProfile = null;
        Long feeProfileId = null;
        if (userKey != null) {
            feeProfileId = this.baseDao.executeDynamicQueryForSingleResult(
                    "select feeProfile.id from FeeProfile feeProfile, Customer customer where customer.feeProfile=feeProfile.id AND customer.identificationKey='"
                            + userKey + "'",
                    Long.class, false, true);
            feeProfile = new FeeProfile();
            if (feeProfileId != null) feeProfile.setId(feeProfileId);
        } else {
            try {
                List ids = this.baseDao.executeNamedQuery(
                        "SELECT cus.wallet.shortCode FROM Customer cus WHERE cus.identificationKey='" + userKey + "'",
                        String.class, false);
                feeProfile = this.feesService.getSystemDefaultFeeProfile((String) ids.get(0));
            } catch (FeesException e) {
                throw new CustomerException("BUS01010", e);
            }
        }
        return feeProfile;
    }

    public void resendActivationCode(Customer customer) {
        if (customer.getActivationCode() == null) {
            String activationCode = RegistrationUtils.generateActivationCode();
            String encActivationCode = AESEncryption.encrypt(activationCode);
            customer.setActivationCode(encActivationCode);
            Query query = this.baseDao.getEntityManager().createQuery(
                    "UPDATE Customer c SET c.activationCode =:activationCode WHERE c.customerID=:customerID");
            query.setParameter("activationCode", encActivationCode).setParameter("customerID",
                    customer.getCustomerID());
            query.executeUpdate();
        }
        HashMap parameters = new HashMap();
        try {
            customer = getCustomer(customer.getCustomerID());
        } catch (CustomerException e1) {
            e1.printStackTrace();
        }
        String decActivationCode = AESEncryption.decrypt(customer.getActivationCode());
        parameters.put("customer", customer);
        parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());
        parameters.put("activation_code", decActivationCode);

        this.notificationManager.sendNotification("ResendCustomerActivationCode", parameters);

    }

    @Transactional
    public Customer getCustomerByMsisdn(String msisdn) throws CustomerException {
        Customer customer = null;
        List<Customer> customers = this.baseDao.findByProperty(Customer.class, "msisdn", msisdn, false);
        if ((customers != null) && (customers.size() > 0)) {
            customer = (Customer) customers.get(0);
        }

        return customer;
    }

    @Override
    public String getCustomerNameById(Long Id) {
        try {
            String customerName = String.valueOf(this.baseDao.executeNamedQueryForSingleResult("Customer.getNameByCustomerID", Customer.class, false, false, Id));
            return customerName;
        } catch (RuntimeException e) {
            e.printStackTrace();
        }
        return null;
    }

    public Customer getCustomerByEmail(String email) throws CustomerException {
        Customer customer = null;
        List<Customer> customers = this.baseDao.findByProperty(Customer.class, "email", email, false);
        if ((customers != null) && (customers.size() > 0)) {
            customer = (Customer) customers.get(0);
        }

        return customer;
    }

    public boolean isActive(String userKey, String walletShort) throws CustomerException {
        List customerList = this.baseDao.executeNamedQuery("Customer.findByUserKey", Customer.class, false, false,
                userKey, walletShort);

        if ((customerList == null) || (customerList.isEmpty())) {
            throw new CustomerException("BUS01009");
        }
        Customer customer = (Customer) customerList.get(0);

        return customer.getStatus() == Enums.UserStatus.ACTIVE;
    }

    public void runPaymentMethodScheduler(Scheduler scheduler) throws CustomerException {
        MPJob paymentMethodJob = null;
        if (scheduler != null) {
            try {
                paymentMethodJob = (MPJob) this.baseDao
                        .findByProperty(MPJob.class, "name", "exportCustomerPaymentMethod", false).get(0);
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (paymentMethodJob != null) {
                this.schedulerManager.addScheduler(paymentMethodJob, scheduler);
//				scheduler.setStatus(Scheduler.SchedulerStatus.Pending);
            }
        }

    }

    public void exportCustomerPaymentMethod() throws CustomerException {
        List<CustomerGar> customerGarList = null;
        List paymentMethodList = null;
        try {
            String natvieQuery = "select model from CustomerGar model where model.paymentMethod.type = 1 and model.paymentMethod.availability = 1 and model.paymentMethodStatus = 0";
            customerGarList = this.baseDao.executeDynamicQuery(natvieQuery, CustomerGar.class, false, false);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if ((customerGarList != null) && (customerGarList.size() > 0)) {
            boolean flag = true;

            for (CustomerGar c : customerGarList) {
                CustomerGar updatedGarObject = c;
                updatedGarObject.setPaymentMethodStatus(PaymentMethodStatus.ISSUED);
                try {
                    this.baseDao.update(updatedGarObject);
                } catch (Exception e) {
                    flag = false;

                    e.printStackTrace();
                }

            }

            if (flag) {
                String filePath = "";
                try {
                    filePath = this.propertyLoader.loadProperty("USERS_FILE_PATH");
                    if ((filePath == null) || (filePath.equals(""))) {
                        filePath = System.getProperty("user.home") + "/users.txt";
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                File usersFile = new File(filePath);

                if (!usersFile.exists()) {
                    try {
                        usersFile.createNewFile();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                BufferedWriter bw = null;
                try {
                    FileWriter fw = new FileWriter(usersFile.getAbsoluteFile());
                    bw = new BufferedWriter(fw);
                    for (CustomerGar c : customerGarList) {
                        bw.write(createFileKey(c.getCustomer()) + "," + c.getCustomer().getMsisdn() + ";");
                    }

                } catch (IOException e) {
                    e.printStackTrace();
                    try {
                        bw.close();
                    } catch (IOException ex) {
                        ex.printStackTrace();
                    }
                } finally {
                    try {
                        bw.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public Customer getCustomer(String msisdn, String walletShortCode) throws CustomerException {
        Customer customer = null;

        customer = (Customer) this.baseDao
                .executeDynamicQueryForSingleResult(
                        "select model from Customer model where model.msisdn='" + msisdn
                                + "' and model.walletShortCode = '" + walletShortCode + "'",
                        Customer.class, false, false);

        return customer;
    }

    private String getMsisdnFormat(String msisdn) {
        String formattedMsisdn = msisdn.substring(msisdn.length() - 10, msisdn.length());
        return formattedMsisdn;
    }

    private String createFileKey(Customer customer) {
        String countryCode = customer.getCountry().getCountryCode();
        String msisdn = customer.getMsisdn();

        StringBuilder key = new StringBuilder("");
        if ((customer != null) && (customer.getWalletShortCode() != null) && (countryCode != null)
                && (msisdn != null)) {
            key.append(customer.getWalletShortCode());
            key.append(countryCode.substring(countryCode.lastIndexOf('0') + 1));
            key.append(msisdn.substring(msisdn.indexOf('0') + 1));
        }

        return key.toString();
    }

    public boolean isCustomerServiceRestricted(Enums.PaymentMethodType paymentMethodType, Long serviceTypeId) {
        Query query = (Query) getBaseDao().executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("isCustomerServiceRestricted", this.getClass(), String.valueOf(paymentMethodType.ordinal()), String.valueOf(serviceTypeId)));
        List results = query.getResultList();

        return (results != null) && (results.size() > 0);
    }

    public boolean isAliasAlreadyExists(Long customerId, String alias, long walletId) {
        String query = "select model from Customer model where model.alias='" + alias + "' and model.wallet="
                + walletId;
        if (customerId != null) {
            query = query + " and model.customerID <> " + customerId;
        }
        List customerList = this.baseDao.executeDynamicQuery(query, Customer.class, false, false);

        return (customerList != null) && (!customerList.isEmpty());
    }

    public boolean isRegistrationNumberAlreadyExists(Long customerId, String registrationNumber, long walletId) {
        String query = "select model from Customer model where model.registrationNumber='" + registrationNumber
                + "' and model.wallet=" + walletId;
        if (customerId != null) {
            query = query + " and model.customerID <> " + customerId;
        }
        List customerList = this.baseDao.executeDynamicQuery(query, Customer.class, false, false);

        return (customerList != null) && (!customerList.isEmpty());
    }

    public void activate(BusinessMessage message) throws GeneralFailureException {
        if (message.getPrimarySenderInfo().getUserKey() == null) {
            throw new CustomerException("VAL01055");
        }
        if (message.getPrimarySenderInfo().getImei() == null) {
            throw new CustomerException("VAL01003");
        }
        Customer customer = getCustomer(message.getPrimarySenderInfo().getUserKey());
        customer.setStatus(Enums.UserStatus.ACTIVE);
        customer.setActivationDate(new Date());
        customer.setIMEI(message.getPrimarySenderInfo().getImei());
        this.baseDao.update(customer);
        this.auditManager.addHistoricalData(customer.getCustomerID().longValue(), 1L, Enums.UserStatus.IN_ACTIVE.name(),
                Enums.UserStatus.ACTIVE.name());

        boolean registerSvaAccount = false;
        try {
            registerSvaAccount = Boolean
                    .parseBoolean(this.propertyLoader.loadProperty("REGISTER_SVA_ACCOUNT_WHEN_ACTIVATE_CUSTOMER"));
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (registerSvaAccount) {
            CustomerGar customerGar = this.garManager.getSvaGar(customer.getCustomerID());
            if (customerGar == null) {
                registerSvaAccountForRegisteredCustomer(customer,message);
            }
        }
    }

    public void activate(String userId) throws GeneralFailureException {
        if (userId == null) {
            throw new CustomerException("VAL01055");
        }
        Customer customer = getCustomer(userId);
        if (customer.getStatus() != Enums.UserStatus.IN_ACTIVE) {
            throw new CustomerException("BUS01011");
        }

        customer.setStatus(Enums.UserStatus.ACTIVE);
        customer.setActivationDate(new Date());
        this.baseDao.update(customer);
        this.auditManager.addHistoricalData(customer.getCustomerID().longValue(), 1L, Enums.UserStatus.IN_ACTIVE.name(),
                Enums.UserStatus.ACTIVE.name());
    }

    public String getAllCustomersIDsUnderBusinessEntity(long parentBusinessEntityID) {
        StringBuilder customerIDsStr = new StringBuilder();
        String businessEntityIDsStr = this.businessEntityManager
                .getAllChildrenIDsUnderBusinessEntity(parentBusinessEntityID);
        Query query = (Query) this.baseDao.executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("getAllCustomersIDsUnderBusinessEntity", this.getClass(), businessEntityIDsStr));

        List results = query.getResultList();

        if (results.size() > 0) {
            for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
                Object result = localIterator.next();
                customerIDsStr.append(result + ",");
            }
        }
        if (customerIDsStr.length() > 0) {
            customerIDsStr = customerIDsStr.delete(customerIDsStr.length() - 1, customerIDsStr.length());
        } else {
            customerIDsStr.append("-1");
        }
        return customerIDsStr.toString();
    }

    public String getAllCustomersUserKeysUnderBusinessEntity(long parentBusinessEntityID) {
        StringBuilder customerIDsStr = new StringBuilder();
        String businessEntityIDsStr = this.businessEntityManager.getAllChildrenIDsUnderBusinessEntity(parentBusinessEntityID);
        Query query = (Query) this.baseDao.executeNativeQuery(ServiceQueryEngine.getQueryStringToExecute("getAllCustomersUserKeysUnderBusinessEntity", this.getClass(), businessEntityIDsStr));

        List results = query.getResultList();
        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            customerIDsStr.append("'" + result + "',");
        }
        if (customerIDsStr.length() > 0) {
            customerIDsStr = customerIDsStr.delete(customerIDsStr.length() - 1, customerIDsStr.length());
        } else {
            customerIDsStr.append("'-1'");
        }
        return customerIDsStr.toString();
    }

    public List<Customer> getAllCustomersUnderBusinessEntity(long parentBusinessEntityID) {
        String businessEntityIDsStr = this.businessEntityManager
                .getAllChildrenIDsUnderBusinessEntity(parentBusinessEntityID);
        return this.baseDao.executeDynamicQuery(
                "select model from Customer model where model.registeringAgent.businessEntityID in ("
                        + businessEntityIDsStr + ")",
                Customer.class, false, false);
    }

    public boolean isIdentificationKeyAlreadyExists(String walletShortCode, String msisdn) {
        String identificationKey = RegistrationUtils.generateIdentficationKey(walletShortCode, msisdn);
        List customerList = this.baseDao.executeDynamicQuery(
                "select model from Customer model where model.identificationKey='" + identificationKey + "' ",
                Customer.class, false, false);
        return (customerList != null) && (!customerList.isEmpty());
    }

    public void updateCustomerAuthenticationType(BusinessMessage businessMessage) throws GeneralFailureException {
        PartyDetails senderInfo = businessMessage.getPrimarySenderInfo();
        boolean isUssdRequest = isUssdRequest(businessMessage);

        if (senderInfo == null) {
            handleMissingAttributesException("SenderInfo");
        }
        String accountId = getAccountId(senderInfo.getPaymentMethod());
        String walletShortCode = senderInfo.getWalletShortCode();
        String msisdn = senderInfo.getMsisdn();
        String iMei = senderInfo.getImei();
        Integer authenticationTypeValue = senderInfo.getAuthenticationType();

        if (Strings.isNullOrEmpty(walletShortCode)) {
            handleMissingAttributesException("SenderInfo: WalletShortCode");
        }
        if (Strings.isNullOrEmpty(msisdn)) {
            handleMissingAttributesException("SenderInfo: Msisdn");
        }
        if (authenticationTypeValue == null) {
            handleMissingAttributesException("SenderInfo: AuthenticationType");
        }
        if (!Enums.CustomerAuthenticationTypeEnum
                .checkAuthenticationTypeExistence(authenticationTypeValue.intValue())) {
            HashMap vars = new HashMap();
            vars.put("attribute", "SenderInfo: AuthenticationType");
            throw new GeneralFailureException("VAL13014", vars);
        }
        Object serviceLog = businessMessage.getSoftFields().get("SERVICE_LOG");
        Long serviceLogId = null;
        if (serviceLog != null) {
            serviceLogId = ((ServiceLog) serviceLog).getId();
        }
        Customer customer = getCustomer(msisdn, walletShortCode);
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }
        if (!isUssdRequest) {
            if ((customer.getCustomerRegistrationType().intValue() != Enums.CustomerRegisterationTypeEnum.USSD
                    .ordinal()) && (Strings.isNullOrEmpty(iMei))) {
                handleMissingAttributesException("SenderInfo: iMei");
            }
            if (customer.getCustomerRegistrationType().intValue() != Enums.CustomerRegisterationTypeEnum.USSD
                    .ordinal()) {
                CustomerDevices loginedDevice = new CustomerDevices();
                CustomerDevices customerDevice = null;
                if (!iMei.equals(customer.getIMEI())
                        && !"3005".equalsIgnoreCase(businessMessage.getServiceInfo().getCode())
                        && !"1101".equalsIgnoreCase(businessMessage.getServiceInfo().getCode())) {
                    loginedDevice.setCustomer(customer.getCustomerID());
                    loginedDevice.setIMEI(iMei);
                    try {
                        customerDevice = customerDevicesManager.getDeviceImeiByClientInfo(loginedDevice,
                                businessMessage);
                    } catch (Exception e) {
                        logger.error(
                                "Error While tring to get Customer Device in Class CustomerManagerImpl in mehtod updateCustomerAuthenticationType with Exception "
                                        + e);
                    }
                    if (customerDevice != null) {
                        throw new GeneralFailureException("VAL05030");
                    } else if ((businessMessage.getClientInfo() != null
                            && businessMessage.getClientInfo().getVersion() != null)) {
                        throw new CustomerException("VAL05029");
                    } else {
                        if (customer.getActivationCode() == null) {
                            String activationCode = RegistrationUtils.generateActivationCode();
                            String encActivationCode = AESEncryption.encrypt(activationCode);
                            customer.setActivationCode(encActivationCode);
                            businessMessage.getPrimarySenderInfo().getPersonalDetails()
                                    .setFirstName(customer.getFirstName());
                            HashMap parameters = new HashMap();
                            parameters.put("customer", customer);
                            parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());
                            parameters.put("activation_code", activationCode);

                            this.notificationManager.sendNotification("ReactivationSendSmsActivationCode", parameters);

                            this.baseDao.update(customer);
                        } else {
                            businessMessage.getPrimarySenderInfo().getPersonalDetails()
                                    .setFirstName(customer.getFirstName());
                            String activationCode = AESEncryption.decrypt(customer.getActivationCode());
                            HashMap parameters = new HashMap();
                            parameters.put("customer", customer);
                            parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());
                            parameters.put("activation_code", activationCode);

                            this.notificationManager.sendNotification("ReactivationSendSmsActivationCode", parameters);

                        }
                        this.baseDao.update(customer);
                    }
                }
            }

        }
        Enums.CustomerAuthenticationTypeEnum newAuthenticationType;
        if (authenticationTypeValue.intValue() == -1) {
            if (customer
                    .getCustomerAuthenticationType() != Enums.CustomerAuthenticationTypeEnum.NO_AUTHENTICATION_NO_LIMIT
                    .getAuthenticationType()) {
                newAuthenticationType = Enums.CustomerAuthenticationTypeEnum.values()[customer
                        .getCustomerAuthenticationType().intValue()];
                businessMessage.getPrimarySenderInfo().setAuthenticationType(customer.getCustomerAuthenticationType());
            } else {
                newAuthenticationType = Enums.CustomerAuthenticationTypeEnum.PIN;
                businessMessage.getPrimarySenderInfo()
                        .setAuthenticationType(Enums.CustomerAuthenticationTypeEnum.PIN.getAuthenticationType());
            }
        } else {
            newAuthenticationType = Enums.CustomerAuthenticationTypeEnum.values()[authenticationTypeValue.intValue()];
        }

        Enums.CustomerAuthenticationTypeEnum currentCustomerAuthenticationType = Enums.CustomerAuthenticationTypeEnum.PIN;

        RegistrationType registrationType = baseDao.executeNamedQueryForSingleResult(
                "CustomerType.getRegistrationType.byId", RegistrationType.class, false, true,
                customer.getCustomerType());
        if (registrationType != null) {
            if (customer.getCustomerType() != null) {
                currentCustomerAuthenticationType = Enums.CustomerAuthenticationTypeEnum.values()[customer
                        .getCustomerAuthenticationType().intValue()];
            }

            Enums.CustomerProfileType customerProfileType = Enums.CustomerProfileType.TOKEN;
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.PIN) {
                customerProfileType = Enums.CustomerProfileType.PIN;
            }
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.OTP) {
                customerProfileType = Enums.CustomerProfileType.OTP;
            }
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.BIOMETRIC) {
                customerProfileType = Enums.CustomerProfileType.BIOMETRIC;
            }
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.TOKEN
                    && customer.isTokenIndmnity() == true) {
                customerProfileType = Enums.CustomerProfileType.Token_INDMNITY;
            }
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.FACE_RECOGNITION) {
                customerProfileType = Enums.CustomerProfileType.FACE_RECOGNITION;
            }
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.NO_AUTHENTICATION_NO_LIMIT) {
                customerProfileType = Enums.CustomerProfileType.NO_AUTHENTICATION_NO_LIMIT;
            }

            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.PIN_OTP_FINANCIAL) {
                customerProfileType = Enums.CustomerProfileType.PIN_OTP_FINANCIAL;
            }

            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.NO_AUTHENTICATION_FOR_BREFERENTIAL_BENFICIARY) {
                customerProfileType = Enums.CustomerProfileType.NO_AUTHENTICATION_FOR_BREFERENTIAL_BENFICIARY;
            }
            if (newAuthenticationType == Enums.CustomerAuthenticationTypeEnum.TOKEN) {
                if (customer.isTokenIndmnity() == true) {
                    customerProfileType = Enums.CustomerProfileType.Token_INDMNITY;
                    customer.setCustomerAuthenticationType(
                            Integer.valueOf(newAuthenticationType.getAuthenticationType()));
                    customer = assignDefaultSegmintationToCustomer(customer, accountId, walletShortCode,
                            customerProfileType, null, businessMessage);
                    this.baseDao.update(customer);
                } else {
                    customerProfileType = Enums.CustomerProfileType.TOKEN;
                    customer.setCustomerAuthenticationType(
                            Integer.valueOf(newAuthenticationType.getAuthenticationType()));
                    customer = assignDefaultSegmintationToCustomer(customer, accountId, walletShortCode,
                            customerProfileType, null, businessMessage);
                    this.baseDao.update(customer);
                }

            } else {
                customer.setCustomerAuthenticationType(Integer.valueOf(newAuthenticationType.getAuthenticationType()));
                customer = assignDefaultSegmintationToCustomer(customer, accountId, walletShortCode,
                        customerProfileType, null, businessMessage);

            }
        }

    }

    private String getAccountId(PaymentMethodDetail paymentMethod) {
        String accountId = null;
        if (paymentMethod.getBank() != null && paymentMethod.getBank().getAccountNumber() != null) {
            accountId = paymentMethod.getBank().getAccountNumber();
        } else if (paymentMethod.getCard() != null && paymentMethod.getCard().getCardNumber() != null) {
            accountId = paymentMethod.getCard().getCardNumber();
        }
        return accountId;
    }

    public Customer assignDefaultSegmintationToCustomer(Customer customer,
                                                        Enums.CustomerProfileType customerProfileType) throws GeneralFailureException {
        String walletShortCode = baseDao.executeNamedQuerySingleResult("BusinessEntity.getWalletShortCodeByID",
                String.class, false, customer.getWallet());
        if (Strings.isNullOrEmpty(walletShortCode)) {
            handleMissingAttributesException("SenderInfo: WalletShortCode");
        }

        RegistrationType registrationType = new RegistrationType();
        registrationType.setId(new Long(customerProfileType.getProfileType()));


        String getDefaultCustomerTypeNativeQuery = ServiceQueryEngine.getQueryStringToExecute("assignDefaultSegmintationToCustomer_getDefaultCustomerTypeNativeQuery",
                this.getClass(), walletShortCode, String.valueOf(customerProfileType.getProfileType()));

        String getCustomerTypeNativeQuery = ServiceQueryEngine.getQueryStringToExecute("assignDefaultSegmintationToCustomer_getCustomerTypeNativeQuery",
                this.getClass(), walletShortCode, String.valueOf(customerProfileType.getProfileType()), customer.getSchmCode());

        CustomerType defaultCustomerType = null;
        List results = null;
        Query query = (Query) this.baseDao.executeNativeQuery(getDefaultCustomerTypeNativeQuery);

        if (customer.getSchmCode() != null && !customer.getSchmCode().isEmpty()) {
            query = (Query) this.baseDao.executeNativeQuery(getCustomerTypeNativeQuery);
            results = query.getResultList();
            if (results == null || results.size() <= 0) {
                query = (Query) this.baseDao.executeNativeQuery(getDefaultCustomerTypeNativeQuery);
                results = query.getResultList();
            }
        } else {
            results = query.getResultList();
        }

        if (results == null || results.size() <= 0) {
            throw new CustomerException("BUS13011");
        }

        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            Object[] r = (Object[]) result;
            defaultCustomerType = new CustomerType();
            defaultCustomerType.setId(((BigDecimal) r[0]).longValue());
            defaultCustomerType.setDefaultFeeId(((BigDecimal) r[1]).longValue());
            defaultCustomerType.setDefaultRiskId(((BigDecimal) r[2]).longValue());
        }
        if (isCustomerTypeChanged(customer, defaultCustomerType)) {
            customer.setCustomerType(defaultCustomerType.getId());
            if (defaultCustomerType.getDefaultFeeId() == null) {
                throw new CustomerException("BUS13011");
            }
            if (defaultCustomerType.getDefaultRiskId() == null) {
                throw new CustomerException("BUS13011");
            }
            if (customer.getCustomerID() == null || !isCustomerAssigenedToPromo(customer)) {
                customer.setFeeProfile(defaultCustomerType.getDefaultFeeId());
            }

            if (customer.getCustomerID() == null || !isCustomerAssigenedToBulkRisk(customer)) {
                customer.setRiskProfile(defaultCustomerType.getDefaultRiskId());
            }

            List userProfileList = this.baseDao.executeNamedQuery("UserProfile.getDefaultProfileByRegistrationType",
                    UserProfile.class, false, true, new Object[]{customer.getWallet(), registrationType});
            if ((userProfileList == null) || (userProfileList.size() <= 0)) {
                throw new CustomerException("BUS13011");
            }
            UserProfile defaultUserProfile = (UserProfile) userProfileList.get(0);
            customer.setUserProfile(defaultUserProfile.getId());

            if (customer.getCustomerID() != null) {
                baseDao.update(customer);
            }
        }
        if (defaultCustomerType.getDefaultRiskId() == null) {
            throw new CustomerException("BUS13011");
        }
        if (customer.getCustomerID() == null || !isCustomerAssigenedToPromo(customer)) {
            customer.setFeeProfile(defaultCustomerType.getDefaultFeeId());
        }

        if (customer.getCustomerID() == null || !isCustomerAssigenedToBulkRisk(customer)) {
            customer.setRiskProfile(defaultCustomerType.getDefaultRiskId());
        }

        List userProfileList = this.baseDao.executeNamedQuery("UserProfile.getDefaultProfileByRegistrationType",
                UserProfile.class, false, false, new Object[]{customer.getWallet(), registrationType});
        if ((userProfileList == null) || (userProfileList.size() <= 0)) {
            throw new CustomerException("BUS13011");
        }
        UserProfile defaultUserProfile = (UserProfile) userProfileList.get(0);
        customer.setUserProfile(defaultUserProfile.getId());

        return customer;
    }

    public Customer assignDefaultSegmintationToCustomer(Customer customer, String accountId, String shortCode,
                                                        CustomerProfileType customerProfileType, String schemeCode, BusinessMessage businessMessage)
            throws GeneralFailureException {
        CustomerType customerType = null;

        String profilesQuery = null;
        try {
            if (accountId != null) {
                profilesQuery = ServiceQueryEngine.getQueryStringToExecute("assignDefaultSegmintationToCustomer_AccountId",
                        this.getClass(), accountId, String.valueOf(customerProfileType.getProfileType()),
                        shortCode, customer.getIdentificationKey());
            } else if (schemeCode != null) {
                profilesQuery = ServiceQueryEngine.getQueryStringToExecute("assignDefaultSegmintationToCustomer_schemeCode",
                        this.getClass(), String.valueOf(customer.getCustomerProfileTypeID()),
                        schemeCode, String.valueOf(customerProfileType.getProfileType()), shortCode);

            } else if (businessMessage.getPrimarySenderInfo().getPersonalDetails()
                    .getCutomerTypeSubsubscriberCategory() != null) {
                long customerTypeID = businessMessage.getPrimarySenderInfo().getPersonalDetails()
                        .getCutomerTypeSubsubscriberCategory();

                profilesQuery = ServiceQueryEngine.getQueryStringToExecute("assignDefaultSegmintationToCustomer_getCutomerTypeSubsubscriberCategory",
                        this.getClass(), String.valueOf(customerTypeID));
            } else {
                assignDefaultSegmintationToCustomer(customer, customerProfileType);
                return customer;
            }

            Query query = (Query) this.baseDao.executeNativeQuery(profilesQuery.toString());

            List results = query.getResultList();
            Object[] result = null;
            if (results != null && results.size() > 0) {
                result = (Object[]) results.get(0);
            }
            customerType = new CustomerType();
            customerType.setId(((BigDecimal) result[0]).longValue());
            customerType.setDefaultFeeId(((BigDecimal) result[1]).longValue());
            customerType.setDefaultRiskId(((BigDecimal) result[2]).longValue());
            customer.setCustomerType(customerType.getId());

            if (customerType.getDefaultFeeId() == null || customerType.getDefaultRiskId() == null) {
                throw new CustomerException("BUS13011");
            }

            if (customer.getCustomerID() == null) {
                customer.setFeeProfile(customerType.getDefaultFeeId());
            }

            if (isCustomerAssigenedToPromo(customer)) {
                try {
                    String customerPromoProfileId = getActiveCustomerPromoProfileId(customer.getCustomerID());
                    BigDecimal promoFee = getProfileTotalFee(Long.valueOf(customerPromoProfileId), businessMessage);
                    BigDecimal actualFee = getProfileTotalFee(customerType.getDefaultFeeId(), businessMessage);
                    if (promoFee != null && actualFee != null) {
                        if (actualFee.compareTo(promoFee) < 0) { // if transaction fee is less than promo fee neglect
                            // promo fee
                            customer.setFeeProfile(customerType.getDefaultFeeId());
                        } else { // if promo fee is less update customer with promo profile and update customer
                            // promo profile old fee with transaction fee
                            customer.setFeeProfile(Long.valueOf(customerPromoProfileId));
                            updateCustomerPromoProfileDefaultFee(customer.getCustomerID(),
                                    customerType.getDefaultFeeId());
                        }
                    } else {
                        customer.setFeeProfile(customerType.getDefaultFeeId());
                    }
                } catch (Exception e) {
                    customer.setFeeProfile(customerType.getDefaultFeeId());
                    e.printStackTrace();
                }
            } else {
                customer.setFeeProfile(customerType.getDefaultFeeId());
            }
            if (isCustomerAssigenedToPromo(customer)) {
                try {
                    String customerPromoProfileId = getActiveCustomerPromoProfileId(customer.getCustomerID());
                    BigDecimal promoFee = getProfileTotalFee(Long.valueOf(customerPromoProfileId), businessMessage);
                    BigDecimal actualFee = getProfileTotalFee(customerType.getDefaultFeeId(), businessMessage);
                    if (promoFee != null && actualFee != null) {
                        if (actualFee.compareTo(promoFee) < 0) {
                            customer.setFeeProfile(customerType.getDefaultFeeId());
                        } else {
                            customer.setFeeProfile(Long.valueOf(customerPromoProfileId));
                            updateCustomerPromoProfileDefaultFee(customer.getCustomerID(),
                                    customerType.getDefaultFeeId());
                        }
                    } else {
                        customer.setFeeProfile(customerType.getDefaultFeeId());
                    }
                } catch (Exception e) {
                    customer.setFeeProfile(customerType.getDefaultFeeId());
                    e.printStackTrace();
                }
            } else {
                customer.setFeeProfile(customerType.getDefaultFeeId());
            }

            if (customer.getCustomerID() == null || !isCustomerAssigenedToBulkRisk(customer)) {
                customer.setRiskProfile(customerType.getDefaultRiskId());
            }
            RegistrationType registrationType = new RegistrationType();
            registrationType.setId(new Long(customerProfileType.getProfileType()));

            List userProfileList = this.baseDao.executeNamedQuery("UserProfile.getDefaultProfileByRegistrationType",
                    UserProfile.class, false, true, new Object[]{customer.getWallet(), registrationType});
            if ((userProfileList == null) || (userProfileList.size() <= 0)) {
                throw new CustomerException("BUS13011");
            }
            UserProfile defaultUserProfile = (UserProfile) userProfileList.get(0);
            customer.setUserProfile(defaultUserProfile.getId());

            if (customer.getCustomerID() != null) {
                baseDao.update(customer);
            }
        } catch (Exception e) {
            logger.error("issue in profile setup");
            e.printStackTrace();
            assignDefaultSegmintationToCustomer(customer, customerProfileType);
        }

        return customer;
    }

    private boolean isCustomerTypeChanged(Customer customer, CustomerType customerType) {
        if (customer.getCustomerType() != null && customer.getCustomerType().equals(customerType.getId())) {
            return false;
        }
        return true;
    }

    private boolean isCustomerAssigenedToPromo(Customer customer) {
        if (customer.getCustomerID() == null)
            return false;
        Query query = this.baseDao.getEntityManager().createNamedQuery(
                "CustomerPromoProfile.findPromoProfileIdByCustomerIdAndStatus", CustomerPromoProfile.class);
        query.setParameter(1, customer.getCustomerID());
        query.setParameter(2, "active");
        List<CustomerPromoProfile> profiles = query.getResultList();
        if (profiles != null && profiles.size() > 0) {
            CustomerPromoProfile profile = profiles.get(0);
            return true;
        } else {
            return false;
        }

    }

    private boolean isCustomerAssigenedToBulkRisk(Customer customer) {
        if (customer.getCustomerID() == null)
            return false;
        Query query = this.baseDao.getEntityManager().createNamedQuery(
                "CustomerBulkRiskProfile.findBulkRiskProfileByCustomerIdAndStatus", CustomerBulkRiskProfile.class);
        query.setParameter(1, customer.getCustomerID());
        query.setParameter(2, "active");
        List<CustomerBulkRiskProfile> profiles = query.getResultList();
        if (profiles != null && profiles.size() > 0) {
            CustomerBulkRiskProfile profile = profiles.get(0);
            if (customer.getRiskProfile().equals(profile.getBulkRiskProfileId())) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }

    }

    public boolean isCustomerRegistered(String msisdn, String walletShortCode) {
        List customerList = this.baseDao.executeDynamicQuery("select model from Customer model where model.msisdn='"
                + msisdn + "'  and model.walletShortCode='" + walletShortCode + "'", Customer.class, false, false);

        return (customerList != null) && (!customerList.isEmpty());
    }

    private boolean isUssdRequest(BusinessMessage message) {
        if (message.getSoftFields().containsKey("isUssdRequest")) {
            return ((Boolean) message.getSoftFields().get("isUssdRequest")).booleanValue();
        }
        return false;
    }

    public BusinessMessage activateRemoteCustomer(BusinessMessage businessMessage) throws Exception {
        PartyDetails senderInfo = businessMessage.getPrimarySenderInfo();

        if (senderInfo == null) {
            handleMissingAttributesException("SenderInfo");
        }

        String msisdn = senderInfo.getMsisdn();
        if (Strings.isNullOrEmpty(msisdn)) {
            handleMissingAttributesException("SenderInfo : MSISDN");
        }
        String walletShortCode = senderInfo.getWalletShortCode();
        if (Strings.isNullOrEmpty(walletShortCode)) {
            handleMissingAttributesException("SenderInfo : WalletShortCode");
        }
        Customer customer = null;
        String registeredCustomerOtp = "";
        if (businessMessage.getSoftFields().containsKey("isBulk")
                && businessMessage.getSoftFields().get("isBulk").equals(1)) {
            BulkCustomerEntity bulkCustomer = checkCustomerInBulk(msisdn, walletShortCode);
            if (bulkCustomer == null) {
                throw new CustomerException("BUS01009");
            }
            registeredCustomerOtp = bulkCustomer.getActivationCode();
            if (bulkCustomer.getExpiryDate() != null) {
                if (isOtpExpired(bulkCustomer.getExpiryDate())) {
                    throw new CustomerException(CustomerException.INVALID_OTP);
                }
            }
        } else {
            customer = getCustomer(msisdn, walletShortCode);
            if (customer == null) {
                throw new CustomerException("BUS01009");
            }
            registeredCustomerOtp = customer.getActivationCode();
        }

        String otp = senderInfo.getSmsActivationCode();
        String encOTP = AESEncryption.encrypt(otp);

        if (Strings.isNullOrEmpty(otp)) {
            handleMissingAttributesException("SenderInfo : ActivationCode");
        }

        if (Strings.isNullOrEmpty(registeredCustomerOtp)) {
            throw new CustomerException("BUS01014");
        }
        if (!encOTP.equals(registeredCustomerOtp)) {
            throw new CustomerException("BUS13015");
        }

        boolean isUssdRequest = isUssdRequest(businessMessage);

        String iMei = senderInfo.getImei();
        if ((!isUssdRequest) && (Strings.isNullOrEmpty(iMei))) {
            handleMissingAttributesException("SenderInfo : IMEI");
        }

        String pin = senderInfo.getPin();
        if (Strings.isNullOrEmpty(pin)) {
            handleMissingAttributesException("SenderInfo : Pin");
        }

        String password = senderInfo.getPassword();
        if ((!isUssdRequest) && (Strings.isNullOrEmpty(password))) {
            handleMissingAttributesException("SenderInfo : Password");
        }
        if (businessMessage.getSoftFields().containsKey("isBulk")
                && businessMessage.getSoftFields().get("isBulk").equals(1)) {
        } else {
            customer.setStatus(Enums.UserStatus.ACTIVE);
            customer.setActivationDate(new Date());
            customer.setActivationCode(null);
            customer.setIMEI(iMei);
            customer.setReasonOfClosing(null);
            customer.setBlockingDate(null);
            customer.setPIN(hashString(pin));

            if ((!isUssdRequest) && (senderInfo.isPlainPassword())) {
                customer.setNewLogin(true);
            }

            if (!isUssdRequest) {
                if (senderInfo.isPlainPassword()) {
                    customer.setHandsetPassword(hashString(password));
                } else {
                    customer.setHandsetPassword(password);
                }
            }
            this.baseDao.update(customer);
            releaseCustomer(customer.getMsisdn());
            businessMessage.getPrimarySenderInfo().setAuthenticationType(customer.getCustomerAuthenticationType());
            if (!isUssdRequest) {
                resetMessageSequenceNumber(customer.getIMEI());
            }
        }

        return businessMessage;
    }

    private boolean isOtpExpired(Date expiryDate) {
        return expiryDate.getTime() < (new Date().getTime());
    }

    private void releaseCustomer(String msidn) {
        String sqlUpdateDML = ServiceQueryEngine.getQueryStringToExecute("releaseCustomer", this.getClass(), msidn);
        Query query = (Query) baseDao.executeNativeQuery(sqlUpdateDML);
        query.executeUpdate();
    }

    public BusinessMessage reActivateRemoteCustomerByPIN(BusinessMessage businessMessage) throws Exception {
        PartyDetails senderInfo = businessMessage.getPrimarySenderInfo();

        String userId = senderInfo.getUserKey();

        Object serviceLog = businessMessage.getSoftFields().get("SERVICE_LOG");
        Long serviceLogId = null;
        if (serviceLog != null) {
            serviceLogId = ((ServiceLog) serviceLog).getId();
        }

        if (senderInfo == null) {
            handleMissingAttributesException("SenderInfo");
        }

        String msisdn = senderInfo.getMsisdn();
        if (Strings.isNullOrEmpty(msisdn)) {
            handleMissingAttributesException("SenderInfo : MSISDN");
        }
        String walletShortCode = senderInfo.getWalletShortCode();
        if (Strings.isNullOrEmpty(walletShortCode)) {
            handleMissingAttributesException("SenderInfo : WalletShortCode");
        }
        Customer customer = getCustomer(msisdn, walletShortCode);
        String identificationKey = walletShortCode + msisdn;
        if ((businessMessage.getClientInfo() != null && businessMessage.getClientInfo().getVersion() != null)
                && (customer.getHandsetPassword() == null) && (customer.getCustomerRegistrationType()
                .intValue() == Enums.CustomerRegisterationTypeEnum.USSD.ordinal())) {
            throw new CustomerException("VAL302022");
        }

        if (customer.getCustomerRegistrationType() == Enums.CustomerRegisterationTypeEnum.IN_BRANCH.ordinal()
                && customer.getActivationCode() != null && customer.getHandsetPassword() == null) {
            throw new CustomerException(CustomerException.INBRANCH_LOGIN_WITHOUT_ACTIVATION);
        }

        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        String iMei = senderInfo.getImei();
        if (Strings.isNullOrEmpty(iMei)) {
            handleMissingAttributesException("SenderInfo : IMEI");
        }
        if (customerDevicesManager.isDeviceBlackListed(businessMessage.getPrimarySenderInfo().getImei())) {
            throw new CustomerException(CustomerException.CUSTOMER_DEVICE_IS_BLACKLISTED);
        }
        if (isSameClientInfo(customer, businessMessage)) {
            try {
                businessMessage = updateMessageWithCustomerSmeData(businessMessage, customer);
            } catch (Exception e) {
                System.out.println("Failed to check if customer is SME and will be considered retail");
                businessMessage.getPrimarySenderInfo().setIsRetail(true);
                e.printStackTrace();
            }
            if (businessMessage.getPrimarySenderInfo().getIsSme() == true
                    && businessMessage.getPrimarySenderInfo().getIsRetail() == false)
                checkCustomerBussinessEntityIsActive(businessMessage.getPrimarySenderInfo().getBEHierarchyId());

            String password = senderInfo.getPassword();
            Integer authenticationTypeValue = senderInfo.getAuthenticationType();
            if (authenticationTypeValue != Enums.CustomerAuthenticationTypeEnum.FACE_RECOGNITION.getAuthenticationType()
                    && Strings.isNullOrEmpty(password)) {
                handleMissingAttributesException("SenderInfo : Password");
            }
            String swk = senderInfo.getSwk();
            if (Strings.isNullOrEmpty(swk)) {
                handleMissingAttributesException("SenderInfo : SWK");
            }

            Map<String, Object> queryParameters = new HashMap<String, Object>();
            queryParameters.put("status", Enums.UserStatus.ACTIVE);
            if (businessMessage.getPrimarySenderInfo().getBECustomerId() == null
                    || (businessMessage.getPrimarySenderInfo().getIsSmeActive() != null
                    && businessMessage.getPrimarySenderInfo().getIsSmeActive() == true))
                queryParameters.put("activationCode", null);
            queryParameters.put("IMEI", iMei);

            if (authenticationTypeValue == null) {
                handleMissingAttributesException("SenderInfo: AuthenticationType");
            }
            if (!Enums.CustomerAuthenticationTypeEnum
                    .checkAuthenticationTypeExistence(authenticationTypeValue.intValue())) {
                HashMap vars = new HashMap();
                vars.put("attribute", "SenderInfo: AuthenticationType");
                throw new GeneralFailureException("VAL13014", vars);
            }
            if (!businessMessage.getServiceInfo().getCode().equals("4260")
                    && authenticationTypeValue != Enums.CustomerAuthenticationTypeEnum.FACE_RECOGNITION
                    .getAuthenticationType()
                    && !Strings.isNullOrEmpty(password)) {
                queryParameters.put("handsetPassword", password);
            }
            String encNewSwk = encryptCustomerSWK(swk);
            String encOldSwk = customer.getSymmetricWorkingKey();
            queryParameters.put("symmetricWorkingKey", encNewSwk);

            if (senderInfo.isPlainPassword()) {
                queryParameters.put("newLogin", true);
            }
            queryParameters.put("reasonOfClosing", null);
            queryParameters.put("loginTrials", Integer.valueOf(0));
            queryParameters.put("loginTrialsPassword", Integer.valueOf(0));
            queryParameters.put("blockingDate", null);
            Map<String, Object> idMap = Collections.singletonMap("customerID", (Object) customer.getCustomerID());

            baseDao.updateModelByIdHQL(Customer.class, queryParameters, idMap);
            if (businessMessage.getParameter() != null && businessMessage.getParameter() == 1) {
                Long sessionId = createOrUpdateCustomerSession(customer.getIdentificationKey(), encNewSwk);
                businessMessage.setSessionId(sessionId);
            }
        } else {
            customer.setStatus(Enums.UserStatus.IN_ACTIVE);
            customer.setLastModifiedDate(new Date());
            customer.setLoginTrials(Integer.valueOf(0));
            customer.setLoginTrialsPassword(Integer.valueOf(0));
            String activationCode = null;
            if (businessMessage.getClientInfo() != null && businessMessage.getClientInfo().getVersion() != null) {
                if (customer.getReasonOfClosing() == null) {
                    customer.setReasonOfClosing("Invalid IMEI");
                    this.baseDao.update(customer);
                    this.blockingManager.addToBlackList(customer.getCustomerID(), null, Enums.UserType.CUSTOMER, userId,
                            serviceLogId, null, "Invalid IMEI");
                    throw new CustomerException("VAL01054");
                } else if (customer.getReasonOfClosing().equals("Invalid IMEI")) {
                    throw new CustomerException("VAL302022");
                }
            } else {
                if (customer.getActivationCode() == null) {
                    activationCode = RegistrationUtils.generateActivationCode();
                    String encActivationCode = AESEncryption.encrypt(activationCode);
                    customer.setActivationCode(encActivationCode);
                    businessMessage.getPrimarySenderInfo().getPersonalDetails().setFirstName(customer.getFirstName());
                    HashMap parameters = new HashMap();
                    parameters.put("customer", customer);
                    parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());
                    parameters.put("activation_code", activationCode);

                    this.notificationManager.sendNotification("ReactivationSendSmsActivationCode", parameters);
                    this.baseDao.update(customer);
                } else {
                    businessMessage.getPrimarySenderInfo().getPersonalDetails().setFirstName(customer.getFirstName());
                    String decActivationCode = AESEncryption.decrypt(customer.getActivationCode());
                    HashMap parameters = new HashMap();
                    parameters.put("customer", customer);
                    parameters.put("WALLET_SHORT_CODE", customer.getWalletShortCode());
                    parameters.put("activation_code", decActivationCode);

                    this.notificationManager.sendNotification("ReactivationSendSmsActivationCode", parameters);

                }
                throw new CustomerException("VAL01051");
            }
        }
        AllowedListConfig config = new AllowedListConfig();
        String hoursToForceImport = allowedListBundle.getString("hours.to.force.import");
        config.setHoursToForceImport(Long.parseLong(hoursToForceImport));
        businessMessage.setAllowedListConfig(config);

        businessMessage.getPrimarySenderInfo().setAuthenticationType(customer.getCustomerAuthenticationType());
        resetMessageSequenceNumber(customer.getIMEI());
        String language = businessMessage.getPrimarySenderInfo().getLanguage() != null
                ? businessMessage.getPrimarySenderInfo().getLanguage()
                : "en";
        ExceptionResolver resolver = ExceptionUtil.handle(language, "showSecureHint");
        LoginSuccessAction loginSuccessAction = new LoginSuccessAction();
        loginSuccessAction.setShowSecureHint(
                System.getenv("showSecureHint") != null ? System.getenv("showSecureHint").equals("true") : false);
        loginSuccessAction.setSecureHintMsg(resolver.getDescription());
        loginSuccessAction.setSecureHintImageUrl(System.getenv("imageUrl") + "/"
                + businessMessage.getPrimarySenderInfo().getLanguage() + "-" + "secureHint.jpg");
        businessMessage.setLoginSuccessAction(loginSuccessAction);
        CustomerDevices customerDevice = new CustomerDevices();
        customerDevice.setCustomer(customer.getCustomerID());
        customerDevice.setIMEI(businessMessage.getPrimarySenderInfo().getImei());
        customerDevice.setSwk(businessMessage.getPrimarySenderInfo().getSwk());
        customerDevice.setCreationDate(new Date());
        customerDevice.setStatus(1L);
        customerDevice.setName("NA");
        if (businessMessage.getClientInfo() != null) {
            customerDevice.setAppVersion(businessMessage.getClientInfo().getVersionName());
            if (businessMessage.getClientInfo().getPlatform() != null
                    && !businessMessage.getClientInfo().getPlatform().isEmpty()) {
                customerDevice.setPlatform(businessMessage.getClientInfo().getPlatform());
                if (businessMessage.getClientInfo().getPlatform().indexOf(";") >= 0) {
                    customerDevice.setOs(new String(businessMessage.getClientInfo().getPlatform().substring(0,
                            businessMessage.getClientInfo().getPlatform().indexOf(" "))));
                    customerDevice.setOsVersion(new String(businessMessage.getClientInfo().getPlatform().substring(
                            businessMessage.getClientInfo().getPlatform().indexOf(" "),
                            businessMessage.getClientInfo().getPlatform().indexOf(";"))));
                }
            }
            if (businessMessage.getClientInfo().getDeviceInfo() != null) {
                customerDevice.setModel(businessMessage.getClientInfo().getDeviceInfo().getModel());
                customerDevice.setName(businessMessage.getClientInfo().getDeviceInfo().getManufacturer());
                customerDevice.setDeviceManufacturer(businessMessage.getClientInfo().getDeviceInfo().getManufacturer());
                customerDevice.setType(businessMessage.getClientInfo().getDeviceInfo().getNetworkType());
            }

        }
        CustomerDevices deviceInDb = customerDevicesManager.getDeviceImeiByClientInfo(customerDevice, businessMessage);
        deviceInDb.setLastAccess(new Date());
        baseDao.update(deviceInDb);

        if (customer.getCustomerProfileTypeID() != null && customer.getCustomerProfileTypeID() == 3) {
            businessMessage.getPrimarySenderInfo().setLockedProfile(true);
        } else {
            businessMessage.getPrimarySenderInfo().setLockedProfile(false);
        }
        if (businessMessage.getPrimarySenderInfo() != null
                && businessMessage.getPrimarySenderInfo().getPersonalDetails() != null) {
            businessMessage.getPrimarySenderInfo().getPersonalDetails().setEmail(customer.getEmail());
        }
        identificationKey = businessMessage.getPrimarySenderInfo().getWalletShortCode()
                + businessMessage.getPrimarySenderInfo().getMsisdn();
        if (identificationKey != null) {
            businessMessage.getIndemnityProfile()
                    .setIsIndemnityProfileEnabled(getCustomerIndemnityProfileStatus(identificationKey).intValue());
        }

        if (businessMessage.getPrimarySenderInfo().getFamilyMember() == true
                && businessMessage.getPrimarySenderInfo().getIsRetail() == true) {
            closeFamilyAccount(businessMessage);
        }

        return businessMessage;

    }

    private void handleMissingAttributesException(String attribute) throws GeneralFailureException {
        HashMap vars = new HashMap();
        vars.put("attribute", attribute);
        throw new GeneralFailureException("VAL01056", vars);
    }

    public int getLastMessageSequenceNumber(String mobileImei) throws GeneralFailureException {
        CustomerMessageSequence customerMessageSequence = (CustomerMessageSequence) this.baseDao
                .findSingleResultByProperty(CustomerMessageSequence.class, "customerMobileIMEI", mobileImei);
        if (customerMessageSequence == null) {
            return 0;
        }

        Customer customers = (Customer) this.baseDao.findSingleResultByProperty(Customer.class, "IMEI",
                customerMessageSequence.getCustomerMobileIMEI());
        if (customers == null) {
            resetMessageSequenceNumber(customerMessageSequence.getCustomerMobileIMEI());
            return 0;
        }

        return customerMessageSequence.getLastMessageSequence();
    }

    public int getLastMessageSequenceNumberByIMEI(String mobileImei) throws GeneralFailureException {
        BigDecimal lastMessageSeqNum = (BigDecimal) this.baseDao.executeNamedQueryForSingleResult(
                "Customer.getCustomerLastMessageSequence", BigDecimal.class, false, false, new Object[]{mobileImei});
        if (lastMessageSeqNum == null) {
            return 0;
        }
        return lastMessageSeqNum.intValue();
    }

    public void updateLastMessageSequenceNumber(String mobileImei, int newMessageSequence)
            throws GeneralFailureException {
        CustomerMessageSequence customerMessageSequence = (CustomerMessageSequence) this.baseDao
                .findSingleResultByProperty(CustomerMessageSequence.class, "customerMobileIMEI", mobileImei);
        if (customerMessageSequence == null) {
            CustomerMessageSequence messageSequence = new CustomerMessageSequence();
            messageSequence.setCustomerMobileIMEI(mobileImei);
            messageSequence.setLastMessageDatetime(new Date());
            messageSequence.setLastMessageSequence(1);
            this.baseDao.save(messageSequence);
        } else {
            customerMessageSequence.setLastMessageSequence(newMessageSequence);
            customerMessageSequence.setLastMessageDatetime(new Date());
            this.baseDao.update(customerMessageSequence);
        }
    }

    public void resetMessageSequenceNumber(String mobileImei) throws GeneralFailureException {
        CustomerMessageSequence customerMessageSequence = (CustomerMessageSequence) this.baseDao
                .findSingleResultByProperty(CustomerMessageSequence.class, "customerMobileIMEI", mobileImei);
        if (customerMessageSequence == null) {
            return;
        }

        this.baseDao.remove(customerMessageSequence);
    }

    @Override
    public String encryptCustomerSWK(String swk) throws SecurityException {
        return this.encryptionDecryptionManager.encryptUsingTdesWithAppKey(swk);
    }

    private String decryptCustomerSWK(String swk) throws SecurityException {
        return this.encryptionDecryptionManager.decryptUsingTdesWithAppKey(swk);
    }

    public Customer findCustomerByIMEI(String imei) throws CustomerException {
        Customer customer = null;
        Query query = this.baseDao.getEntityManager()
                .createQuery("select model from Customer model where model.IMEI= :imei");
        query.setParameter("imei", imei);
        List customerList = new ArrayList(query.getResultList());
        if ((customerList != null) && (!customerList.isEmpty())) {
            customer = (Customer) customerList.get(0);
        } else {
            throw new CustomerException("VAL01054");
        }
        return customer;
    }

    public String hashPassword(String password) {
        return hashString(password);
    }

    private String generateOTP(int n) {
        char[] alphNum = "1234567890".toCharArray();
        Random rnd = new Random();

        StringBuilder sb = new StringBuilder(n);
        for (int i = 0; i < n; i++) {
            sb.append(alphNum[rnd.nextInt(alphNum.length)]);
        }
        String id = sb.toString();
        return id;
    }

    @Deprecated
    public void sendOTPService(String msisdn, String firstName) throws GeneralFailureException, IOException {

        TransactionOTPSession transactionOTPSession = baseDao.getEntityManager().find(TransactionOTPSession.class,
                msisdn);

        String otp;

        if (transactionOTPSession == null) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");

            otp = generateOTP(Integer.parseInt(otpLength));

            String encOtp = AESEncryption.encrypt(otp);

            transactionOTPSession = new TransactionOTPSession(msisdn, encOtp, new Date());
            transactionOTPSession.setUsed(false);
            baseDao.save(transactionOTPSession);

        } else if (isOtpExpired(transactionOTPSession) || transactionOTPSession.isUsed()) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");

            otp = generateOTP(Integer.parseInt(otpLength));

            String encOtp = AESEncryption.encrypt(otp);

            transactionOTPSession.setOtp(encOtp);
            transactionOTPSession.setUsed(false);
            transactionOTPSession.setCreationTime(new Date());
            baseDao.update(transactionOTPSession);

        } else {
            otp = AESEncryption.decrypt(transactionOTPSession.getOtp());
        }

        String message = "Dear " + firstName + ", Your One Time Password is " + otp + ". It expires in "
                + this.propertyLoader.loadProperty("OTP_TIMEOUT")
                + " minutes. Do not disclose this code to anyone. For help: 01-2808822, <EMAIL>";
        String formatedMsisdn = msisdn;
        if (formatedMsisdn.startsWith("2340")) {
            formatedMsisdn = formatedMsisdn.replaceFirst("2340", "234");
        }
        messageManager.sendSMS(message, new String[]{formatedMsisdn});

    }

    public boolean isValidOTPSubscriber(String msisdn, String otp, String serviceCode)
            throws GeneralFailureException, IOException {

        TransactionOTPSession transactionOTPSession = getTranXOTPSession(msisdn, serviceCode);
        String encOtp = AESEncryption.encrypt(otp);

        if ((transactionOTPSession != null) && StringUtils.equals(transactionOTPSession.getOtp(), encOtp)
                && transactionOTPSession.isUsed()) {
            throw new CustomerException(CustomerException.INVALID_OTP_IS_USED_BEFORE);
        }


        if ((transactionOTPSession != null) && StringUtils.equals(transactionOTPSession.getOtp(), encOtp)
                && !isOtpExpired(transactionOTPSession)) {
            transactionOTPSession.setUsed(true);
            baseDao.update(transactionOTPSession);
            return true;
        } else {
            return false;
        }

    }

    public boolean isValidOTPSubscriber(String msisdn, String otp, String serviceCode, Long corporateId)
            throws GeneralFailureException, IOException {

        TransactionOTPSession transactionOTPSession = getTranXOTPSession(msisdn, serviceCode, corporateId);
        String encOtp = AESEncryption.encrypt(otp);

        if ((transactionOTPSession != null) && StringUtils.equals(transactionOTPSession.getOtp(), encOtp)
                && transactionOTPSession.isUsed()) {
            throw new CustomerException(SMEException.INVALID_OTP_OR_EXPIRED);
        }

        if ((transactionOTPSession != null) && StringUtils.equals(transactionOTPSession.getOtp(), encOtp)
                && !isOtpExpired(transactionOTPSession)) {
            transactionOTPSession.setUsed(true);
            baseDao.update(transactionOTPSession);
            return true;
        } else {
            return false;
        }

    }

    private boolean isOtpExpired(TransactionOTPSession transactionOTPSession)
            throws GeneralFailureException, IOException {
        return (transactionOTPSession.getCreationTime().getTime() + getOTPTimeOut()) < (new Date().getTime());
    }

    private Long getOTPTimeOut() throws GeneralFailureException, IOException {
        String otpTimeOut = this.propertyLoader.loadProperty("OTP_TIMEOUT");
        return getMilliSecondsOfNumOfMins(Integer.parseInt(otpTimeOut));
    }

    private Long getMilliSecondsOfNumOfMins(int mins) {
        return new Long(mins * 60 * 1000);
    }

    public boolean isCustomerInNegativeDb(BusinessMessage message) throws GeneralFailureException {
        if (message.getPrimarySenderInfo().getUserKey() == null) {
            throw new CustomerException("VAL01001");
        }
        boolean isCustomerOrPaymentMethodBlocked = false;
        String reasonOfClosing = null;
        String nativeQuery = ServiceQueryEngine.getQueryStringToExecute("isCustomerInNegativeDb", this.getClass(), message.getPrimarySenderInfo().getUserKey());
        Query query = (Query) this.baseDao.executeNativeQuery(nativeQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            if (message.getClientInfo() != null && message.getClientInfo().getVersion() != null) {
                for (Object result : results) {
                    Object[] r = (Object[]) result;
                    reasonOfClosing = (String) r[1];
                    if (reasonOfClosing != null && (reasonOfClosing.equals(INVALID_IMEI))) {
                        throw new CustomerException("VAL302022");
                    } else {
                        if (reasonOfClosing != null && !reasonOfClosing.equals(EXCEEDED_NUMBER_OF_PIN_RETRIALS_REASON)
                                && !reasonOfClosing.equals(BLOCKED_BY_PASSWORD))
                            throw new GeneralFailureException("VAL01016");
                        else
                            throw new CustomerException("VAL302022");
                    }
                }
            }
        }
        return isCustomerOrPaymentMethodBlocked;
    }

    public boolean isMsisdnInRegistrationBlackList(String msisdn) throws Throwable {
        RegistrationBlackList customer = null;
        String mobileNumber = (msisdn != null) && (msisdn.startsWith("2340")) ? msisdn.replaceFirst("2340", "234")
                : msisdn;

        customer = (RegistrationBlackList) this.baseDao.getEntityManager().find(RegistrationBlackList.class,
                mobileNumber);

        return customer != null;
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRED)
    public void completeRegistration() {
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("completeRegistration", this.getClass());
        List<Customer> result = new ArrayList();
        this.baseDao.getEntityManager().setFlushMode(FlushModeType.COMMIT);
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr, Customer.class);
        result = query.getResultList();
        System.out.println("inComplete registrations count " + (result != null ? result.size() + "" : "0"));
        int i = 0;
        for (Customer c : result) {
            if (i > 100)
                ;
            System.out.println("Customer to complete registration  " + c.getMsisdn() + " : " + c.getAccountNumber()
                    + " counter i=" + i);
            try {
                BusinessMessage customerResponseMessage = generatResponse(c.getAccountNumber());
                Customer complete = TransferToCustomer(customerResponseMessage, c.getAccountNumber());
                c.setGar(complete.getGar());
                BusinessEntity wallet = (BusinessEntity) baseDao.executeNamedQueryForSingleResult(
                        "BusinessUser.getBusinessEntity.getWallet.getBusinessEntityID", BusinessEntity.class, false,
                        false, c.getCreatedBy());
                c.setWallet(wallet.getBusinessEntityID());
                this.baseDao.getEntityManager().merge(c);
                try {
                    this.customerRemoteManager.registerSVAAccountAndBankAccountForCustomer(null, c);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                sendApplicationURL(c);
                i++;
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println("Customer registration  completed " + c.getMsisdn() + " : " + c.getAccountNumber());
        }
        System.out.println("Completing registrations finished ");
    }

    public BusinessMessage generatResponse(String accountNumbere) {
        try {
            String url = "http://192.168.123.36:9191/mpaymentapp-receiver/uba/portal/inquiry/services";
            URL obj = new URL(url);
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", "application/json");

            String body = "{message:{\"serviceInfo\":{\n         \"id\":888913,\n         \"stepId\":\"1000004\"\n      },\n      \"senderInfo\":{\n         \"paymentMethod\":{\n            \n            \"paymentMethodType\":0,\n            \"bank\":{\n               \"accountNumber\":\""
                    + accountNumbere
                    + "\"\n            }\n         }\n        \n      }\n   },\n   \"serviceId\":888913,\n   \"walletShortCode\":\"1234\"\n}";

            con.setDoOutput(true);
            DataOutputStream wr = new DataOutputStream(con.getOutputStream());
            wr.writeBytes(body);
            wr.flush();
            wr.close();

            int responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));

            StringBuffer response = new StringBuffer();
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            Gson gson = new Gson();

            BusinessMessage customerResponseMessage = (BusinessMessage) gson.fromJson(response.toString(),
                    BusinessMessage.class);

            String holderName = customerResponseMessage.getPrimarySenderInfo().getPaymentMethod().getBank()
                    .getHolderName();
            customerResponseMessage.getPrimarySenderInfo().getPaymentMethod().getBank()
                    .setHolderName(holderName.replaceAll(",", ""));

            return customerResponseMessage;
        } catch (Exception localException) {
        }
        return null;
    }

    private Customer TransferToCustomer(BusinessMessage businessMessage, String accNo) {
        Customer customer = new Customer();
        customer.setAccountNumber(accNo);
        customer.setMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());

        if (businessMessage.getPrimarySenderInfo().getPersonalDetails() != null) {
            if (businessMessage.getPrimarySenderInfo().getPaymentMethod().getBank() != null) {
                String gar = businessMessage.getPrimarySenderInfo().getPaymentMethod().getBank().getHolderName() + ","
                        + businessMessage.getPrimarySenderInfo().getPaymentMethod().getBank().getAccountType() + ","
                        + businessMessage.getPrimarySenderInfo().getPaymentMethod().getBank().getSolId() + ","
                        + businessMessage.getPrimarySenderInfo().getPaymentMethod().getPaymentMethodCurrency();

                System.out.println("TransferToCustomer Customer Gar :: " + gar);

                String garEncoded = Encoder.encodeBase64(gar.getBytes());
                customer.setGar(garEncoded);
            }

        }

        return customer;
    }

    public void saveBvnEnquiry(NibssBVNEnquiry bvnEnquiry) {

        String sql = ServiceQueryEngine.getQueryStringToExecute("saveBvnEnquiry", this.getClass(), bvnEnquiry.getId(), bvnEnquiry.getAccountName(), bvnEnquiry.getAccountNumber(), bvnEnquiry.getBankVerificationNumber(), bvnEnquiry.getDestinationInstitutionCode(), bvnEnquiry.getSessionId(), bvnEnquiry.getSenderMsisdn(), String.valueOf(bvnEnquiry.getServiceLogId()));
        Query queryInsert = (Query) baseDao.executeNativeQuery(sql);
        queryInsert.executeUpdate();

    }


    public BusinessMessage getReciverVeriCode(BusinessMessage message) {
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("getReciverVeriCode", this.getClass(),
                message.getPrimaryReceiverInfo().getPaymentMethod().getBank().getAccountNumber(),
                message.getPrimaryReceiverInfo().getPaymentMethod().getBank().getShortCode(),
                message.getPrimarySenderInfo().getMsisdn());
        Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr, NibssBVNEnquiry.class);
        NibssBVNEnquiry bvn = new NibssBVNEnquiry();
        try {
            List list = query.getResultList();

            if ((list != null) && (!list.isEmpty())) {
                bvn = (NibssBVNEnquiry) list.get(0);
                message.getPrimaryReceiverInfo().getPaymentMethod().getBank().setHolderName(bvn.getAccountName());
                message.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                        .setBankVerificationCode(bvn.getBankVerificationNumber());
                message.getPrimaryReceiverInfo().getPaymentMethod().getBank().setNameInquiryRef(bvn.getSessionId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return message;
    }

    public List<String> getGars(Long customerId) {
        return null;
    }

    public BusinessMessage checkRiskInfo(BusinessMessage message, Long customerId)
            throws GarException, CustomerException {


        Long accountID = null;
        String paymentMethodType = null;
        Long accountNumber = null;

        if (message.getSourcePaymentMethod() != null && message.getSourcePaymentMethod().getPaymentMethodCode() != null) {


            accountID = this.baseDao.executeNamedQueryForSingleResult("CustomerGar.getCustomeraccountNumberByGarId",
                    Long.class, false, true, new Object[]{message.getSourcePaymentMethod().getPaymentMethodCode()});


        } else {
            accountID = this.baseDao.executeNamedQueryForSingleResult("CustomerGar.getCustomerSVAaccountByUserKey",
                    Long.class, false, true, new Object[]{message.getPrimarySenderInfo().getUserKey()});

            if (message.getPrimarySenderInfo() != null && message.getPrimarySenderInfo().getPaymentMethod() != null) {

                accountNumber = getPaymentMethodAccountId(message.getPrimarySenderInfo().getPaymentMethod());
            }

            paymentMethodType = SchemeCodeEnum.REGULAR.toString();

            if (accountNumber != null) {
                String schmCode = garManager.getCustomerGarWithAccNum(customerId, new Long(accountNumber))
                        .getSchemeCode();
                paymentMethodType = garManager.getPaymentMethodTypeBySchemeCode(schmCode);
            }
        }
        String userID = accountID + "-1";
        SimpleDateFormat dt1 = new SimpleDateFormat("yyyy-MM-dd");
        String generalDay = userID + "-" + dt1.format(new Date());
        String Query1 = null;

        if (paymentMethodType != null && !paymentMethodType.isEmpty()) {
            Query1 = ServiceQueryEngine.getQueryStringToExecute("checkRiskInfo", this.getClass(), generalDay, paymentMethodType);
        } else if (paymentMethodType == null || paymentMethodType.isEmpty()) {
            Query1 = ServiceQueryEngine.getQueryStringToExecute("checkRiskInfo_empty", this.getClass(),
                    generalDay);
        }

        Query query = (Query) this.baseDao.executeNativeQuery(Query1);
        UserRiskProfile userRiskProfile = new UserRiskProfile();
        BigDecimal D_Sum = BigDecimal.ZERO;

        List results = query.getResultList();
        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            Object[] r = (Object[]) result;
            if ((r[0] == null) || (!D_Sum.equals(BigDecimal.ZERO))) {
                continue;
            }
            D_Sum = (BigDecimal) r[0];
            break;
        }

        userRiskProfile.setCurrentRiskLimitPerDay(D_Sum);

        message.getPrimarySenderInfo().setUserRiskProfile(userRiskProfile);
        return message;
    }

    public UsersRiskProfile checkRiskProfiles(BusinessMessage message) throws GeneralFailureException {
        String queryString = ServiceQueryEngine.getQueryStringToExecute("checkRiskProfiles", this.getClass());
        Query query = (Query) this.baseDao.executeNativeQuery(queryString);
        UsersRiskProfile usersRiskProfile = new UsersRiskProfile();

        List results = query.getResultList();
        ArrayList profiles = new ArrayList();
        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            Object[] r = (Object[]) result;
            com.cit.mpaymentapp.common.customer.message.RiskProfile riskProfile = new com.cit.mpaymentapp.common.customer.message.RiskProfile();
            riskProfile.setMaxRiskLimit((BigDecimal) r[2]);
            if (r[3] != null) {
                continue;
            }
            riskProfile.setKey(r[0].toString());
            riskProfile.setRiskProfileName(r[1].toString());
            profiles.add(riskProfile);
        }

        usersRiskProfile.setRiskProfiles(profiles);
        return usersRiskProfile;
    }

    public boolean verifyPassword(BusinessMessage message) throws GeneralFailureException, IOException {
        String serviceCode = message.getServiceInfo().getCode();
        Object serviceLog = message.getSoftFields().get("SERVICE_LOG");
        Long serviceLogId = null;
        if (serviceLog != null) {
            serviceLogId = ((ServiceLog) serviceLog).getId();
        }
        String password = message.getPrimarySenderInfo().getPassword();
        String userId = message.getPrimarySenderInfo().getUserKey();
        if (Strings.isNullOrEmpty(password)) {
            handleMissingAttributesException("PASSWORD");
        }

        String passMaxTrials = this.propertyLoader.loadProperty("PASSWORD_MAX_TRIALS");
        int MAX_TRIALS = Integer.parseInt(passMaxTrials);

        boolean verified = false;

        Customer customer = getCustomer(userId);
        if (customer == null) {
            throw new CustomerException("BUS01009");
        }

        if ((customer.getHandsetPassword() == null) && (customer.getCustomerRegistrationType()
                .intValue() == Enums.CustomerRegisterationTypeEnum.USSD.ordinal())) {
            throw new CustomerException("VAL302022");
        }
        if (password != null) {
            verified = password.equals(customer.getHandsetPassword());
        }

        if (!verified) {
            Integer numberOfTrials = customer.getLoginTrialsPassword();
            customer.setLoginTrialsPassword(Integer.valueOf(numberOfTrials.intValue() + 1));
            if (numberOfTrials.intValue() + 1 == MAX_TRIALS) {
                this.blockingManager.blockUser(customer.getCustomerID(), Enums.UserType.CUSTOMER, userId, serviceLogId,
                        null, "blocked by password");
                throw new CustomerException("VAL302025");
            }
            if (numberOfTrials.intValue() + 1 > MAX_TRIALS) {
                throw new CustomerException("VAL302025");
            }
            updateCustomer(customer);
        } else {
            String updateQuery = "update Customer model set model.loginTrialsPassword=0 where model.identificationKey='"
                    + userId + "'";
            this.baseDao.executeDynamicQuery(updateQuery, String.class, true, false);
            if ((serviceCode.equals("4260")) && (customer.getStatus() == Enums.UserStatus.BLOCKED)
                    && (checkBlockTimeDiff(customer))) {
                unblock(customer, "SYSTEM", null);
                customer.setBlockingDate(null);
                this.baseDao.save(customer);
            }
        }

        return verified;
    }

    private boolean checkBlockTimeDiff(Customer customer)
            throws NumberFormatException, IOException, GeneralFailureException {
        Date currentDate = new Date();
        Date blockingDate = customer.getBlockingDate();
        if (blockingDate != null) {
            long diff = currentDate.getTime() - blockingDate.getTime();
            long diffMinutes = diff / 60000L % 60L;
            if (diffMinutes >= new Long(this.propertyLoader.loadProperty("PASSWORD_MAX_TRIALS")).longValue()) {
                return true;
            }
        }
        return false;
    }

    public <T> List<T> getCustomerBlockingData(String msisdn) {
        String query = ServiceQueryEngine.getQueryStringToExecute("getCustomerBlockingData", this.getClass(), msisdn);
        Query q = (Query) this.baseDao.executeNativeQuery(query);
        List result = q.getResultList();
        return result;
    }

    public void updateCustomerCoolOffCyclesLimit(Long customerID, int customerCoolOffCyclesLimit) {
        String query = ServiceQueryEngine.getQueryStringToExecute("updateCustomerCoolOffCyclesLimit", this.getClass(),
                String.valueOf(customerCoolOffCyclesLimit), String.valueOf(customerID));
        System.out.print(query);
        Query queryRes = (Query) this.baseDao.executeNativeQuery(query, true);
        List results = queryRes.getResultList();
        System.out.print("");
    }

    @Deprecated
    public void UnblockCustomerForCoolOffService(String msisdn) {
        String query = ServiceQueryEngine.getQueryStringToExecute("UnblockCustomerForCoolOffService", this.getClass(), msisdn);
        this.baseDao.executeNativeQuery(query);
    }

    public void setUssdPin(String userId, String PIN, String serviceId) throws CustomerException {
        if (userId == null) {
            throw new CustomerException("VAL01055");
        }
        if (PIN == null) {
            throw new CustomerException("VAL01015");
        }
        if ((PIN.length() != 4) || (!PIN.matches("[0-9]+"))) {
            throw new CustomerException("VAL01015");
        }
        Customer customer = getCustomer(userId);
        boolean unblocked = false;
        if ("42234".equals(serviceId)) {
            customer.setLoginTrials(Integer.valueOf(0));
            if (customer.getStatus().equals(Enums.UserStatus.BLOCKED)) {
                try {
                    unblock(customer, "SYSTEM", "Exceed Number of PIN retrials");
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new CustomerException(CustomerException.GENERAL_ERROR);
                }
            }

        }

        customer.setPIN(hashString(PIN));
        customer.setStatus(Enums.UserStatus.ACTIVE);
        customer.setReasonOfClosing("");
        customer.setLastModifiedDate(new Date());
        customer.setLoginTrials(Integer.valueOf(0));
        this.baseDao.update(customer);
    }

    private boolean isSameModileInfo(String msisdn, ClientInfo newClientInfo)
            throws GeneralFailureException, IOException {
        String enablePlatformCheck = propertyLoader.loadProperty("Enable_Mobile_Platform");
        if (!enablePlatformCheck.equals("true")) {
            return false;
        }
        boolean flag = false;
        ClientInfo olfClientInfo = getSavedClientInfo(msisdn);
        String oldReqPlatform = null;
        String newReqPlatform = null;
        if (newClientInfo == null) {
            return false;
        } else if (olfClientInfo != null) {
            oldReqPlatform = olfClientInfo.getPlatform();
            newReqPlatform = newClientInfo.getPlatform();
            if (oldReqPlatform.equals(newReqPlatform)) {
                flag = true;
            }
        }
        return flag;
    }

    private ClientInfo getSavedClientInfo(String msidn) throws GeneralFailureException {
        String[] encryptedMessages = null;
        String querySt = null;
        EndToEndTimeRequest endTendReq = null;
        CustomerRequestMessage customerMessage = null;
        ClientInfo clientInfo = null;
        Calendar calendar = new GregorianCalendar(2017, 4, 24);
        Date date = calendar.getTime();
        String msisdn10Digit = getMsisdnFormat(msidn);
        querySt = "select e1 from EndToEndTimeRequest e1 "
                + "where e1.startDate = (select min(e2.startDate) from EndToEndTimeRequest e2 "
                + "where e2.msisdn like '" + msidn + "'" + "and e2.startDate>:date)";

        Query query = baseDao.getEntityManager().createQuery(querySt);
        query.setParameter("date", date);
        try {
            endTendReq = (EndToEndTimeRequest) query.getSingleResult();
        } catch (NoResultException e) {
            endTendReq = null;
        }
        if (endTendReq != null) {
            String reqSt = null;
            reqSt = endTendReq.getRequestPart1();
            if (reqSt != null) {
                if (endTendReq.getRequestPart2() != null) {
                    reqSt.concat(endTendReq.getRequestPart2());
                }
            } else {
                return null;
            }
            Gson gson = new GsonBuilder().setDateFormat(DateFormat.FULL).setDateFormat("dd-MM-yyyy").create();
            Request request = gson.fromJson(reqSt, Request.class);
            encryptedMessages = request.getEncryptedMessages();

            String decryptedMessage = null;
            try {
                decryptedMessage = decryptWithRSA(encryptedMessages);
            } catch (Exception exception) {
                throw new GeneralFailureException(GeneralFailureException.INVALID_MESSAGE_CONTENT);
            }
            gson = new GsonBuilder().setDateFormat(DateFormat.FULL).setDateFormat("dd-MM-yyyy").create();

            customerMessage = gson.fromJson(decryptedMessage, CustomerRequestMessage.class);
            if (customerMessage != null) {
                clientInfo = customerMessage.getClientInfo();
            }
        }
        return clientInfo;
    }

    private String decryptWithRSA(String[] encryptedMessages) throws SecurityException {

        String decryptedMessage = "";
        for (String messageChunk : encryptedMessages) {
            try {
                decryptedMessage = decryptedMessage.concat(getRsaSecurity().decrypt(messageChunk, getPrivateKey()));
            } catch (Exception ex) {
                throw new SecurityException("Failed to decrypt the messge [" + messageChunk + "]" + ex.getMessage());
            }
        }
        return decryptedMessage;
    }

    public PrivateKey getPrivateKey() {
        if (privateKey == null) {
            privateKey = encryptionDecryptionManager.loadPrivateKey();
        }
        return privateKey;
    }

    public RSASecurity getRsaSecurity() {
        if (rsaSecurity == null) {
            rsaSecurity = encryptionDecryptionManager.loadRSAKey();
        }
        return rsaSecurity;
    }

    public void updateProfileImage(String userKey, String imageName) throws CustomerException {
        Customer customer = new Customer();
        customer = getCustomer(userKey);
        CustomerProfile customerProfile = new CustomerProfile();
        if (customer.getCustomerProfile() != null && customer.getCustomerProfile().getProfileImage() != null) {
            customer.getCustomerProfile().setProfileImage(imageName);
        } else {
            customerProfile.setProfileImage(imageName);
            baseDao.save(customerProfile);
            customer.setCustomerProfile(customerProfile);
        }
        updateCustomer(customer);
    }

    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public boolean isSameClientInfo(Customer customer, BusinessMessage businessMessage) throws GeneralFailureException {

        if (businessMessage.getClientInfo() != null && businessMessage.getClientInfo().getDeviceInfo() != null
                && businessMessage.getClientInfo().getDeviceInfo().isImeiGenerated() == null
                && !businessMessage.getServiceInfo().getId().equals("4250")) {
            businessMessage.getSoftFields().put("isFromAddNewDevice", true);
            setFromAddNewDevice(true);
        }
        if (businessMessage.getClientInfo() != null && businessMessage.getClientInfo().getDeviceInfo() != null
                && businessMessage.getClientInfo().getDeviceInfo().isImeiGenerated() == null
                && businessMessage.getServiceInfo().getId().equals("4250")) {
            if (isFromAddNewDevice()) {
                businessMessage.getSoftFields().put("isFromAddNewDevice", true);
                setFromAddNewDevice(false);
            }
        }
        CustomerDevices device = new CustomerDevices();
        CustomerDevices customerDevice = null;
        MultiDevicesConfig multiDevicesConfig = customerDevicesManager.getMultiDevicesConfig();
        if (customer != null && businessMessage != null) {
            device.setCustomer(customer.getCustomerID());
            if (businessMessage.getPrimarySenderInfo() != null) {
                device.setIMEI(businessMessage.getPrimarySenderInfo().getImei());
                device.setSwk(businessMessage.getPrimarySenderInfo().getSwk());
            }
            if (businessMessage.getClientInfo() != null && businessMessage.getClientInfo().getPlatform() != null
                    && !businessMessage.getClientInfo().getPlatform().isEmpty()) {
                device.setPlatform(businessMessage.getClientInfo().getPlatform());
            }
            try {
                customerDevice = customerDevicesManager.getDeviceImeiByClientInfo(device, businessMessage);
            } catch (Exception e) {
                logger.error("Error in Class CustomerManagerImpl in mehtod isSameClientInfo with exception " + e);
                return false;
            }

            if (customerDevice != null && customerDevice.getIMEI() != null && customerDevice.getStatus() != null
                    && !customerDevice.getStatus().equals(3l)) {
                if (customerDevice.getStatus() != null && !customerDevice.getStatus().equals(0l)) {
                    return true;
                } else
                    throw new CustomerException(CustomerException.CUSTOMER_DEVICE_IS_DISABLED);
            } else {
                if (multiDevicesConfig.getEnableMultiDevices() == 1) {
                    if (customerDevicesManager.isDeviceUsedByAnotherUser(
                            businessMessage.getPrimarySenderInfo().getImei(),
                            businessMessage.getPrimarySenderInfo().getMsisdn())) {
                        if (multiDevicesConfig.getEnableDuplicateDevices() == 1) {
                            if (customerDevicesManager
                                    .isDeviceWhitListed(businessMessage.getPrimarySenderInfo().getImei())) {
                                throw new CustomerException(CustomerException.CUSTOMER_DEVICE_IS_NEW);
                            } else if (customerDevicesManager
                                    .isMsisdnWhiteListed(businessMessage.getPrimarySenderInfo().getMsisdn())) {
                                throw new CustomerException(CustomerException.CUSTOMER_DEVICE_IS_NEW);
                            } else {
                                throw new CustomerException(CustomerException.MSISDN_IS_NOT_IN_WHITELIST);
                            }

                        } else {
                            throw new CustomerException(CustomerException.DUPLICATE_DEVICES_IS_DISABLED);
                        }
                    } else {
                        throw new CustomerException(CustomerException.CUSTOMER_DEVICE_IS_NEW);
                    }

                } else {
                    throw new CustomerException(CustomerException.MULTI_DEVICES_IS_DISABLED);
                }

            }

        }
        return false;
    }

    private boolean isCustomerReachMaxNumberOfDevices(Long customerId) {
        try {
            if (customerDevicesManager.getCustomerDevicesCount(customerId) >= customerDevicesManager
                    .getMultiDevicesConfig().getMaxNumberOfDevices()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            logger.error(
                    "Error While tring to get Customer Devices Count  in Class CustomerManagerImpl in mehtod isCustomerReachMaxNumberOfDevices with Exception "
                            + e);
            return false;
        }

    }

    public void updateCustomerBankNumber(String msisdn, String bankNum) throws CustomerException {
        if (msisdn == null) {
            throw new CustomerException("VAL01055");
        }
        if (bankNum == null) {
            System.out.println("Bank CIF Not Found For Customer with MSISDN " + msisdn);
        } else {
            String queryStr = ServiceQueryEngine.getQueryStringToExecute("updateCustomerBankNumber", this.getClass(),
                    bankNum, msisdn);
            Query query = this.baseDao.getEntityManager().createNativeQuery(queryStr);
            query.executeUpdate();
        }
    }

    private static ResourceBundle allowedListBundle = ResourceBundle.getBundle("allowed-list-transfer-types");
    private static Set<String> transactionalServices = new HashSet<String>();

    static {
        String services = allowedListBundle.getString("transaction.service.to.send.specific.otp.message");
        String servicesList[] = services.split(",");
        for (int i = 0; i < servicesList.length; i++) {
            transactionalServices.add(servicesList[i]);
        }
    }


    public TransactionOTPSession getTranXOTPSession(String msisdn, String serviceCode) throws GeneralFailureException {
        TransactionOTPSession transactionOTPSession = null;
        Query query = null;
        if (serviceCode != null) {
            query = this.baseDao.getEntityManager().createQuery(
                    "select model from TransactionOTPSession model where model.msisdn=:msisdn and serviceCode=:serviceCode");
            query.setParameter("msisdn", msisdn);
            query.setParameter("serviceCode", serviceCode);
            query.setMaxResults(1);
        }
        if (serviceCode == null) {
            query = this.baseDao.getEntityManager().createQuery(
                    "select model from TransactionOTPSession model where model.msisdn=:msisdn and serviceCode IS NULL");
            query.setParameter("msisdn", msisdn);
            query.setMaxResults(1);
        }
        try {
            transactionOTPSession = (TransactionOTPSession) query.getSingleResult();
        } catch (Exception localException) {

        }

        return transactionOTPSession;
    }

    public TransactionOTPSession getTranXOTPSession(String msisdn, String serviceCode, Long corporateId) throws GeneralFailureException {
        TransactionOTPSession transactionOTPSession = null;
        Query query = null;
        if (serviceCode != null) {
            query = this.baseDao.getEntityManager().createQuery(
                    "select model from TransactionOTPSession model where model.msisdn=:msisdn and serviceCode=:serviceCode and model.corporateId =: corporateId");
            query.setParameter("msisdn", msisdn);
            query.setParameter("serviceCode", serviceCode);
            query.setParameter("corporateId", corporateId);
            query.setMaxResults(1);
        }
        try {
            transactionOTPSession = (TransactionOTPSession) query.getSingleResult();
        } catch (Exception localException) {

        }
        return transactionOTPSession;
    }

    public String maskString(String strText, int start, int end, char maskChar) throws Exception {

        if (strText == null || strText.equals(""))
            return "";

        if (start < 0)
            start = 0;

        if (end > strText.length())
            end = strText.length();

        if (start > end)
            throw new Exception("End index cannot be greater than start index");

        int maskLength = end - start;

        if (maskLength == 0)
            return strText;

        StringBuilder sbMaskString = new StringBuilder(maskLength);

        for (int i = 0; i < maskLength; i++) {
            sbMaskString.append(maskChar);
        }

        return strText.substring(0, start) + sbMaskString.toString() + strText.substring(start + maskLength);
    }

    public Optional<String> getServiceCode(String apiCode) {
        String sqlQuery = ServiceQueryEngine.getQueryStringToExecute("getServiceCode", this.getClass(), apiCode);
        Query query = (Query) baseDao.executeNativeQuery(sqlQuery);

        return query.getResultList().stream().findFirst();
    }

    public BusinessMessage sendOtpPerApiCode(BusinessMessage paramBusinessMessage, String msisdn, String firstName) throws Exception {

        String otpApiCode = paramBusinessMessage.getParameters().getAttributeAsString("otpApiCode");
        if (otpApiCode == null || otpApiCode.isEmpty())
            throw new GeneralFailureException(GeneralFailureException.INVALID_OTP_API_CODE);
        String OTPServiceCode = getServiceCode(otpApiCode).orElseThrow(() -> new GeneralFailureException(GeneralFailureException.INVALID_OTP_API_CODE));
        TransactionOTPSession transactionOTPSession = null;

        if (OTPServiceCode != null && !OTPServiceCode.isEmpty()) {
            transactionOTPSession = getTranXOTPSession(msisdn, OTPServiceCode);

        } else {
            transactionOTPSession = getTranXOTPSession(msisdn, null);
        }

        String otp;

        if (transactionOTPSession == null) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");

            otp = generateOTP(Integer.parseInt(otpLength));

            String encOtp = AESEncryption.encrypt(otp);
            if (OTPServiceCode != null && !OTPServiceCode.isEmpty()) {

                transactionOTPSession = new TransactionOTPSession(msisdn, encOtp, new Date(), OTPServiceCode);
            } else {
                transactionOTPSession = new TransactionOTPSession(msisdn, encOtp, new Date(), null);

            }
            transactionOTPSession.setUsed(false);
            baseDao.getEntityManager().merge(transactionOTPSession);

        } else {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");
            otp = generateOTP(Integer.parseInt(otpLength));
            String encOtp = AESEncryption.encrypt(otp);
            Long businessEntityId = paramBusinessMessage.getParameters().getAttributeAsLong("businessEntityId");
            transactionOTPSession.setOtp(encOtp);
            transactionOTPSession.setUsed(false);
            transactionOTPSession.setCreationTime(new Date());
            transactionOTPSession.setCorporateId(businessEntityId);
            baseDao.update(transactionOTPSession);
        }
//		else if (isOtpExpired(transactionOTPSession) || transactionOTPSession.isUsed()) { // commented for SAN-2156 - to prevent send the same otp again
//			String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");
//
//			otp = generateOTP(Integer.parseInt(otpLength));
//
//			String encOtp = AESEncryption.encrypt(otp);
//			Long businessEntityId = paramBusinessMessage.getParameters().getAttributeAsLong("businessEntityId");
//			transactionOTPSession.setOtp(encOtp);
//			transactionOTPSession.setUsed(false);
//			transactionOTPSession.setCreationTime(new Date());
//			transactionOTPSession.setCorporateId(businessEntityId);
//			baseDao.update(transactionOTPSession);
//
//		} else {
//			otp = AESEncryption.decrypt(transactionOTPSession.getOtp());
//		}
        HashMap parameters = new HashMap();
        String OTP_TIMEOUT = this.propertyLoader.loadProperty("OTP_TIMEOUT");
        String language = paramBusinessMessage.getPrimarySenderInfo().getLanguage() != null
                ? paramBusinessMessage.getPrimarySenderInfo().getLanguage()
                : DEFAULT_LANGUAGE;
        if (transactionalServices.contains(paramBusinessMessage.getServiceInfo().getOtpServiceId())) {
            parameters.put("Service_Code", "transaction_otp");
        } else {
            parameters.put("Service_Code", paramBusinessMessage.getServiceInfo().getCode());
        }
        parameters.put("language", language);
        parameters.put("Wallet_Info", paramBusinessMessage.getWalletInfo());
        parameters.put("Customer_Msisdn", paramBusinessMessage.getPrimarySenderInfo().getMsisdn());
        parameters.put("WALLET_SHORT_CODE", paramBusinessMessage.getPrimarySenderInfo().getWalletShortCode());
        parameters.put("firstName", firstName);
        parameters.put("otp", otp);
        parameters.put("OTP_TIMEOUT", OTP_TIMEOUT);
        if (paramBusinessMessage.getPrimaryReceiverInfo() != null
                && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod() != null) {
            if (paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                    .getHolderName() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank().getHolderName()
                    .trim() != "") {
                parameters.put("beneficiaryName",
                        paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank().getHolderName());
            } else if (paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard()
                    .getHolderName() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard().getHolderName()
                    .trim() != "") {
                parameters.put("beneficiaryName",
                        paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard().getHolderName());
            } else if (paramBusinessMessage.getAllowedList() != null
                    && paramBusinessMessage.getAllowedList().size() > 0) {
                if (paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName() != null) {
                    parameters.put("beneficiaryName",
                            paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName());
                }
            } else if (paramBusinessMessage.getPrimaryReceiverInfo().getMsisdn() != null) {
                parameters.put("beneficiaryName", paramBusinessMessage.getPrimaryReceiverInfo().getMsisdn());
            } else {
                parameters.put("Service_Code", paramBusinessMessage.getServiceInfo().getCode());
            }
        } else if (paramBusinessMessage.getAllowedList() != null && paramBusinessMessage.getAllowedList().size() > 0) {
            if (paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName() != null) {
                parameters.put("beneficiaryName", paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName());
            }
        }
        String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");
        String maskedMsisdn = maskString(msisdn, 0, msisdn.length() - 3, '*');
        paramBusinessMessage.getPrimarySenderInfo().setOtpLength(otpLength);
        paramBusinessMessage.getPrimarySenderInfo().setMaskedMsisdn(maskedMsisdn);
        paramBusinessMessage.getParameters().put("otp", otp);
        sendTransactionOTP(parameters);
        return paramBusinessMessage;


    }

    @Override
    public void validateUserLoginToTheCorrectApp(BusinessMessage businessMessage) throws IOException, GeneralFailureException {
        boolean checkUserType = Boolean.parseBoolean(propertyLoader.
                loadProperty(businessMessage.getHeader().getWalletShortCode() + "_check_user_type"));
        if (checkUserType) {
            String userType = businessMessage.getParameters().getAttributeAsString("userType");
            List<String> retailUserTypes = Arrays.asList(propertyLoader.loadProperty(
                    businessMessage.getHeader().getWalletShortCode() + "_retailUserTypes").split(","));
            List<String> agentUserTypes = Arrays.asList(propertyLoader.loadProperty(
                    businessMessage.getHeader().getWalletShortCode() + "_agentUserTypes").split(","));
            Customer customer = findCustomerByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());
            if (retailUserTypes.contains(userType))
                checkIfUserIsRetail(businessMessage, customer);
            else if (agentUserTypes.contains(userType)) {
                checkIfUserIsAgent(businessMessage, customer);
            }
        }
    }

    private void checkIfUserIsAgent(BusinessMessage businessMessage, Customer customer) throws CustomerException {
        Long beCustomerId = customer.getBECustomerId();
        if (beCustomerId == null)
            throw new CustomerException(CustomerException.USER_IS_RETAIL);
    }

    private void checkIfUserIsRetail(BusinessMessage businessMessage, Customer customer) throws CustomerException {
        Long beCustomerId = customer.getBECustomerId();
        if (beCustomerId != null)
            throw new CustomerException(CustomerException.USER_IS_AGENT);
    }

    public BusinessMessage sendOTPService(BusinessMessage paramBusinessMessage, String msisdn, String firstName)
            throws Exception {

        String OTPServiceCode = paramBusinessMessage.getServiceInfo().getOtpServiceId();
        String serviceCode = paramBusinessMessage.getServiceInfo().getCode();
        TransactionOTPSession transactionOTPSession = null;

        if (OTPServiceCode != null && !OTPServiceCode.isEmpty()) {
            transactionOTPSession = getTranXOTPSession(msisdn, OTPServiceCode);

        } else {
            transactionOTPSession = getTranXOTPSession(msisdn, null);
        }

        String otp;

        if (transactionOTPSession == null) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");

            otp = generateOTP(Integer.parseInt(otpLength));

            String encOtp = AESEncryption.encrypt(otp);
            if (OTPServiceCode != null && !OTPServiceCode.isEmpty()) {

                transactionOTPSession = new TransactionOTPSession(msisdn, encOtp, new Date(), OTPServiceCode);
            } else {
                transactionOTPSession = new TransactionOTPSession(msisdn, encOtp, new Date(), null);

            }
            transactionOTPSession.setUsed(false);
            baseDao.getEntityManager().merge(transactionOTPSession);

        } else if (isOtpExpired(transactionOTPSession) || transactionOTPSession.isUsed()) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");

            otp = generateOTP(Integer.parseInt(otpLength));

            String encOtp = AESEncryption.encrypt(otp);

            transactionOTPSession.setOtp(encOtp);
            transactionOTPSession.setUsed(false);
            transactionOTPSession.setCreationTime(new Date());
            baseDao.update(transactionOTPSession);

        } else {
            otp = AESEncryption.decrypt(transactionOTPSession.getOtp());
        }
        HashMap parameters = new HashMap();
        String OTP_TIMEOUT = this.propertyLoader.loadProperty("OTP_TIMEOUT");
        String language = paramBusinessMessage.getPrimarySenderInfo().getLanguage() != null
                ? paramBusinessMessage.getPrimarySenderInfo().getLanguage()
                : DEFAULT_LANGUAGE;
        if (transactionalServices.contains(paramBusinessMessage.getServiceInfo().getOtpServiceId())) {
            parameters.put("Service_Code", "transaction_otp");
        } else {
            parameters.put("Service_Code", paramBusinessMessage.getServiceInfo().getCode());
        }
        parameters.put("language", language);
        parameters.put("Wallet_Info", paramBusinessMessage.getWalletInfo());
        parameters.put("Customer_Msisdn", paramBusinessMessage.getPrimarySenderInfo().getMsisdn());
        parameters.put("WALLET_SHORT_CODE", paramBusinessMessage.getPrimarySenderInfo().getWalletShortCode());
        parameters.put("firstName", firstName);
        parameters.put("otp", otp);
        parameters.put("OTP_TIMEOUT", OTP_TIMEOUT);
        if (paramBusinessMessage.getPrimaryReceiverInfo() != null
                && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod() != null) {
            if (paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                    .getHolderName() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank().getHolderName()
                    .trim() != "") {
                parameters.put("beneficiaryName",
                        paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank().getHolderName());
            } else if (paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard()
                    .getHolderName() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard().getHolderName()
                    .trim() != "") {
                parameters.put("beneficiaryName",
                        paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard().getHolderName());
            } else if (paramBusinessMessage.getAllowedList() != null
                    && paramBusinessMessage.getAllowedList().size() > 0) {
                if (paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName() != null) {
                    parameters.put("beneficiaryName",
                            paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName());
                }
            } else if (paramBusinessMessage.getPrimaryReceiverInfo().getMsisdn() != null) {
                parameters.put("beneficiaryName", paramBusinessMessage.getPrimaryReceiverInfo().getMsisdn());
            } else {
                parameters.put("Service_Code", paramBusinessMessage.getServiceInfo().getCode());
            }
        } else if (paramBusinessMessage.getAllowedList() != null && paramBusinessMessage.getAllowedList().size() > 0) {
            if (paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName() != null) {
                parameters.put("beneficiaryName", paramBusinessMessage.getAllowedList().get(0).getBeneficiaryName());
            }
        }
        String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");
        String maskedMsisdn = maskString(msisdn, 0, msisdn.length() - 3, '*');
        paramBusinessMessage.getPrimarySenderInfo().setOtpLength(otpLength);
        paramBusinessMessage.getPrimarySenderInfo().setMaskedMsisdn(maskedMsisdn);
        paramBusinessMessage.getParameters().put("otp", otp);
        sendTransactionOTP(parameters);
        return paramBusinessMessage;

    }

    public void sendSMSService(BusinessMessage paramBusinessMessage, String msisdn, String firstName)
            throws GeneralFailureException, IOException {
        HashMap parameters = new HashMap();
        String language = paramBusinessMessage.getPrimarySenderInfo().getLanguage() != null
                ? paramBusinessMessage.getPrimarySenderInfo().getLanguage()
                : DEFAULT_LANGUAGE;
        if (paramBusinessMessage.getAddBeneficiaryType().trim().toLowerCase().equals("add"))
            parameters.put("Service_Code", "add_beneficiary_sms");
        else if (paramBusinessMessage.getAddBeneficiaryType().trim().toLowerCase().equals("import"))
            parameters.put("Service_Code", "import_beneficiary_sms");
        parameters.put("language", language);
        parameters.put("Wallet_Info", paramBusinessMessage.getWalletInfo());
        parameters.put("Customer_Msisdn", paramBusinessMessage.getPrimarySenderInfo().getMsisdn());
        parameters.put("WALLET_SHORT_CODE", paramBusinessMessage.getPrimarySenderInfo().getWalletShortCode());
        parameters.put("firstName", firstName);
        if (paramBusinessMessage.getPrimaryReceiverInfo() != null
                && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod() != null) {
            if (paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                    .getHolderName() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank().getHolderName()
                    .trim() != "") {
                parameters.put("beneficiaryName",
                        paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank().getHolderName());
            } else if (paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard()
                    .getHolderName() != null
                    && paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard().getHolderName()
                    .trim() != "") {
                parameters.put("beneficiaryName",
                        paramBusinessMessage.getPrimaryReceiverInfo().getPaymentMethod().getCard().getHolderName());
            } else if (paramBusinessMessage.getSoftFields().containsKey("beneficiaryName") == true) {
                parameters.put("beneficiaryName", paramBusinessMessage.getSoftFields().get("beneficiaryName"));
            } else if (paramBusinessMessage.getAllowedList() != null
                    && paramBusinessMessage.getAllowedList().size() > 0) {
                parameters.put("beneficiaryName", "new beneficiary");

            }
        } else if (paramBusinessMessage.getSoftFields().containsKey("beneficiaryName") == true) {
            parameters.put("beneficiaryName", paramBusinessMessage.getSoftFields().get("beneficiaryName"));
        } else if (paramBusinessMessage.getAllowedList() != null && paramBusinessMessage.getAllowedList().size() > 0) {
            parameters.put("beneficiaryName", "new beneficiary");
        }
        sendTransactionOTP(parameters);

    }

    @Override
    public Customer getCustomerByMsisdnAndWalletShortCodeNative(String msisdn, String walletShortCode)
            throws GeneralFailureException {
        Long customerID = null;
        Customer customer = new Customer();
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerByMsisdnAndWalletShortCodeNative", this.getClass(), msisdn, walletShortCode);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List result = query.getResultList();
        if ((result != null) && (result.size() > 0)) {
            BigDecimal customerIdBigDecimal = (BigDecimal) result.get(0);
            customerID = Long.valueOf(customerIdBigDecimal.longValue());
            customer.setCustomerID(customerID);
        }
        return customer;
    }

    @Override
    public Customer getCustomerBankNumberByIdentificationKeynNative(String IndetificationKey)
            throws GeneralFailureException {
        String customerBankNum = null;
        Long customerId = null;
        Customer customer = null;
        if (IndetificationKey == null) {
            throw new AccountException(AccountException.MISSING_MANDATORY_DATA);
        }
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerBankNumberByIdentificationKeynNative", this.getClass(), IndetificationKey);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            Object[] objResults = (Object[]) results.get(0);
            if (objResults[0] != null) {
                customerBankNum = (String) objResults[0];
            }
            customerId = Long.valueOf(((BigDecimal) objResults[1]).longValue());
            customer = new Customer();
            customer.setCustomerID(customerId);
            customer.setCustomerBankNum(customerBankNum);
        }
        return customer;
    }

    @Override
    public int enableDisableCustomerIndemnityProfile(Long enableStatus, String identificationKey,
                                                     Date indemnityCreationDate) throws GeneralFailureException {
        int result = -1;
        if (enableStatus != null && identificationKey != null) {
            Query query = null;
            try {
                query = this.baseDao.getEntityManager().createQuery(
                        "update Customer set ENABLE_INDEMNITY_PROFILE = :enableStatus , Indemnity_Creation_Date = :indemnityCreationDate where IDENTIFICATION_KEY= :identificationKey");
                query.setParameter("enableStatus", enableStatus);
                query.setParameter("identificationKey", identificationKey);
                query.setParameter("indemnityCreationDate", indemnityCreationDate);
                query.setMaxResults(1);
                result = query.executeUpdate();
            } catch (Exception e) {
                return -1;
            }
        }
        return result;
    }

    @Override
    public Customer getCustomerNativeByIdentificationKeyAndWalletShortCode(String identificationKey,
                                                                           String walletShortCode) throws GeneralFailureException {
        BusinessEntity businessEntity = new BusinessEntity();
        Customer customer = new Customer();
        String stringQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerNativeByIdentificationKeyAndWalletShortCode", this.getClass(), identificationKey);
        Query query = (Query) this.baseDao.executeNativeQuery(stringQuery);
        List<Object> result = query.getResultList();
        if ((result != null) && (result.size() > 0)) {
            Object[] customerList = (Object[]) result.get(0);
            customer.setCustomerID(getLongValue(customerList[0]));
            customer.setActivationCode(getStringValue(customerList[1]));
            customer.setActivationDate((Date) customerList[2]);
            customer.setIMEI((String) customerList[3]);
            customer.setHandsetPassword((String) customerList[4]);
            customer.setSymmetricWorkingKey((String) customerList[5]);

            customer.setNewLogin(getBooleanValue(customerList[6]));
            customer.setReasonOfClosing((String) customerList[7]);

            customer.setLoginTrials(getIntegerValue(customerList[8]));

            customer.setLoginTrialsPassword(getIntegerValue(customerList[9]));

            customer.setBlockingDate((Date) customerList[10]);
            customer.setLastModifiedDate((Date) customerList[11]);
            customer.setFirstName((String) customerList[12]);

            customer.setStatus(UserStatus.getTypeEnum(getLongValue(customerList[13])));

            businessEntity.setBusinessEntityID(getLongValue(customerList[14]));
            customer.setRegisteringAgent(businessEntity);

            customer.setCustomerRegistrationType(getIntegerValue(customerList[15]));

            customer.setCustomerAuthenticationType(getIntegerValue(customerList[16]));

        }
        return customer;

    }

    private String getStringValue(Object val) {
        if (val == null) {
            return null;
        }

        return (String) val;

    }

    private boolean getBooleanValue(Object val) {
        if (val == null) {
            return false;
        }
        if (val instanceof BigDecimal) {
            return Long.valueOf(((BigDecimal) val).longValue()) == 1L;
        }
        return false;
    }

    private Long getLongValue(Object val) {
        if (val == null) {
            return null;
        }
        if (val instanceof BigDecimal) {
            return ((BigDecimal) val).longValue();
        }
        return null;
    }

    private Integer getIntegerValue(Object val) {
        if (val == null) {
            return null;
        }
        if (val instanceof BigDecimal) {
            return ((BigDecimal) val).intValue();
        }
        return null;
    }

    @Override
    public String getCustomerAccountNumberForCIF(String identificationKey) throws AccountException {

        String customerAccountNum = null;
        if (identificationKey == null) {
            throw new AccountException(AccountException.MISSING_MANDATORY_DATA);
        }

        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerAccountNumberForCIF", this.getClass(), identificationKey);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            customerAccountNum = ((BigDecimal) results.get(0)).toString();
        }
        return customerAccountNum;
    }

    @Override
    public Long getCustomerIndemnityProfileStatus(String identificationKey) throws GeneralFailureException {
        Long enableStatus = 0L;
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerIndemnityProfileStatus", this.getClass(), identificationKey);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            BigDecimal enableStatusBigDecimal = (BigDecimal) results.get(0);
            if (enableStatusBigDecimal != null) {
                enableStatus = Long.valueOf(enableStatusBigDecimal.longValue());
            }

        }
        return enableStatus;
    }

    @Override
    public int updateCustomerNateive(Customer customer) throws GeneralFailureException {
        int result = -1;
        if (customer != null) {

            try {
                String query = ServiceQueryEngine.getQueryStringToExecute("updateCustomerNateive",
                        this.getClass(), String.valueOf(customer.getStatus()), customer.getActivationCode(),
                        String.valueOf(customer.getActivationDate()), customer.getIMEI(), customer.getHandsetPassword(),
                        customer.getSymmetricWorkingKey(), String.valueOf(customer.isNewLogin()),
                        customer.getReasonOfClosing(), String.valueOf(customer.getLoginTrials()),
                        String.valueOf(customer.getLoginTrialsPassword()), String.valueOf(customer.getBlockingDate()),
                        customer.getFirstName(), String.valueOf(customer.getWallet()),
                        String.valueOf(customer.getCustomerID()));
                Query q = (Query) baseDao.executeNativeQuery(query);
                q.executeUpdate();
                return 0;
            } catch (Exception e) {
                return -1;
            }
        }
        return result;

    }


    public String getCustomerSWK(String msisdn, String walletShortCode) throws Exception {
        String swk = getCustomerSWKByMsisdn(msisdn, walletShortCode);
        if (Strings.isNullOrEmpty(swk)) {
            throw new CustomerException("VAL01026");
        }
        return decryptCustomerSWK(swk);
    }

    public void sendTransactionOTP(HashMap parameters) {
        String serviceCode = (String) parameters.get("Service_Code");

        this.notificationManager.sendNotification(serviceCode, parameters);

    }

    @Override
    public Date getCustomerIndemnityCreationDate(String identificationKey) throws GeneralFailureException {
        Date indemnityCreationDate = null;
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getCustomerIndemnityCreationDate", this.getClass(), identificationKey);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results != null) && (results.size() > 0)) {
            indemnityCreationDate = (Date) results.get(0);

        }
        return indemnityCreationDate;
    }

    @Override
    public boolean checkCustomerRegDateForIndemity(BusinessMessage message)
            throws GeneralFailureException, IOException {
        String msisdn = message.getPrimarySenderInfo().getMsisdn();
        String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();
        String userKey = walletShortCode + msisdn;
        String indemityCraetionLimit = this.propertyLoader.loadProperty("Indemity_Transaction_Days_Limit");
        Customer customer = baseDao.findSingleResultByProperty(Customer.class, "identificationKey", userKey);
        CustomerBulkRiskProfile customerBulkRiskProfile = baseDao
                .findSingleResultByProperty(CustomerBulkRiskProfile.class, "customerId", customer.getCustomerID());
        if (customerBulkRiskProfile != null) {
            if (customerBulkRiskProfile.getExpiryDate() == null
                    && "inactive".equals(customerBulkRiskProfile.getStatus())) {
                return true;
            } else if (customerBulkRiskProfile.getExpiryDate() != null
                    && "inactive".equals(customerBulkRiskProfile.getStatus())) {
                Date date = customerBulkRiskProfile.getExpiryDate();
                Calendar c = Calendar.getInstance();
                c.setTime(date);
                c.set(c.HOUR, 1);
                c.set(c.AM_PM, 0);
                c.set(c.MINUTE, 0);
                c.set(c.SECOND, 0);
                c.add(Calendar.DATE, Integer.parseInt(indemityCraetionLimit));
                c.set(c.HOUR, 1);
                if ((new Date().after(c.getTime()))) {
                    return true;
                }
            }
        } else {
            indemityCraetionLimit = this.propertyLoader.loadProperty("Indemity_Creation_Days_Limit");
            String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("checkCustomerRegDateForIndemity", this.getClass(), indemityCraetionLimit, userKey);
            Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
            if ((query.getResultList() != null) && (query.getResultList().size() > 0)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean checkCustomerRiskLimitForIndemity(BusinessMessage message, Long bulkRiskProfileId)
            throws GeneralFailureException, IOException {
        String msisdn = message.getPrimarySenderInfo().getMsisdn();
        String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();
        String userKey = walletShortCode + msisdn;
        Customer customer = baseDao.findSingleResultByProperty(Customer.class, "identificationKey", userKey);
        if (customer.getRiskProfile().equals(bulkRiskProfileId)) {
            return false;
        }
        return true;
    }

    @Override
    public void addCustomerPromoProfile(Long customerId, Long promoProfileId, int span) throws CustomerException {
        CustomerPromoProfile customerPromoProfile = new CustomerPromoProfile();
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("addCustomerPromoProfile", this.getClass(), String.valueOf(customerId));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results == null) || (results.size() >= 1)) {
            throw new CustomerException("BUS01009");
        }
        System.out.println("Start add customer to promo profile for customer with id >>> " + customerId
                + " to promo with id >>> " + promoProfileId);
        Customer customer = baseDao.findById(Customer.class, customerId);
        Long feeProfileId = customer.getFeeProfile();
        customerPromoProfile.setCustomerId(customerId);
        customerPromoProfile.setDefaultFeeProfileId(feeProfileId);
        customerPromoProfile.setPromoProfileId(promoProfileId);
        customerPromoProfile.setStatus("active");
        customerPromoProfile.setAssignedDate(new Date());
        customerPromoProfile.setSpan(span);
        this.baseDao.save(customerPromoProfile);
    }

    @Override
    public void updateCustomerFeeProfileId(Long customerId, Long feeProfileId) throws CustomerException {
        Customer customer = baseDao.findById(Customer.class, customerId);

        if (feeProfileId != null && !(customer == null && customer.getFeeProfile().equals(feeProfileId))) {
            System.out.println("Start update customer to promo profile for customer with id >>> " + customerId
                    + " to promo with id >>> " + feeProfileId);
            customer.setFeeProfile(feeProfileId);
            this.baseDao.update(customer);
        } else {
            System.out.println("Promo fee profile ID not found !!,the default fee id will be assigned");
            throw new CustomerException("BUS01009");
        }
    }

    private ResourceBundle bundle = ResourceBundle.getBundle("promo-pricing");
    int span = Integer.parseInt(bundle.getString("promo.span"));
    private final String anotherUbaAccount = allowedListBundle.getString("another.uba.account");
    private final String otherBanksAccount = allowedListBundle.getString("other.banks.account");
    private final String internationalOtherBank = allowedListBundle.getString("international.other.bank");
    private final String anotherUbaPrepaid = allowedListBundle.getString("another.uba.prepaid.card");
    private final String mobileNumber = allowedListBundle.getString("mobile.number");
    private final String mobileTopUp = allowedListBundle.getString("mobile.topup");
    private final String mobileMoneyWallet = allowedListBundle.getString("mobile.money.wallet");
    private final String mobileMoneyOperator = allowedListBundle.getString("mobile.money.operator");

    @Override
    public String assignCutomersToPromoFeeProfile(String[] customers) {
        String response = "";
        int count = 0;
        Long promoProfileId = Long.parseLong(bundle.getString("promo.id.new.registeration"));
        for (String id : customers) {
            try {
                Long customerId = Long.parseLong(id);
                addCustomerPromoProfile(customerId, promoProfileId, span);
                updateCustomerFeeProfileId(customerId, promoProfileId);
                count++;
                System.out.println(
                        "customer with Id : " + id + " assigned to promo fee profile with Id >> " + promoProfileId);
            } catch (Exception e) {
                System.out.println("some errors happened while assigning customer with Id >> " + id + " to promo");
            }

        }

        return response + "assigned " + count + " user";

    }

    @Override
    public BulkCustomerEntity checkCustomerInBulk(String msisdn, String walletShortCode) {
        Query query = this.baseDao.getEntityManager().createNamedQuery("BulkCustomerEntity.getBulkCustomer",
                BulkCustomerEntity.class);
        query.setParameter(1, msisdn);
        query.setParameter(2, 0);
        query.setParameter(3, walletShortCode);
        List<BulkCustomerEntity> bulkCustomers = query.getResultList();
        BulkCustomerEntity bulkCustomer = null;
        if (bulkCustomers != null && bulkCustomers.size() == 1) {
            bulkCustomer = bulkCustomers.get(0);
        }
        return bulkCustomer;
    }

    @Override
    public void addCustomerBulkRiskProfile(Long customerId, Long bulkRiskProfileId, int span, String serviceCode)
            throws CustomerException {
        CustomerBulkRiskProfile customerBulkRiskProfile = new CustomerBulkRiskProfile();
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("addCustomerBulkRiskProfile", this.getClass(), String.valueOf(customerId));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List results = query.getResultList();
        if ((results == null) || (results.size() >= 1)) {
            throw new CustomerException("BUS01009");
        }
        System.out.println("Start add customer to risk profile for customer with id >>> " + customerId
                + " to risk with id >>> " + bulkRiskProfileId);
        Customer customer = baseDao.findById(Customer.class, customerId);
        Long riskProfileId = customer.getRiskProfile();
        Long customerProfileType = (long) CustomerProfileTypeEnum.REGULAR.getProfileType();
        if (serviceCode == null || !serviceCode.equals(SELF_REGISTRATION_BY_MOBILE)) {
            if (customer.getCustomerProfileTypeID() != CustomerProfileTypeEnum.LIMITED.getProfileType()) {
                customerProfileType = customer.getCustomerProfileTypeID();
            }
        }
        customerBulkRiskProfile.setCustomerId(customerId);
        customerBulkRiskProfile.setDefaultRiskProfileId(riskProfileId);
        customerBulkRiskProfile.setBulkRiskProfileId(bulkRiskProfileId);
        customerBulkRiskProfile.setDefaultProfileType(customerProfileType);
        customerBulkRiskProfile.setStatus("active");
        customerBulkRiskProfile.setAssignedDate(new Date());
        customerBulkRiskProfile.setSpan(span);
        this.baseDao.save(customerBulkRiskProfile);

    }

    @Override
    public void updateCustomerRiskProfileId(Long customerId, Long bulkRiskProfileId) throws CustomerException {
        Customer customer = baseDao.findById(Customer.class, customerId);

        if (!(customer == null && customer.getRiskProfile().equals(bulkRiskProfileId))) {
            System.out.println("Start update customer to risk profile for customer with id >>> " + customerId
                    + " to risk with id >>> " + bulkRiskProfileId);
            customer.setRiskProfile(bulkRiskProfileId);
            this.baseDao.update(customer);
        } else {
            System.out.println("Bulk risk profile id not found !!,the default risk profile id will be assigned");
            throw new CustomerException("BUS01009");
        }

    }

    @Override
    public void updateCustomerToRiskLimit(String msisdn, Long bulkRiskProfileId, int span) throws CustomerException {
        Customer customerByMsisdn = getCustomerByMsisdn(msisdn);
        CustomerBulkRiskProfile customerBulkRisk = baseDao.findSingleResultByProperty(CustomerBulkRiskProfile.class,
                "customerId", customerByMsisdn.getCustomerID());
        Long customerProfileType = (long) CustomerProfileTypeEnum.REGULAR.getProfileType();
        if (customerBulkRisk == null) {
            CustomerBulkRiskProfile customerBulkRiskProfile = new CustomerBulkRiskProfile();
            Long riskProfileId = customerByMsisdn.getRiskProfile();

            customerBulkRiskProfile.setCustomerId(customerByMsisdn.getCustomerID());
            customerBulkRiskProfile.setDefaultRiskProfileId(riskProfileId);
            customerBulkRiskProfile.setBulkRiskProfileId(bulkRiskProfileId);
            if (customerByMsisdn.getCustomerProfileTypeID() != null && customerByMsisdn
                    .getCustomerProfileTypeID() != CustomerProfileTypeEnum.LIMITED.getProfileType()) {
                customerProfileType = customerByMsisdn.getCustomerProfileTypeID();
            }
            customerBulkRiskProfile.setDefaultProfileType(customerProfileType);
            customerBulkRiskProfile.setStatus("active");
            customerBulkRiskProfile.setAssignedDate(new Date());
            customerBulkRiskProfile.setSpan(span);
            customerBulkRisk = baseDao.update(customerBulkRiskProfile);
        } else {
            customerBulkRisk.setStatus("active");
            customerBulkRisk.setExpiryDate(null);
            if (customerByMsisdn.getCustomerProfileTypeID() != null && customerByMsisdn
                    .getCustomerProfileTypeID() != CustomerProfileTypeEnum.LIMITED.getProfileType()) {
                customerProfileType = customerByMsisdn.getCustomerProfileTypeID();
            }
            customerBulkRisk.setDefaultProfileType(customerProfileType);
            baseDao.update(customerBulkRisk);
        }
        customerByMsisdn.setRiskProfile(customerBulkRisk.getBulkRiskProfileId());
        customerByMsisdn.setCustomerProfileTypeID(Long.valueOf(CustomerProfileTypeEnum.LIMITED.getProfileType()));
        baseDao.update(customerByMsisdn);
    }

    @Override
    public List<CustomerBulkRiskProfile> getCustomerRiskProfile(Long customerId) throws CustomerException {
        List<CustomerBulkRiskProfile> customerBulkRiskProfileList = baseDao
                .findByProperty(CustomerBulkRiskProfile.class, "customerId", customerId, false);
        return customerBulkRiskProfileList;
    }

    @Override
    public void updateCustomerRiskProfileExpiryDate(CustomerBulkRiskProfile customerBulkRiskProfile)
            throws CustomerException {
        baseDao.update(customerBulkRiskProfile);
    }

    @Override
    public void updateBulkCustomerEnrolled(String msisdn, String walletShortCode) {
        BulkCustomerEntity customer = checkCustomerInBulk(msisdn, walletShortCode);
        customer.setIsEnrolled(1);
        this.baseDao.update(customer);

    }

    public String getFeeProfileNameById(Long id) throws GeneralFailureException {
        String feeProfileName = null;
        try {
            feeProfileName = baseDao.findById(FeeProfile.class, id).getProfileName();
        } catch (Exception e) {
            throw new GeneralFailureException("BUS01010", e);
        }

        return feeProfileName;
    }

    @Override
    public BusinessMessage addBeneficiariesToAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {

        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
        AllowedList allowedList = businessMessage.getAllowedList().get(0);
        int count = 0;
        if (allowedList != null) {
            if (!checkCustomerInAllowedList(allowedList, userKey)) {
                addAndUpdateBeneficiaryInAllowedList(allowedList, userKey, "add");
            }
        } else {
            throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
        }
        if (allowedList != null) {
            Map softFields = businessMessage.getSoftFields();
            if (softFields != null)
                softFields.put("beneficiaryName", allowedList.getBeneficiaryName());
        }
        businessMessage.setAllowedList(new ArrayList<AllowedList>());
        getAllBeneficiariesFromAllowedList(businessMessage);

        return businessMessage;
    }

    @Override
    public BusinessMessage importBeneficiariesToAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {
        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
        List<AllowedList> allowedList = businessMessage.getAllowedList();
        int importListSize = allowedList.size();
        int failCases = 0;
        if (allowedList != null) {
            for (AllowedList list : allowedList) {
                try {
                    if (!checkCustomerInAllowedList(list, userKey)) {
                        list = addAndUpdateBeneficiaryInAllowedList(list, userKey, "add");
                    }
                } catch (BeneficiaryListException ex) {
                    ++failCases;
                }
            }
            String successMessage = "";
            ServiceSuccessResponse successResponse = new ServiceSuccessResponse();
            if (failCases == 0) {
                successMessage = "All your favorites have been imported successfully into your beneficiary list";
                successResponse.setStatusCode("importAll");
                businessMessage.setServiceSuccessResponse(successResponse);
                successResponse.setSuccessShortDescription("Info");
            } else if (failCases < importListSize && failCases != 0) {
                successResponse.setStatusCode("importPartial");
                String[] params = {"" + (importListSize - failCases), "" + importListSize, "" + failCases};
                successResponse.setParams(params);
                successResponse.setSuccessShortDescription("Info");
                businessMessage.setServiceSuccessResponse(successResponse);

            } else if (failCases == importListSize) {
                successResponse.setStatusCode("importAlreadyExist");
                successResponse.setSuccessShortDescription("Info");
                businessMessage.setServiceSuccessResponse(successResponse);

            }

        } else {
            throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
        }
        businessMessage.setAllowedList(new ArrayList<AllowedList>());
        getAllBeneficiariesFromAllowedList(businessMessage);

        return businessMessage;
    }

    @Override
    public BusinessMessage deleteBeneficiaryFromAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {
        List<AllowedList> list = businessMessage.getAllowedList();
        if (list != null) {
            for (AllowedList allowedList : list) {
                if (allowedList.getId() != null) {
                    this.baseDao.executeNamedQuery("AllowedList.deleteBeneficiaryById", AllowedBeneficiaryList.class,
                            true, false, allowedList.getId());
                }
            }
        } else {
            throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
        }
        return businessMessage;
    }

    @Override
    public BusinessMessage getAllBeneficiariesFromAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {
        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
        String Currency = businessMessage.getWalletInfo().getCurrency();
        String beneficiaryId = null;
        String transferType = null;

        if (businessMessage.getAllowedList() != null && !businessMessage.getAllowedList().isEmpty()
                && businessMessage.getAllowedList().get(0) != null) {
            beneficiaryId = businessMessage.getAllowedList().get(0).getBeneficiaryId();
            transferType = businessMessage.getAllowedList().get(0).getTransferType();
        }

        if (userKey == null || userKey.trim() == "")
            throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);

        AllowedList list = new AllowedList();
        List<AllowedList> allowedList = new ArrayList<AllowedList>();
        int count = 0;

        if (beneficiaryId != null) {
            AllowedBeneficiaryList beneficiary = this.baseDao.executeNamedQuerySingleResult(
                    "AllowedList.getBeneficiaryByAccountNumberAndUserKeyAndTransferType", AllowedBeneficiaryList.class,
                    false, userKey, beneficiaryId, transferType);
            list = setBeneficiaryintoList(beneficiary, Currency);
            if (list != null)
                allowedList.add(list);
        } else {
            List<AllowedBeneficiaryList> beneficiaryAllowedlist = this.baseDao.executeNamedQuery(
                    "AllowedList.getAllBeneficiariesByUserKey", AllowedBeneficiaryList.class, false, false, userKey);

            if (beneficiaryAllowedlist != null) {
                for (AllowedBeneficiaryList benef : beneficiaryAllowedlist) {

                    list = setBeneficiaryintoList(benef, Currency);
                    allowedList.add(list);
                }
            }
        }
        businessMessage.setAllowedList(allowedList);
        return businessMessage;
    }

    @Override
    public BusinessMessage getAllInternationalBeneficiariesFromAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {
        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();

        String beneficiaryId = null;
        String transferType = null;
        String Currency = businessMessage.getWalletInfo().getCurrency();
        if (businessMessage.getAllowedList() != null && !businessMessage.getAllowedList().isEmpty()
                && businessMessage.getAllowedList().get(0) != null) {
            beneficiaryId = businessMessage.getAllowedList().get(0).getBeneficiaryId();
            transferType = businessMessage.getAllowedList().get(0).getTransferType();
        }

        if (userKey == null || userKey.trim() == "")
            throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);

        AllowedList list = new AllowedList();
        List<AllowedList> allowedList = new ArrayList<AllowedList>();
        if (beneficiaryId != null) {
            AllowedBeneficiaryList beneficiary = this.baseDao.executeNamedQuerySingleResult(
                    "AllowedList.getBeneficiaryByAccountNumberAndUserKeyAndTransferType", AllowedBeneficiaryList.class,
                    false, userKey, beneficiaryId, transferType);
            list = setBeneficiaryintoList(beneficiary, Currency);
            if (list != null)
                allowedList.add(list);
        } else {
            List<AllowedBeneficiaryList> beneficiaryAllowedlist = this.baseDao.executeNamedQuery(
                    "AllowedList.getAllBeneficiariesByUserKeyAndTransferType", AllowedBeneficiaryList.class, false,
                    false, userKey, internationalOtherBank);
            if (beneficiaryAllowedlist != null) {
                for (AllowedBeneficiaryList benef : beneficiaryAllowedlist) {
                    list = setBeneficiaryintoList(benef, Currency);
                    allowedList.add(list);
                }
            }
        }
        businessMessage.setAllowedList(allowedList);
        return businessMessage;
    }

    private AllowedList setBeneficiaryintoList(AllowedBeneficiaryList benef, String Currency) {

        if (benef != null) {
            AllowedList list = new AllowedList();
            list.setId(benef.getId());
            list.setBeneficiaryId(StringEscapeUtils.escapeXml(benef.getBeneficiaryId()));
            list.setTransferType(benef.getTransferType());
            list.setBeneficiaryName(StringEscapeUtils.escapeXml(benef.getBeneficiaryName()));
            list.setLastTransferDate(benef.getLastTransferDate());
            list.setPreferential(benef.getIsPreferential() == 1);
            list.setBeneficiaryAddress(StringEscapeUtils.escapeXml(benef.getAddress()));
            list.setBeneficiaryCurrency(benef.getCurrency());
            if (anotherUbaPrepaid.equals(benef.getTransferType())) {
                list.setCardNumber(benef.getCardNumber());
            } else if (otherBanksAccount.equals(benef.getTransferType())) {
                list.setTransferTypeCategory(benef.getTransferTypeCategory());
                list.setInstitutionCode(benef.getInstitutionCode());
                list.setCountryName(benef.getCountryName());
                list.setSwiftCode(benef.getSwiftCode());
                list.setBankAddress(benef.getBankAddress());
                list.setInstitutionName(benef.getInstitutionName());
                list.setBranchCode(benef.getBranchCode());
                list.setBranchName(benef.getBranchName());
                list.setWalletShortCode(benef.getWalletShortCode());
                if (benef.getCurrency() != null) {
                    list.setBeneficiaryCurrency(benef.getCurrency());
                } else {
                    list.setBeneficiaryCurrency(Currency);
                }
            } else if (mobileMoneyWallet.equals(benef.getTransferType())) {
                list.setMobileOperator(benef.getMobileOperator());
            } else if (mobileTopUp.equals(benef.getTransferType())) {
                list.setCountryIso2(benef.getCountryIso2());
            } else if (internationalOtherBank.equals(benef.getTransferType())) {
                list.setTransferTypeCategory(benef.getTransferTypeCategory());
                list.setCountryName(benef.getCountryName());
                list.setInstitutionName(benef.getInstitutionName());
                list.setBankAddress(benef.getBankAddress());
                Gson gson = new Gson();
                TransferData trData = gson.fromJson(benef.getInternationalBankDetails().getInternationalTransfer(),
                        TransferData.class);
                list.setInternationalBankDetails(trData);
            }
            return list;
        }
        return null;
    }

    @Override
    public boolean checkCustomerInAllowedList(AllowedList list, String userKey) throws BeneficiaryListException {
        if (list != null) {

            String beneficiaryName = list.getBeneficiaryName();
            if (beneficiaryName == null)
                throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
            List<AllowedBeneficiaryList> benefList = this.baseDao.executeNamedQuery(
                    "AllowedList.getAllowedBeneficiariesByBeneficiaryName", AllowedBeneficiaryList.class, false, false,
                    beneficiaryName, userKey);
            if (benefList == null || benefList.size() == 0) {
                String beneficiaryId = list.getBeneficiaryId();
                String transferType = list.getTransferType();
                if (beneficiaryId == null || transferType == null)
                    throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
                List<AllowedBeneficiaryList> benefList2;
                if (transferType.equals(otherBanksAccount)) {
                    String transferTypeCategory = list.getTransferTypeCategory();
                    if (transferTypeCategory == null)
                        throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
                    benefList2 = this.baseDao.executeNamedQuery(
                            "AllowedList.getAllowedBeneficiariesByBeneficiaryIdAndTransferTypeAndtransferTypeCategory",
                            AllowedBeneficiaryList.class, false, false, beneficiaryId, transferType,
                            transferTypeCategory, userKey);
                } else {
                    benefList2 = this.baseDao.executeNamedQuery(
                            "AllowedList.getAllowedBeneficiariesByBeneficiaryIdAndTransferType",
                            AllowedBeneficiaryList.class, false, false, beneficiaryId, transferType, userKey);
                }
                if (benefList2 == null || benefList2.size() == 0) {
                    return false;
                } else {
                    HashMap<String, String> beneficiaryErrorMessages = new HashMap<String, String>();
                    String existBeneficiaryName = benefList2.get(0).getBeneficiaryName();
                    beneficiaryErrorMessages.put("ruleName", existBeneficiaryName);

                    System.out.println(
                            "This account already exist => checkCustomerInAllowedList() for as a component not step");
                    throw new BeneficiaryListException(BeneficiaryListException.CUSTOMER_ACCOUNT_ALEREADY_EXIST,
                            beneficiaryErrorMessages);
                }
            } else {
                System.out.println("Name already exist => checkCustomerInAllowedList() for as a component not step");
                throw new BeneficiaryListException(BeneficiaryListException.CUSTOMER_NAME_ALREADY_EXIST);
            }
        }
        return true;
    }

    @Override
    public BusinessMessage updateBeneficiaryInAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {
        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
        List<AllowedList> list = businessMessage.getAllowedList();
        if (list == null || list.size() == 0) {
            throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
        } else {
            for (AllowedList allowed : list) {
                if (allowed.getId() != null) {
                    List<AllowedBeneficiaryList> benefList = this.baseDao.executeNamedQuery(
                            "AllowedList.getAllowedByNameForUpdate", AllowedBeneficiaryList.class, false, false,
                            allowed.getBeneficiaryName(), allowed.getId(), userKey);
                    if (benefList == null || benefList.size() == 0) {
                        String beneficiaryId = allowed.getBeneficiaryId();
                        String transferType = allowed.getTransferType();
                        Long id = allowed.getId();
                        List<AllowedBeneficiaryList> benefList2;
                        if (transferType.equals(otherBanksAccount)) {
                            String transferTypeCategory = allowed.getTransferTypeCategory();
                            benefList2 = this.baseDao.executeNamedQuery(
                                    "AllowedList.getAllowedByBenefIdAndTransferTypeAndTransferTypeCategory",
                                    AllowedBeneficiaryList.class, false, false, beneficiaryId, transferType,
                                    transferTypeCategory, id, userKey);
                        } else {
                            benefList2 = this.baseDao.executeNamedQuery(
                                    "AllowedList.getAllowedByBenefIdAndTransferType", AllowedBeneficiaryList.class,
                                    false, false, beneficiaryId, transferType, id, userKey);
                        }
                        if (benefList2 == null || benefList2.size() == 0) {
                            addAndUpdateBeneficiaryInAllowedList(allowed, userKey, "update");
                        } else {
                            System.out.println("This account already exist ");
                            HashMap<String, String> beneficiaryErrorMessages = new HashMap<String, String>();
                            String existBeneficiaryName = benefList2.get(0).getBeneficiaryName();
                            beneficiaryErrorMessages.put("ruleName", existBeneficiaryName);
                            throw new BeneficiaryListException(BeneficiaryListException.CUSTOMER_ACCOUNT_ALEREADY_EXIST,
                                    beneficiaryErrorMessages);
                        }
                    } else {
                        System.out.println("Name already exist ");
                        throw new BeneficiaryListException(BeneficiaryListException.CUSTOMER_NAME_ALREADY_EXIST);
                    }
                } else {
                    System.out.println(" Data Missing from the request !! => updateBeneficiaryInAllowedList()");
                    throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
                }
            }
        }
        return businessMessage;
    }

    private AllowedList addAndUpdateBeneficiaryInAllowedList(AllowedList list, String userKey, String operation)
            throws BeneficiaryListException {
        AllowedBeneficiaryList benefList = new AllowedBeneficiaryList();
        String beneficiaryId = list.getBeneficiaryId();
        String transferType = list.getTransferType();
        String beneficiaryName = list.getBeneficiaryName();
        int isPreferentialBeneficiary = list.isPreferential() == false ? 0 : 1;

        if (transferType.equals(anotherUbaPrepaid)) {
            String cardNumber = list.getCardNumber();
            benefList.setCardNumber(cardNumber);
        } else if (transferType.equals(otherBanksAccount)) {
            String transferTypeCategory = list.getTransferTypeCategory();
            String countryName = list.getCountryName();
            benefList.setTransferTypeCategory(transferTypeCategory);
            benefList.setCountryName(countryName);
            if (list.getInstitutionCode() != null || list.getInstitutionCode().trim() != "")
                benefList.setInstitutionCode(list.getInstitutionCode());
            else
                throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
            benefList.setInstitutionName(list.getInstitutionName());
            benefList.setBranchCode(list.getBranchCode());
            benefList.setBranchName(list.getBranchName());
            benefList.setWalletShortCode(list.getWalletShortCode());
            benefList.setSwiftCode(list.getSwiftCode());
            benefList.setBankAddress(list.getBankAddress());
        } else if (transferType.equals(mobileMoneyWallet)) {
            benefList.setMobileOperator(list.getMobileOperator());
        } else if (transferType.equals(mobileTopUp)) {
            benefList.setCountryIso2(list.getCountryIso2());
        } else if (transferType.equals(internationalOtherBank)) {

            benefList.setTransferTypeCategory(list.getTransferTypeCategory());
            InternationalBankDetails iBankDetails = new InternationalBankDetails();
            benefList.setCountryName(list.getCountryName());

            benefList.setInstitutionName(list.getInstitutionName());
            benefList.setBranchCode(list.getBranchCode());
            benefList.setBranchName(list.getBranchName());
            benefList.setBankAddress(list.getBankAddress());

            Gson gson = new Gson();
            String nternationalBankJSON = gson.toJson(list.getInternationalBankDetails());
            iBankDetails.setInternationalTransfer(nternationalBankJSON);
            benefList.setInternationalBankDetails(iBankDetails);
        }
        benefList.setBeneficiaryId(beneficiaryId);
        benefList.setTransferType(transferType);
        benefList.setBeneficiaryName(beneficiaryName);
        benefList.setDate(new Date());
        benefList.setUserKey(userKey);
        benefList.setIsPreferential(isPreferentialBeneficiary);
        benefList.setCurrency(list.getBeneficiaryCurrency());
        benefList.setAddress(list.getBeneficiaryAddress());
        if (operation.equals("update")) {
            System.out.println("Update Beneficiary in Allowed List ");
            benefList.setId(list.getId());
            this.baseDao.update(benefList);
        } else if (operation.equals("add")) {
            System.out.println("Add Beneficiary To Allowed List ");
            this.baseDao.save(benefList);
            list.setId(benefList.getId());
        }
        return list;

    }

    @Override
    public BusinessMessage checkBeneficiaryInAllowedList(BusinessMessage businessMessage)
            throws BeneficiaryListException {
        if (businessMessage.getCheckAllowed()) {
            if (businessMessage.getAllowedList() == null || businessMessage.getAllowedList().size() == 0)
                throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);

            AllowedList allowedList = businessMessage.getAllowedList().get(0);

            if (allowedList.getBeneficiaryId() == null || allowedList.getBeneficiaryId() == ""
                    || allowedList.getTransferType() == null || allowedList.getTransferType() == ""
                    || allowedList.getId() == null) {
                throw new BeneficiaryListException(BeneficiaryListException.MISSING_DATA);
            }

            String beneficiaryId = allowedList.getBeneficiaryId();
            String transferType = allowedList.getTransferType();
            String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
            Long id = allowedList.getId();

            List<AllowedBeneficiaryList> benefList;
            if (transferType.equals(otherBanksAccount)) {
                String transferTypeCategory = allowedList.getTransferTypeCategory();
                benefList = this.baseDao.executeNamedQuery(
                        "AllowedList.getAllowedBeneficiariesByIdAndBeneficiaryIdAndTransferTypeAndTransferTypeCategory",
                        AllowedBeneficiaryList.class, false, false, id, beneficiaryId, transferType, userKey,
                        mobileTopUp, transferTypeCategory);
            } else {
                benefList = this.baseDao.executeNamedQuery(
                        "AllowedList.getAllowedBeneficiariesByIdAndBeneficiaryIdAndTransferType",
                        AllowedBeneficiaryList.class, false, false, id, beneficiaryId, transferType, userKey,
                        mobileTopUp);
            }
            if (benefList.size() == 0 || benefList == null) {
                System.out.println("Beneficiary not in the allowed list ");
                throw new BeneficiaryListException(BeneficiaryListException.CUSTOMER_NOT_FOUND);
            }
        }
        return businessMessage;
    }

    @Override
    public void updateBeneficiaryLastTransferDate(BusinessMessage businessMessage) {
        try {
            Long id = businessMessage.getAllowedList().get(0).getId();
            List<AllowedBeneficiaryList> benefList = this.baseDao.executeNamedQuery("AllowedList.getAllowedById",
                    AllowedBeneficiaryList.class, false, false, id);
            if (benefList != null && benefList.size() != 0) {
                AllowedBeneficiaryList benef = benefList.get(0);
                benef.setLastTransferDate(new Date());
                this.baseDao.update(benef);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("there are some errors happend while updating the transfer date >>>> ");
        }
    }

    @Override
    public BusinessMessage loadWalletInfo(BusinessMessage businessMessage) throws GeneralFailureException {
        WalletInfo walletInfo = baseDao.executeNamedQuerySingleResult("WalletInfo.byWalletShortCode", WalletInfo.class,
                true, businessMessage.getWalletInfo().getWalletShortCode());
        businessMessage.getWalletInfo().setCountryIso2(walletInfo.getCountryIso2());
        businessMessage.getWalletInfo().setCountryIso3(walletInfo.getCountryIso3());
        businessMessage.getWalletInfo().setCountryCode(walletInfo.getCountryCode());
        businessMessage.getWalletInfo().setCurrencyShortCode(walletInfo.getCurrencyShortCode());
        businessMessage.getWalletInfo().setTimeZone(walletInfo.getTimeZone());
        businessMessage.getWalletInfo().setUserGroup(walletInfo.getUserGroup());
        businessMessage.getWalletInfo().setSmsSenderId(walletInfo.getSmsSenderId());
        businessMessage.getWalletInfo().setCountryName(walletInfo.getCountryName());
        businessMessage.getWalletInfo().setCurrency(walletInfo.getCurrency());
        businessMessage.getWalletInfo().setCurrencySymbol(walletInfo.getCurrencySymbol());
        businessMessage.getWalletInfo().setWalletName(walletInfo.getWalletName());
        return businessMessage;
    }

    @Override
    public GeneralLookups getGeneralLookUpData(String walletShortCode) {

        GeneralLookups generalLookups = baseDao.executeNamedQuerySingleResult("GeneralLookups.findById",
                GeneralLookups.class, false, walletShortCode);

        return generalLookups;
    }

    private PromoProfile getCustomerPromoFeeProfile(String promoProfileId) {
        PromoProfile promoProfile = this.baseDao.executeNamedQueryForSingleResult(
                "PromoProfile.findPromoProfileByFeeProfileId", PromoProfile.class, false, false, promoProfileId);
        return promoProfile;
    }

    public List<CustomerPromoProfile> getCustomerPromoProfileListBycustomerId(Customer customer) {
        List<CustomerPromoProfile> customerPromoProfileList = this.baseDao.executeNamedQuery(
                "CustomerPromoProfile.getCustomerPromoProfileByCustomerId", CustomerPromoProfile.class, false, false,
                customer.getCustomerID());
        return customerPromoProfileList;
    }

    public PromoPricing getActivePromoProfileDetailsByCustomer(Customer customer) throws GeneralFailureException {
        CustomerPromoProfile customerPromoProfile = this
                .getCustomerProfileIdByCustomerIdAndStatus(customer.getCustomerID());
        PromoProfile promoProfile = null;
        PromoPricing promoPricing = new PromoPricing();

        if (customerPromoProfile != null)
            promoProfile = getCustomerPromoFeeProfile(customerPromoProfile.getPromoProfileId().toString());

        if (promoProfile != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            promoPricing.setSpan(customerPromoProfile.getSpan());
            promoPricing.setStartDate(simpleDateFormat.format(promoProfile.getStartDate()));
            if (promoProfile.getEndDate() != null)
                promoPricing.setEndDate(simpleDateFormat.format(promoProfile.getEndDate()));
            promoPricing.setPromoStatus(promoProfile.getStatus());
            promoPricing.setDescription(promoProfile.getDescription());
            promoPricing.setPromoProfileId(promoProfile.getFeeProfileId());
        }
        return promoPricing;
    }

    @Override
    public String getActiveCustomerPromoProfileId(Long customerId) throws GeneralFailureException {
        CustomerPromoProfile customerPromoProfile = this.getCustomerProfileIdByCustomerIdAndStatus(customerId);
        if (customerPromoProfile != null && customerPromoProfile.getPromoProfileId() != null)
            return customerPromoProfile.getPromoProfileId().toString();
        else
            return null;
    }

    @Override
    public PromoPricing getLatestCustomerPromoProfile(BusinessMessage businessMessage) throws GeneralFailureException {
        String userKey;
        if (businessMessage.getPrimarySenderInfo().getUserKey() == null) {
            userKey = businessMessage.getWalletInfo().getWalletShortCode()
                    .concat(businessMessage.getPrimarySenderInfo().getMsisdn());
            businessMessage.getPrimarySenderInfo().setUserKey(userKey);
        }
        Customer customer = this.getCustomer(businessMessage.getPrimarySenderInfo().getUserKey());
        PromoPricing promoPricing = null;
        if (customer != null) {
            promoPricing = getActivePromoProfileDetailsByCustomer(customer);

            if (promoPricing == null || promoPricing.getPromoProfileId() == null) {
                List<CustomerPromoProfile> customerPromoProfileList = this.baseDao.getEntityManager()
                        .createNamedQuery("CustomerPromoProfile.getCustomerPromoProfileByCustomerIdAndAssignedDate")
                        .setParameter("customerId", customer.getCustomerID()).getResultList();
                if (customerPromoProfileList != null && customerPromoProfileList.size() > 0) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    promoPricing.setPromoStatus(customerPromoProfileList.get(0).getStatus());
                    promoPricing.setAssignedDate(
                            simpleDateFormat.format(customerPromoProfileList.get(0).getAssignedDate()));
                    promoPricing.setPromoProfileId(customerPromoProfileList.get(0).getPromoProfileId().toString());
                }
            }
        }
        return promoPricing;
    }

    @Override
    public PromoPricing getAllCustomerPromoProfile(BusinessMessage businessMessage) throws GeneralFailureException {
        Customer customer = this.getCustomer(businessMessage.getPrimarySenderInfo().getUserKey());
        String activeCustomerPromoProfileId = getActiveCustomerPromoProfileId(customer.getCustomerID());
        List<CustomerPromoProfile> allCustomerPromoProfileList = new ArrayList<CustomerPromoProfile>();

        if (customer != null) {
            allCustomerPromoProfileList = getCustomerPromoProfileListBycustomerId(customer);
        }
        PromoPricing promoPricing = new PromoPricing();
        promoPricing.setPromoProfileId(activeCustomerPromoProfileId);

        return promoPricing;
    }

    @Override
    public CustomerPromoProfile getCustomerProfileIdByCustomerIdAndStatus(Long customerId)
            throws GeneralFailureException {
        CustomerPromoProfile customerPromoProfile = null;
        if (customerId != null)
            customerPromoProfile = this.baseDao.executeNamedQueryForSingleResult(
                    "CustomerPromoProfile.findPromoProfileIdByCustomerIdAndStatus", CustomerPromoProfile.class, false,
                    false, customerId, "active");
        return customerPromoProfile;
    }

    public PromoPricing getPromoPricingByPromoProfileId(String promoProfileId) {
        PromoProfile promoProfile = getCustomerPromoFeeProfile(promoProfileId);
        if (promoProfile != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

            PromoPricing promoPricing = new PromoPricing();
            promoPricing.setStartDate(simpleDateFormat.format(promoProfile.getStartDate()));
            promoPricing.setEndDate(simpleDateFormat.format(promoProfile.getEndDate()));
            promoPricing.setStatus(promoProfile.getStatus());
            promoPricing.setDescription(promoProfile.getDescription());
            promoPricing.setPromoProfileId(promoProfile.getFeeProfileId());

            return promoPricing;
        } else {
            return null;
        }
    }

    public PromoPricing getOldFeeProfileIdByCustomerPromoProfile(BusinessMessage businessMessage)
            throws CustomerException {
        Customer customer = this.getCustomer(businessMessage.getPrimarySenderInfo().getUserKey());
        CustomerPromoProfile customerPromoProfile = null;
        if (customer != null)
            customerPromoProfile = this.baseDao.executeNamedQueryForSingleResult(
                    "CustomerPromoProfile.findDefaultFeeProfileIdByCustomerIdAndStatusAndPromoProfileId",
                    CustomerPromoProfile.class, false, false, customer.getCustomerID(), "active");

        PromoPricing promoPricing = new PromoPricing();
        if (customerPromoProfile != null && customer.getFeeProfile().equals(customerPromoProfile.getPromoProfileId())) {
            promoPricing.setOldCustomerFeeProfile(new OldCustomerFeeProfile());
            promoPricing.getOldCustomerFeeProfile()
                    .setOldProfileId(customerPromoProfile.getDefaultFeeProfileId().toString());
            ServiceConfigMap serviceType = baseDao.findById(ServiceConfigMap.class,
                    Long.valueOf(businessMessage.getServiceInfo().getServiceTypeForFee()));

            List<FeeValueModel> feeValues = baseDao.executeDynamicQuery(
                    "select feeValueModel from FeeProfile p inner join p.feeValues feeValueModel where p.id = "
                            + customerPromoProfile.getDefaultFeeProfileId()
                            + "and feeValueModel.feeModel.businessServiceType.id =  " + serviceType.getId(),
                    FeeValueModel.class, false, false);

            if (feeValues != null && feeValues.size() > 0) {
                FeeValueModel feeValueModel = feeValues.get(0);
                promoPricing.getOldCustomerFeeProfile().setOldFeeValue(feeValueModel.getValue());
                promoPricing.getOldCustomerFeeProfile().setOldVat(
                        feeValueModel.getVatPercent() != null ? feeValueModel.getVatPercent().toString() : "0");
                Double feeAmount = Double.parseDouble(feeValueModel.getValue());
                Double feeAndVatSum = feeAmount;
                if (feeValueModel.getVatPercent() != null)
                    feeAndVatSum += ((feeValueModel.getVatPercent() * feeAmount) / 100);
                promoPricing.getOldCustomerFeeProfile().setOldFeeAndVatValue(feeAndVatSum.toString());
            }
        }
        return promoPricing;
    }

    public PromoPricing getCustomerPromoDetailsByPromoProfileIdAndCustomerId(BusinessMessage businessMessage)
            throws GeneralFailureException {
        Customer customer = null;
        CustomerPromoProfile customerPromoProfile = null;
        try {
            customer = this.getCustomer(businessMessage.getPrimarySenderInfo().getUserKey());
        } catch (CustomerException e) {
            e.printStackTrace();
        }

        if (customer != null && businessMessage.getPromoPricing() != null
                && businessMessage.getPromoPricing().getPromoProfileId() != null) {
            customerPromoProfile = this.baseDao.executeNamedQueryForSingleResult(
                    "CustomerPromoProfile.findDefaultFeeProfileIdByCustomerIdAndStatusAndPromoProfileId",
                    CustomerPromoProfile.class, false, false, customer.getCustomerID(), "active");
        }

        String language = businessMessage.getPrimarySenderInfo().getLanguage() != null
                ? businessMessage.getPrimarySenderInfo().getLanguage()
                : "en";
        PromoPricing promoPricing = new PromoPricing();
        if (customerPromoProfile != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            promoPricing.setAssignedDate(simpleDateFormat.format(customerPromoProfile.getAssignedDate()));
            promoPricing.setStatus(customerPromoProfile.getStatus());
            promoPricing.setPromoProfileId(String.valueOf(customerPromoProfile.getPromoProfileId()));
            promoPricing.setSpan(customerPromoProfile.getSpan());

            Date date = customerPromoProfile.getAssignedDate();
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.set(c.HOUR, 1);
            c.set(c.AM_PM, 0);
            c.set(c.MINUTE, 0);
            c.set(c.SECOND, 0);
            c.add(Calendar.DATE, customerPromoProfile.getSpan());
            promoPricing.setEndDate(simpleDateFormat.format(c.getTime()));
            ExceptionResolver resolver = ExceptionUtil.handle(language, "activePromoDesc");
            String resolvedMessage = resolver.getDescription();
            if (resolvedMessage != null) {
                resolvedMessage = resolvedMessage.replaceAll("promoAssignedDateVal", promoPricing.getAssignedDate());
                resolvedMessage = resolvedMessage.replaceAll("promoEndDateVal", promoPricing.getEndDate());
            }
            promoPricing.setMessage(resolvedMessage);
            return promoPricing;
        } else {
            if (Integer.parseInt(businessMessage.getClientInfo().getVersion().getMajor()) <= 5
                    && Integer.parseInt(businessMessage.getClientInfo().getVersion().getMinor()) <= 2
                    && Integer.parseInt(businessMessage.getClientInfo().getVersion().getBuild()) < 89) {
                throw new GeneralFailureException("VAL01231");
            } else {
                ExceptionResolver resolver = ExceptionUtil.handle(language, "inActivePromoDesc");
                promoPricing.setStatus("inactive");
                promoPricing.setMessage(resolver.getDescription());
            }
        }
        return promoPricing;
    }

    @Override
    @Transactional
    public boolean checkCustomerMaxDevices(BusinessMessage businessMessage) throws GeneralFailureException {
        Customer customer = getCustomer(businessMessage.getPrimarySenderInfo().getMsisdn(),
                businessMessage.getPrimarySenderInfo().getWalletShortCode());
        if (customerDevicesManager.getCustomerDevicesCount(customer.getCustomerID()) >= customerDevicesManager
                .getMultiDevicesConfig().getMaxNumberOfDevices()) {
            throw new GeneralFailureException("VAL05028");
        } else
            return true;
    }

    public static boolean isFromAddNewDevice() {
        return isFromAddNewDevice;
    }

    public static void setFromAddNewDevice(boolean isFromAddNewDevice) {
        CustomerManagerImpl.isFromAddNewDevice = isFromAddNewDevice;
    }

    @Override
    public BusinessMessage validateBulkCustomer(BusinessMessage businessMessage) throws Exception {
        PartyDetails senderInfo = businessMessage.getPrimarySenderInfo();

        if (senderInfo == null) {
            handleMissingAttributesException("SenderInfo");
        }

        String msisdn = senderInfo.getMsisdn();
        if (Strings.isNullOrEmpty(msisdn)) {
            handleMissingAttributesException("SenderInfo : MSISDN");
        }
        String walletShortCode = senderInfo.getWalletShortCode();
        if (Strings.isNullOrEmpty(walletShortCode)) {
            handleMissingAttributesException("SenderInfo : WalletShortCode");
        }
        Customer customer = null;
        String registeredCustomerOtp = "";
        BulkCustomerEntity bulkCustomer = checkCustomerInBulk(msisdn, walletShortCode);
        if (bulkCustomer == null) {
            throw new CustomerException("BUS01009");
        }
        registeredCustomerOtp = bulkCustomer.getActivationCode();
        if (bulkCustomer.getExpiryDate() != null) {
            if (isOtpExpired(bulkCustomer.getExpiryDate())) {
                throw new CustomerException("VAL199304");
            }
        }

        String otp = senderInfo.getSmsActivationCode();
        String encOTP = AESEncryption.encrypt(otp);

        if (Strings.isNullOrEmpty(otp)) {
            handleMissingAttributesException("SenderInfo : ActivationCode");
        }

        if (Strings.isNullOrEmpty(registeredCustomerOtp)) {
            throw new CustomerException("BUS01014");
        }
        if (!encOTP.equals(registeredCustomerOtp)) {
            throw new CustomerException("BUS13015");
        }

        String iMei = senderInfo.getImei();
        if (Strings.isNullOrEmpty(iMei)) {
            handleMissingAttributesException("SenderInfo : IMEI");
        }

        String swk = senderInfo.getSwk();
        if (Strings.isNullOrEmpty(swk)) {
            handleMissingAttributesException("SenderInfo : SWK");
        }
        return businessMessage;
    }

    @Override
    public List<String> getStuffSchemCodes(String schemCode) {
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("getStuffSchemCodes", this.getClass(), schemCode);
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List stuffSchemCodes = query.getResultList();

        return stuffSchemCodes;

    }

    @Override
    public CustomerSettings getCustomerPreferences(BusinessMessage businessMessage) throws CustomerException {
        Customer customer = getCustomer(businessMessage.getPrimarySenderInfo().getMsisdn(),
                businessMessage.getPrimarySenderInfo().getWalletShortCode());
        CustomerPreferences customerPreferences = this.baseDao.executeNamedQueryForSingleResult(
                "CustomerPreferences.getCustomerPreferences", CustomerPreferences.class, false, false,
                customer.getCustomerID());
        CustomerSettings customerSettings = new CustomerSettings();
        Gson gson = new Gson();
        if (customerPreferences != null) {
            customerSettings = gson.fromJson(customerPreferences.getValue(), CustomerSettings.class);
        }
        return customerSettings;
    }

    @Override
    public CustomerSettings updateCustomerPreferences(BusinessMessage businessMessage) throws CustomerException {
        Customer customer = getCustomer(businessMessage.getPrimarySenderInfo().getMsisdn(),
                businessMessage.getPrimarySenderInfo().getWalletShortCode());
        CustomerPreferences customerPreferences = this.baseDao.executeNamedQueryForSingleResult(
                "CustomerPreferences.getCustomerPreferences", CustomerPreferences.class, false, false,
                customer.getCustomerID());
        CustomerSettings customerSettings = businessMessage.getPrimarySenderInfo().getCustomerSettings();
        String customerSettingsJson = null;
        Gson gson = new Gson();
        if (customerSettings != null && customer != null) {
            customerSettingsJson = gson.toJson(customerSettings);
            if (customerPreferences != null) {
                customerPreferences.setValue(customerSettingsJson);
                customerPreferences.setCustomer(customer);
                baseDao.update(customerPreferences);
            } else {
                customerPreferences = new CustomerPreferences();
                customerPreferences.setValue(customerSettingsJson);
                customerPreferences.setCustomer(customer);
                baseDao.save(customerPreferences);
            }

        }

        return customerSettings;
    }

    @Override
    public void unlockBulkCustomerProfile(String userKey) {
        String query = ServiceQueryEngine.getQueryStringToExecute("unlockBulkCustomerProfile", this.getClass(), userKey);
        Query updateQuery = (Query) this.baseDao.executeNativeQuery(query, true);
    }

    @Override
    public BigDecimal checkBulkCutomerProfileStatus(String userKey) {
        String query = ServiceQueryEngine.getQueryStringToExecute("checkBulkCutomerProfileStatus", this.getClass(), userKey);
        BigDecimal status = new BigDecimal(0);
        Query profileQuery = (Query) this.baseDao.executeNativeQuery(query);
        List results = profileQuery.getResultList();
        if ((results != null) && (results.size() > 0)) {
            return (BigDecimal) results.get(0);
        }
        return status;
    }

    @Override
    public String getOtpMessage(BusinessMessage businessMessage) throws IOException, GeneralFailureException {
        String message = null;
        try {
            String defaultFileName = "messages_configuration.xml";
            String userHome = System.getProperty("user.home");
            String language = businessMessage.getPrimarySenderInfo().getLanguage() != null
                    ? businessMessage.getPrimarySenderInfo().getLanguage()
                    : DEFAULT_LANGUAGE;
            String messagesConfigurationFullPath = userHome + "/wallet_messages_configuration/" + language + "_"
                    + defaultFileName;
            HashMap<String, Object> parameters = new HashMap<String, Object>();
            String serviceCode = businessMessage.getServiceInfo().getCode();
            String msisdn = businessMessage.getPrimarySenderInfo().getMsisdn();
            String otp = createOTP(msisdn);
            String OTP_TIMEOUT = this.propertyLoader.loadProperty("OTP_TIMEOUT");
            parameters.put("language", language);
            parameters.put("Wallet_Info", businessMessage.getWalletInfo());
            parameters.put("Customer_Msisdn", msisdn);
            parameters.put("WALLET_SHORT_CODE", businessMessage.getPrimarySenderInfo().getWalletShortCode());
            parameters.put("Service_Code", serviceCode);
            parameters.put("otp", otp);
            parameters.put("OTP_TIMEOUT", OTP_TIMEOUT);
            InputStream inputStream = new FileInputStream(messagesConfigurationFullPath);
            servicesDocument = ServicesDocument.Factory.parse(inputStream);
            Service service = getService(servicesDocument, serviceCode);
            if (service != null) {
                Notification[] notifications = service.getNotificationArray();
                for (Notification notification : notifications) {
                    SMS sms = notification.getSms();
                    message = (String) MVEL.eval(sms.getMessage(), parameters);
                }
            }
        } catch (XmlException e) {
            e.printStackTrace();
        }
        return message;
    }

    private Service getService(ServicesDocument servicesDocument, String serviceId) {
        Service[] services = servicesDocument.getServices().getServiceArray();
        for (Service service : services) {
            if (service.getServiceId().equals(serviceId)) {
                return service;
            }
        }
        return null;
    }

    @Override
    public String createOTP(String msisdn) throws IOException, GeneralFailureException {
        TransactionOTPSession transactionOTPSession = baseDao.getEntityManager().find(TransactionOTPSession.class,
                msisdn);
        String otp;
        if (transactionOTPSession == null) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");
            otp = generateOTP(Integer.parseInt(otpLength));
            String encOtp = AESEncryption.encrypt(otp);
            transactionOTPSession = new TransactionOTPSession(msisdn, encOtp, new Date());
            transactionOTPSession.setUsed(false);
            baseDao.save(transactionOTPSession);
        } else if (isOtpExpired(transactionOTPSession) || transactionOTPSession.isUsed()) {
            String otpLength = this.propertyLoader.loadProperty("OTP_LENGTH");
            otp = generateOTP(Integer.parseInt(otpLength));
            String encOtp = AESEncryption.encrypt(otp);
            transactionOTPSession.setOtp(encOtp);
            transactionOTPSession.setUsed(false);
            transactionOTPSession.setCreationTime(new Date());
            baseDao.update(transactionOTPSession);

        } else {
            otp = AESEncryption.decrypt(transactionOTPSession.getOtp());
        }
        return otp;
    }

    @Override
    public void updateCustomerProfileTypeId(String msisdn, Long profileTypeId) {
        baseDao.executeNamedQuery("Customer.updateProfileTypeId", Customer.class, true, false, profileTypeId, msisdn);
    }

    @Override
    public Long getCustomerProfileTypeId(String msisdn) {
        Long customerProfileTypeId = baseDao.executeNamedQueryForSingleResult("Customer.getCutomerProfileType",
                Long.class, false, false, msisdn);
        return customerProfileTypeId;
    }

    @Override
    public void updateCustomerPromoProfileStatus(String status, Long customerId) {
        baseDao.executeNamedQuery("CustomerPromoProfile.updateCustomerPromoProfile", CustomerPromoProfile.class, true,
                false, status, customerId);
    }

    @Override
    public BusinessMessage getAccountsDailyRiskLimits(BusinessMessage message) {
        loadAllDailyRiskLimits();
        List<PaymentMethodDetail> paymentDetails = (List<PaymentMethodDetail>) message.getSoftFields()
                .get("niResponse_payments");
        Long customerProfileTypeId = (Long) message.getSoftFields().get("customerProfileTypeId");
        String walletShortCode = message.getWalletInfo().getWalletShortCode();
        Long group = null;
        if (customerProfileTypeId != null) {
            for (PaymentMethodDetail paymentMethodDetail : paymentDetails) {
                try {
                    String schemeCode = getPaymentMethodSchemeCode(paymentMethodDetail);
                    if (schemeCode != null) {
                        group = baseDao.executeNamedQueryForSingleResult(
                                "CustomerAccountTypeMapping.findGroupBySchemeCodeAndProfileId", Long.class, false, true,
                                schemeCode, customerProfileTypeId);
                        String groupKey = walletShortCode + "-" + group;
                        paymentMethodDetail.setRiskProfiles(maxDailyRiskLimits.get(groupKey).getRiskProfiles());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return message;
    }

    @Override
    public String getPaymentMethodSchemeCode(PaymentMethodDetail paymentMethodDetail) {
        if (paymentMethodDetail.getBank() != null && paymentMethodDetail.getBank().getAccountNumber() != null) {
            return paymentMethodDetail.getBank().getSchemeCode();
        } else if (paymentMethodDetail.getCard() != null && paymentMethodDetail.getCard().getCardNumber() != null) {
            return paymentMethodDetail.getCard().getSchemeCode();
        }

        return null;
    }

    private void loadAllDailyRiskLimits() {
        if (maxDailyRiskLimits == null) {
            maxDailyRiskLimits = new HashMap<String, UsersRiskProfile>();
            String sb = ServiceQueryEngine.getQueryStringToExecute("loadAllDailyRiskLimits", this.getClass());

            Query query = (Query) this.baseDao.executeNativeQuery(sb);
            List results = query.getResultList();
            for (Object raw : results) {
                Object[] obj = (Object[]) raw;
                String key = (String) obj[0];
                BigDecimal riskLimit = (BigDecimal) obj[1];
                String profileCode = (String) obj[2];
                String profileName = (String) obj[3];
                BigDecimal customerRegType = (BigDecimal) obj[4];
                Enums.CustomerAuthenticationTypeEnum authType = mapCustomerProfileTypeToAuthenticationType(
                        customerRegType.intValue());
                if (authType != null) {
                    if (maxDailyRiskLimits.containsKey(key)) {
                        UsersRiskProfile usersRiskProfile = maxDailyRiskLimits.get(key);
                        RiskProfile riskProfile = new RiskProfile();
                        riskProfile.setMaxRiskLimit(riskLimit);
                        riskProfile.setKey(authType.name());
                        riskProfile.setRiskProfileName(profileName);
                        riskProfile.setID(authType.getAuthenticationType());
                        usersRiskProfile.getRiskProfiles().add(riskProfile);
                    } else {
                        UsersRiskProfile usersRiskProfile = new UsersRiskProfile();
                        RiskProfile riskProfile = new RiskProfile();
                        riskProfile.setMaxRiskLimit(riskLimit);
                        riskProfile.setKey(authType.name());
                        riskProfile.setRiskProfileName(profileName);
                        riskProfile.setID(authType.getAuthenticationType());
                        usersRiskProfile.getRiskProfiles().add(riskProfile);
                        maxDailyRiskLimits.put(key, usersRiskProfile);
                    }
                }
            }
        }
    }

    public Enums.CustomerAuthenticationTypeEnum mapCustomerProfileTypeToAuthenticationType(int customerProfileType) {

        Enums.CustomerAuthenticationTypeEnum authenticationType = null;

        switch (customerProfileType) {
            case 1:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.TOKEN;
                break;
            case 2:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.PIN;
                break;
            case 5:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.OTP;
                break;
            case 7:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.BIOMETRIC;
                break;
            case 9:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.FACE_RECOGNITION;
                break;
            case 10:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.NO_AUTHENTICATION_NO_LIMIT;
                break;
            case 11:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.PIN_OTP_FINANCIAL;
                break;
            case 12:
                authenticationType = Enums.CustomerAuthenticationTypeEnum.NO_AUTHENTICATION_FOR_BREFERENTIAL_BENFICIARY;
                break;
            default:
                break;
        }
        return authenticationType;

    }

    @Override
    public void RefreshMaxDailyRiskLimitsData() {
        maxDailyRiskLimits = null;
    }

    @Override
    public RiskProfile getAccountMaxDailyRiskLimit(String schemeCode, Long customerProfileTypeId,
                                                   String walletShortCode, CustomerProfileType customerProfileType) {
        RiskProfile profile = new RiskProfile();
        Long group = null;


        group = baseDao.executeNamedQueryForSingleResult(
                "CustomerAccountTypeMapping.findGroupBySchemeCodeAndProfileId", Long.class, false, true, schemeCode,
                customerProfileTypeId);
        String sb = ServiceQueryEngine.getQueryStringToExecute("getAccountMaxDailyRiskLimit", this.getClass(), String.valueOf(customerProfileType.getProfileType()), walletShortCode, String.valueOf(group));
        Query query = (Query) this.baseDao.executeNativeQuery(sb);
        List<Object[]> rows = query.getResultList();
        for (Object[] row : rows) {
            profile.setMaxRiskLimit((BigDecimal) row[0]);
            profile.setRiskProfileName((String) row[1]);
        }
        List results = query.getResultList();

        return profile;
    }


    @Override
    public RiskProfile getAccountMaxDailyRiskLimit(Long customerId, String walletShortCode) {

        RiskProfile profile = new RiskProfile();
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("getAccountMaxDailyRiskLimit_customerId", this.getClass(),
                String.valueOf(customerId), walletShortCode);
        Query query = (Query) this.baseDao.executeNativeQuery(queryStr);
        List<Object[]> rows = query.getResultList();
        for (Object[] row : rows) {
            profile.setMaxRiskLimit((BigDecimal) row[0]);
            profile.setRiskProfileName((String) row[1]);
        }
        List results = query.getResultList();

        return profile;

    }

    @Override
    public Long getPaymentMethodAccountId(PaymentMethodDetail paymentMethodDetail) {
        if (paymentMethodDetail.getBank() != null && paymentMethodDetail.getBank().getAccountNumber() != null) {
            return Long.parseLong(paymentMethodDetail.getBank().getAccountNumber());
        } else if (paymentMethodDetail.getCard() != null && paymentMethodDetail.getCard().getCardNumber() != null
                && !StringUtils.isEmpty(paymentMethodDetail.getCard().getCardNumber())) {
            return Long.parseLong(paymentMethodDetail.getCard().getCardNumber());
        }
        return null;
    }

    @Override
    public boolean isBvnUsedByAnotherUser(String BVN) {
        Long bvnNo = baseDao.executeNamedQueryForSingleResult("Customer.checkBVNExistance", Long.class, false, false,
                BVN);
        if (bvnNo != null && bvnNo > 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isSmeCustomer(long customerId) {
        BigDecimal smeAccountsCount = new BigDecimal(0);
        String sb = ServiceQueryEngine.getQueryStringToExecute("isSmeCustomer", this.getClass(), String.valueOf(SchemeCodeEnum.SME.getSchemeCode()), String.valueOf(customerId));
        Query query = (Query) this.baseDao.executeNativeQuery(sb);
        List results = query.getResultList();
        smeAccountsCount = (BigDecimal) results.get(0);
        if (smeAccountsCount != null && smeAccountsCount.compareTo(BigDecimal.ZERO) > 0) {
            return true;
        }
        return false;
    }

    @Override
    public BigDecimal getIndemnityMaxRiskLimit(String walletShortCode, CustomerGroupEnum customerGroup) {
        BigDecimal maxLimit = new BigDecimal(0);

        String sb = ServiceQueryEngine.getQueryStringToExecute("getIndemnityMaxRiskLimit", this.getClass(), String.valueOf(CustomerAuthenticationTypeEnum.TOKEN.getAuthenticationType()), walletShortCode, String.valueOf(customerGroup.getCustomerGroup()));
        Query query = (Query) this.baseDao.executeNativeQuery(sb);
        List results = query.getResultList();
        maxLimit = (BigDecimal) results.get(0);
        return maxLimit;
    }

    @Override
    public BigDecimal getConsumedAmountForAccount(Long customerId, Long paymentMethodId) {
        String userID = paymentMethodId + "-1";
        SimpleDateFormat dt1 = new SimpleDateFormat("yyyy-MM-dd");
        String generalDay = userID + "-" + dt1.format(new Date());
        String Query1 = ServiceQueryEngine.getQueryStringToExecute("getConsumedAmountForAccount", this.getClass(), generalDay);
        Query query = (Query) this.baseDao.executeNativeQuery(Query1);
        BigDecimal D_Sum = BigDecimal.ZERO;
        List results = query.getResultList();
        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            Object[] r = (Object[]) result;
            if ((r[0] == null) || (!D_Sum.equals(BigDecimal.ZERO))) {
                continue;
            }
            D_Sum = (BigDecimal) r[0];
            break;
        }
        return D_Sum;
    }

    @Override
    public Long getSvaAccountId(String userKey) {
        return this.baseDao.executeNamedQueryForSingleResult("CustomerGar.getCustomerSVAaccountByUserKey", Long.class,
                false, true, new Object[]{userKey});
    }

    @Override
    public BigDecimal getRegularConsumedAmount(BusinessMessage businessMessage) {
        Long accountID = this.baseDao.executeNamedQueryForSingleResult("CustomerGar.getCustomerSVAaccountByUserKey",
                Long.class, false, true, new Object[]{businessMessage.getPrimarySenderInfo().getUserKey()});
        String userID = accountID + "-1";
        SimpleDateFormat dt1 = new SimpleDateFormat("yyyy-MM-dd");
        String generalDay = userID + "-" + dt1.format(new Date());
        String Query1 = ServiceQueryEngine.getQueryStringToExecute("getRegularConsumedAmount", this.getClass(), generalDay, String.valueOf(SchemeCodeEnum.REGULAR));
        Query query = (Query) this.baseDao.executeNativeQuery(Query1);
        BigDecimal D_Sum = BigDecimal.ZERO;
        List results = query.getResultList();
        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            Object[] r = (Object[]) result;
            if ((r[0] == null) || (!D_Sum.equals(BigDecimal.ZERO))) {
                continue;
            }
            D_Sum = (BigDecimal) r[0];
            break;
        }
        return D_Sum;
    }

    @Override
    public BigDecimal getSmeConsumedAmount(BusinessMessage businessMessage) {
        Long accountID = this.baseDao.executeNamedQueryForSingleResult("CustomerGar.getCustomerSVAaccountByUserKey",
                Long.class, false, true, new Object[]{businessMessage.getPrimarySenderInfo().getUserKey()});
        String userID = accountID + "-1";
        SimpleDateFormat dt1 = new SimpleDateFormat("yyyy-MM-dd");
        String generalDay = userID + "-" + dt1.format(new Date());
        String Query1 = ServiceQueryEngine.getQueryStringToExecute("getSmeConsumedAmount", this.getClass(), generalDay, String.valueOf(SchemeCodeEnum.SME));
        Query query = (Query) this.baseDao.executeNativeQuery(Query1);
        BigDecimal D_Sum = BigDecimal.ZERO;
        List results = query.getResultList();
        for (Iterator localIterator = results.iterator(); localIterator.hasNext(); ) {
            Object result = localIterator.next();
            Object[] r = (Object[]) result;
            if ((r[0] == null) || (!D_Sum.equals(BigDecimal.ZERO))) {
                continue;
            }
            D_Sum = (BigDecimal) r[0];
            break;
        }
        return D_Sum;
    }

    @Override
    public BigDecimal getProfileTotalFee(Long feeProfileId, BusinessMessage businessMessage) {
        ServiceConfigMap serviceType = baseDao.findById(ServiceConfigMap.class,
                Long.valueOf(businessMessage.getServiceInfo().getServiceTypeForFee()));

        List<FeeValueModel> feeValues = baseDao.executeDynamicQuery(
                "select feeValueModel from FeeProfile p inner join p.feeValues feeValueModel where p.id = "
                        + feeProfileId + "and feeValueModel.feeModel.businessServiceType.id =  " + serviceType.getId(),
                FeeValueModel.class, false, false);
        BigDecimal totalFee;
        Double feeAndVatSum = null;
        if (feeValues != null && feeValues.size() > 0) {
            FeeValueModel feeValueModel = feeValues.get(0);
            Double feeAmount = Double.parseDouble(feeValueModel.getValue());
            feeAndVatSum = feeAmount;
            if (feeValueModel.getVatPercent() != null)
                feeAndVatSum += ((feeValueModel.getVatPercent() * feeAmount) / 100);
        }
        totalFee = new BigDecimal(feeAndVatSum);
        return totalFee;
    }

    @Override
    public void updateCustomerPromoProfileDefaultFee(Long customerId, Long newFeeProfileId) {
        baseDao.executeNamedQuery("CustomerPromoProfile.updateCustomerPromoProfileDefaultFee",
                CustomerPromoProfile.class, true, false, newFeeProfileId, customerId);
    }

    @Override
    public String getCustomerEmail(String msisdn) {
        String customerEmail = null;
        customerEmail = baseDao.executeNamedQueryForSingleResult("Customer.getCustomerEmailByMsisdn", String.class,
                false, false, msisdn);
        return customerEmail;
    }

    @Override
    public Long createOrUpdateCustomerSession(String identificationKey, String swk) {
        String sb = ServiceQueryEngine.getQueryStringToExecute("createOrUpdateCustomerSession", this.getClass(), identificationKey);
        baseDao.executeNativeQuery(sb, true);
        CustomerSession session = new CustomerSession();
        session.setIdentificationKey(identificationKey);
        session.setSwk(swk);
        session.setCreationDate(new Date());
        session = (CustomerSession) baseDao.saveEntity(session);
        return session.getId();
    }

    @Override
    public String getSessionSwkById(Long sessionId) throws GeneralFailureException {
        String swk = null;
        String stringQuery = ServiceQueryEngine.getQueryStringToExecute("getSessionSwkById", this.getClass(), String.valueOf(sessionId));
        Query query = null;
        try {
            query = (Query) baseDao.executeNativeQuery(stringQuery.toString());
            List<Object> rows = query.getResultList();
            if (rows != null && rows.size() > 0) {
                swk = (String) rows.get(0);
            } else {
                throw new GeneralFailureException(GeneralFailureException.INVALID_SESSION_ID);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GeneralFailureException(GeneralFailureException.INVALID_SESSION_ID);
        }
        return swk;
    }

    @Override
    public BusinessUser checkAgentHealthy(String msisdn) throws Exception {
        BusinessUser businessUser = null;

        Query query = this.baseDao.getEntityManager()
                .createQuery("select model from BusinessUser model where model.mobilePhone=:msisdn");
        query.setParameter("msisdn", msisdn);

        query.setMaxResults(1);
        try {
            businessUser = (BusinessUser) query.getSingleResult();
        } catch (Exception localException) {
        }

        return businessUser;
    }

    @Override
    public void updateCustomerPassword(String msisdn, String password, String newPassword) {

        try {
            Customer customer = getCustomer(msisdn);
            if (customer != null) {
                customer.setHandsetPassword(newPassword);
                updateCustomer(customer);
            }

        } catch (NumberFormatException e) {
            e.printStackTrace();
        } catch (CustomerException e) {
            e.printStackTrace();
        }

    }

    @Override
    public void updateCustomerInfo(BusinessMessage message) throws Exception {
        try {
            Customer customer = new Customer();
            String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();
            String msisdn = message.getPrimaryReceiverInfo().getMsisdn();
            String firstName = message.getPrimaryReceiverInfo().getPersonalDetails().getFirstName();
            String lastName = message.getPrimaryReceiverInfo().getPersonalDetails().getLastName();
            String middleName = message.getPrimaryReceiverInfo().getPersonalDetails().getMiddleName();
            Gender genderType = message.getPrimaryReceiverInfo().getPersonalDetails().getGender();
            String countryISO = message.getPrimaryReceiverInfo().getPersonalDetails().getCountryId();
            String cityName = message.getPrimaryReceiverInfo().getPersonalDetails().getCityName();

            String IDType = message.getPrimaryReceiverInfo().getPersonalDetails().getIdentifierType();
            String IDNumber = message.getPrimaryReceiverInfo().getPersonalDetails().getIdentifier();

            String street = message.getPrimaryReceiverInfo().getPersonalDetails().getStreet();
            Long subscriberCategory = message.getPrimaryReceiverInfo().getPersonalDetails()
                    .getCutomerTypeSubsubscriberCategory();
            String NationalityID = message.getPrimaryReceiverInfo().getPersonalDetails().getNationality();
            String email = message.getPrimaryReceiverInfo().getPersonalDetails().getEmail();

            if (StringUtils.isBlank(firstName)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_FIRSTNAME);
            }
            if (StringUtils.isBlank(lastName)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_LASTTNAME);
            }
            if (genderType == null) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_GENDER);
            }
            if (StringUtils.isBlank(countryISO)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_COUNTRYISO);
            }
            if (StringUtils.isBlank(cityName)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_CITYNAME);
            }
            if (StringUtils.isBlank(IDType)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_IDTYPE);
            }
            if (StringUtils.isBlank(IDNumber)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_IDNUMBER);
            }
            if (StringUtils.isBlank(NationalityID)) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_NATIONALITYID);
            }
            if (subscriberCategory == null) {
                throw new CustomerException(CustomerException.INVALID_REQUEST_REQUIRED_SUBSCRIBER_CATEGORY);
            }

            if (!IDType.matches("[0-9]+")) {
                throw new CustomerException(CustomerException.CUSTOMER_IDTYPE_ERROR_FORMAT);
            }
            if (!NationalityID.matches("[0-9]+")) {
                throw new CustomerException(CustomerException.CUSTOMER_NATIONALITYID_FORMAT);
            }

            if (msisdn == null) {
                throw new CustomerException(CustomerException.MSISDN_IS_NULL_ERROR);
            }
            customer = getCustomerByMsisdnAndWalletShortCode(msisdn, walletShortCode);
            Address address = customer.getPermanentAddress();
            Integer gender = genderType.ordinal();
            if (firstName.trim().equals(customer.getFirstName()) && lastName.trim().equals(customer.getLastName())
                    && middleName.trim().equals(customer.getMiddleName()) && gender == (customer.getGender().ordinal())
                    && countryISO.trim().equals(customer.getCountry().getIsoCode())
                    && cityName.trim().equals(customer.getPermanentAddress().getCity())
                    && Long.parseLong(IDType) == (customer.getUserPersonalIDType().getUserIdentifiactionTypeID())
                    && IDNumber.trim().equals(customer.getUserPersonalIDNumber())
                    && street.trim().equals(customer.getPermanentAddress().getStreet())
                    && subscriberCategory.equals((customer.getCustomerType()))
                    && Long.parseLong(NationalityID) == (customer.getNationality().getId())
                    && email.trim().equals(customer.getEmail())) {
                throw new CustomerException(CustomerException.NO_CHNAGES_FOUND_TO_UPDATE_CUSTOMER);
            }

            if (street != null && !street.isEmpty()) {
                address.setStreet(street);
            }

            if (middleName != null && !middleName.isEmpty()) {
                customer.setMiddleName(middleName);
            }

            if (email != null && !email.isEmpty()) {
                customer.setEmail(email);
            }
            CustomerType customerType = loadCustomerSubscriberCategory(walletShortCode, subscriberCategory);
            customer.setCustomerType(customerType.getId());
            customer.setFeeProfile(customerType.getDefaultFeeId());
            customer.setRiskProfile(customerType.getDefaultRiskId());

            customer.setFirstName(firstName);
            customer.setLastName(lastName);
            customer.setGender(GenderEnum.values()[gender]);
            customer.setCountry(loadCustomerCountry(countryISO));

            address.setCity(cityName);
            customer.setPermanentAddress(address);
            customer.setUserPersonalIDType(loadUserIdTypes(Long.parseLong(IDType)));
            customer.setUserPersonalIDNumber(IDNumber);
            customer.setIdentificationId(IDNumber);
            customer.setNationality(loadCustomerNationality(Long.parseLong(NationalityID)));
            customer.setLastModifiedDate(new Date());

            baseDao.update(customer);

        } catch (SQLException sql) {
            sql.printStackTrace();
            throw new GeneralFailureException(GeneralFailureException.GENERAL_ERROR);
        } catch (NumberFormatException sql) {
            sql.printStackTrace();
            throw new GeneralFailureException(GeneralFailureException.GENERAL_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            throw (e);
        }
    }

    @Override
    public void saveCharger(Charger charger) throws CustomerException {

        this.baseDao.save(charger);

    }

    @Override
    public Boolean checkIfCustomerExist(String msisdn) {
        Boolean isExist = false;
        Customer customer = null;
        Query query = this.baseDao.getEntityManager()
                .createQuery("select model from Customer model where model.msisdn=:msisdn");
        query.setParameter("msisdn", msisdn);

        query.setMaxResults(1);
        try {
            customer = (Customer) query.getSingleResult();
            if (customer != null)
                isExist = true;
        } catch (Exception localException) {
            localException.printStackTrace();
        }

        return isExist;
    }

    private Country loadCustomerCountry(String countryISO) throws Exception {
        Country country = null;
        EntityManager em = baseDao.getEntityManager();
        StringBuilder queryStr = new StringBuilder("Select c From Country c Where c.isoCode='" + countryISO + "'");
        Query query = em.createQuery(queryStr.toString());

        List<Country> listCountry = query.getResultList();
        if (listCountry == null || listCountry.isEmpty()) {
            throw new CustomerException(CustomerException.CUSTOMER_COUNTRY_NOT_FOUND_ERROR);
        }
        country = listCountry.get(0);
        return country;
    }

    private UserIdentificationType loadUserIdTypes(Long userIdTypeID) throws Exception {
        UserIdentificationType userIdType = null;
        EntityManager em = baseDao.getEntityManager();
        StringBuilder queryStr = new StringBuilder(
                "Select u From UserIdentificationType u Where u.userIdentifiactionTypeID=" + userIdTypeID);
        Query query = em.createQuery(queryStr.toString());

        List<UserIdentificationType> listUserIdTypes = query.getResultList();
        if (listUserIdTypes == null || listUserIdTypes.isEmpty() || listUserIdTypes.size() < 0) {
            throw new CustomerException(CustomerException.CUSTOMER_USER_TYPE_ID_NOT_FOUND_ERROR);
        }
        userIdType = listUserIdTypes.get(0);
        return userIdType;
    }

    private DocumentType loadDocumentType(Long idType) throws Exception {
        DocumentType documentType = null;
        EntityManager em = baseDao.getEntityManager();
        StringBuilder queryStr = new StringBuilder("Select d From DocumentType d Where d.id=" + idType);
        Query query = em.createQuery(queryStr.toString());

        List<DocumentType> listDocumentTypes = query.getResultList();
        if (listDocumentTypes == null || listDocumentTypes.isEmpty() || listDocumentTypes.size() < 0) {
            throw new CustomerException(CustomerException.CUSTOMER_USER_TYPE_ID_NOT_FOUND_ERROR);
        }
        documentType = listDocumentTypes.get(0);
        return documentType;
    }

    @Override
    public Nationality loadCustomerNationality(Long nationalityId) throws Exception {
        Nationality nationality = null;
        EntityManager em = baseDao.getEntityManager();
        StringBuilder queryStr = new StringBuilder("Select n From Nationality n Where n.id=" + nationalityId);
        Query query = em.createQuery(queryStr.toString());

        List<Nationality> listNationality = query.getResultList();
        if (listNationality == null || listNationality.isEmpty()) {
            throw new CustomerException(CustomerException.CUSTOMER_NATIONALITY_ID_NOT_FOUND_ERROR);
        }
        nationality = listNationality.get(0);
        return nationality;
    }

    public Customer getCustomerByMsisdnAndWalletShortCode(String msisdn, String walletShortCode)
            throws CustomerException {
        Customer customer = null;
        EntityManager em = baseDao.getEntityManager();
        StringBuilder queryStr = new StringBuilder("Select c From Customer c Where c.msisdn='" + msisdn + "'"
                + "and c.walletShortCode='" + walletShortCode + "'");
        Query query = em.createQuery(queryStr.toString());

        List<Customer> listCustomers = query.getResultList();
        if (listCustomers == null || listCustomers.isEmpty()) {
            throw new CustomerException(CustomerException.CUSTOMER_NOT_FOUND_ERROR);
        }
        customer = listCustomers.get(0);
        return customer;
    }

    public Boolean isValidCustomerSubscriberCategory(String walletShortCode, Long subscriberCategory)
            throws CustomerException {
        boolean valid = false;
        EntityManager em = baseDao.getEntityManager();
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("isValidCustomerSubscriberCategory", this.getClass(), walletShortCode, String.valueOf(subscriberCategory));
        Query query = (Query) getBaseDao().executeNativeQuery(queryStr);
        List results = query.getResultList();

        if ((results != null) && (results.size() > 0)) {
            valid = true;
        }
        return valid;
    }

    public CustomerType loadCustomerSubscriberCategory(String walletShortCode, Long subscriberCategory)
            throws CustomerException {
        CustomerType customerType = null;
        EntityManager em = baseDao.getEntityManager();
        StringBuilder queryStr = new StringBuilder("SELECT ct " + "from CustomerType ct " + "where ct.wallet in "
                + "(SELECT bec.businessEntityID " + "from BusinessEntity bec " + "where bec.shortCode='"
                + walletShortCode + "') " + "and ct.id=" + subscriberCategory);

        Query query = em.createQuery(queryStr.toString());
        List<CustomerType> listCustomers = query.getResultList();
        if (listCustomers == null || listCustomers.isEmpty()) {
            throw new CustomerException(CustomerException.CUSTOMER_SUBSCRIBER_CATEGORY_NOT_FOUND_ERROR);
        }
        customerType = listCustomers.get(0);
        return customerType;
    }

    @Override
    public void saveExternalReceiverData(BusinessMessage businessMessage) {
        ExternalReceiverData externalReceiverData = new ExternalReceiverData();
        Long transactionId = businessMessage.getTransactionInfo().getTransactionId();
        String receiverHolderName = businessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                .getHolderName();
        String receiverAccountNumber = businessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                .getAccountNumber();
        String receiverInstitutionCode = businessMessage.getPrimaryReceiverInfo().getPaymentMethod().getBank()
                .getShortCode();

        externalReceiverData.setReceiverAccountNumber(receiverAccountNumber);
        externalReceiverData.setReceiverHolderName(receiverHolderName);
        externalReceiverData.setReceiverInstitutionCode(receiverInstitutionCode);
        externalReceiverData.setTransactionSummaryId(transactionId);
        baseDao.save(externalReceiverData);
    }

    public Customer getCustomerWithFetchDocuments(String userId) throws CustomerException {

        Customer customer = getCustomer(userId);
        if (customer != null && customer.getDocuments() != null) {
            customer.getDocuments().size();
        }
        return customer;

    }

    public Customer findCustomerByMsisdnWithFetchDocuments(String msisdn, String walletShortCode)
            throws CustomerException {
        Customer customer = findCustomerByMsisdn(msisdn, walletShortCode);
        if (customer != null && customer.getDocuments() != null) {
            customer.getDocuments().size();
        }
        return customer;
    }

    @Override
    public void updateselfRegisteredCustomerStatus(String msisdn, String walletShortCode) throws CustomerException {
        Customer customer = null;
        String self_Registered_Status_Key = "self-registration_" + walletShortCode + "_status";
        Integer status = getSelfRegisteredStatus(self_Registered_Status_Key);
        if (status != null) {
            customer = findCustomerByMsisdn(msisdn, walletShortCode);
            customer.setStatus(UserStatus.values()[status]);
            this.baseDao.update(customer);
        } else {
            throw new CustomerException(CustomerException.INVALID_WALLETSHORTCODE);
        }
    }

    public Integer getSelfRegisteredStatus(String key) {
        Integer status = null;
        GeneralLookups generalLookups = null;
        Query query = this.baseDao.getEntityManager()
                .createQuery("select model from GeneralLookups model where model.key=:key");
        query.setParameter("key", key);

        query.setMaxResults(1);
        try {
            generalLookups = (GeneralLookups) query.getSingleResult();
            if (generalLookups != null)
                status = Integer.parseInt(generalLookups.getValue());
        } catch (Exception localException) {
            localException.printStackTrace();
        }

        return status;
    }

    public String getPinRegex(String key) {
        String reg = null;
        GeneralLookups generalLookups = null;
        Query query = this.baseDao.getEntityManager()
                .createQuery("select model from GeneralLookups model where model.key=:key");
        query.setParameter("key", key);

        query.setMaxResults(1);
        try {
            generalLookups = (GeneralLookups) query.getSingleResult();
            if (generalLookups != null)
                reg = generalLookups.getValue();
        } catch (Exception localException) {
            localException.printStackTrace();
        }

        return reg;
    }

    @Override
    public void updateCustomerPasswordWithMaxTrials(BusinessMessage message)
            throws GeneralFailureException, IOException {
        String userKey = message.getPrimarySenderInfo().getUserKey();
        String oldPassword = hashPassword(message.getPrimarySenderInfo().getPassword());
        String newPassword = hashPassword(message.getPrimarySenderInfo().getNewPassword());
        String congirmPassword = hashPassword(message.getPrimarySenderInfo().getConfirmPassword());
        Customer customer = getCustomer(message.getPrimarySenderInfo().getCustomerId());
        String serviceCode = message.getServiceInfo().getCode();
        Long customerId = customer.getCustomerID();
        String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();
        boolean verified = false;
        Integer MAXCHARSLENGTH = 6;

        String check = hashPassword(customer.getHandsetPassword());

        if (!newPassword.equals(congirmPassword)) {
            throw new CustomerException(CustomerException.PASSWORD_AND_CONFIRM_PASSWORD_DO_NOT_MATCH);
        }
        if (customer == null) {
            throw new CustomerException(CustomerException.CUSTOMER_NOT_FOUND_ERROR);
        }
        if (StringUtils.isBlank(newPassword)) {
            throw new CustomerException(CustomerException.FRIST_SET_NEW_PASSWORD);
        }
        if (StringUtils.isBlank(oldPassword)) {
            throw new CustomerException(CustomerException.FRIST_SET_OLD_PASSWORD);
        }
        if (newPassword.equals(oldPassword)) {
            throw new CustomerException(CustomerException.NEW_PASSWORD_SAME_AS_ACTUALY_PASSWORD);
        }
        securityRulesManager.checkOldPasswordAndBlockCustomer(message, customer);

        securityRulesManager.applaySecurityRules(customerId, message.getPrimarySenderInfo().getNewPassword(),
                newPassword, walletShortCode, DEFAULPASSWORDPATTERNKEY, PASSWORDVALIDITYPERIOD, OLDPASSWORDTRACKED,
                serviceCode);
        updateCustomerPassword(userKey, oldPassword, newPassword);
    }


    @Override
    public void saveNewEmail(BusinessMessage message) throws GeneralFailureException {
        String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();
        String userKey = message.getPrimarySenderInfo().getUserKey();
        String newEmail = (String) message.getSoftFields().get("newEmail");
        Customer customer = getCustomer(message.getPrimarySenderInfo().getCustomerId());
        String emailRegex = "^([0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\\w]*[0-9a-zA-Z]\\.)+[a-zA-Z]{2,9})$";


        if (newEmail.equals(customer.getEmail()))
            throw new CustomerException(CustomerException.EMAIL_SAME_AS_OLD);

        if (!checkRegex(newEmail, emailRegex))
            throw new CustomerException(CustomerException.EMAIL_FORMAT_NOT_VALID);

        List<Customer> customerByEmail =
                (List<Customer>) baseDao.executeNamedQuery("Customer.findByEmail", Customer.class, false, false, newEmail);

        if (!customerByEmail.isEmpty())
            throw new CustomerException(CustomerException.EMAIL_ALREADY_REGISTERED);

        updateEmail(userKey, newEmail, walletShortCode);
    }

    public boolean checkRegex(String input, String regex) {

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        boolean match = matcher.matches();
        return match;
    }

    @NonNull
    @Qualifier("securityRulesManager")
    SecurityRulesManager securityRulesManagerLocal;

    @Override
    public void saveNewPIN(BusinessMessage message) throws GeneralFailureException, IOException {
        String oldPinNotHashed = message.getPrimarySenderInfo().getPin();
        String newEnteredPinNotHashed = message.getPrimarySenderInfo().getNewPin();
        String userKey = message.getPrimarySenderInfo().getUserKey();
        String oldPin = hashPassword(message.getPrimarySenderInfo().getPin());
        String newEnteredPin = hashPassword(message.getPrimarySenderInfo().getNewPin());
        String confirmPin = hashPassword(message.getPrimarySenderInfo().getConfirmPin());
        String newEnteredPinNoHashed = message.getPrimarySenderInfo().getNewPin();

        securityRulesManagerLocal.pinNotReptitiveSeqential(newEnteredPinNoHashed);
        Customer customer = getCustomer(message.getPrimarySenderInfo().getCustomerId());
        String serviceCode = message.getServiceInfo().getCode();
        Long customerId = customer.getCustomerID();
        String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();
        boolean verified = false;
//		String pinRegexKey = "PIN_" + walletShortCode;

        if (!oldPinNotHashed.matches("[0-9]+")
                || !newEnteredPinNotHashed.matches("[0-9]+")) {
            throw new CustomerException(CustomerException.PIN_IS_NOT_VALID);
        }

        if (!newEnteredPin.equals(confirmPin)) {
            throw new CustomerException(CustomerException.PIN_AND_CONFIRM_PIN_DO_NOT_MATCH);
        }

        if (customer == null) {
            throw new CustomerException(CustomerException.CUSTOMER_NOT_FOUND_ERROR);
        }
        if (StringUtils.isBlank(newEnteredPin)) {
            throw new CustomerException(CustomerException.FRIST_SET_NEW_PIN);
        }
        if (StringUtils.isBlank(oldPin)) {
            throw new CustomerException(CustomerException.FRIST_SET_OLD_PIN);
        }
        if (newEnteredPin.equals(oldPin)) {
            throw new CustomerException(CustomerException.INVALID_PIN_MUST_BE_NEW_ONE);
        }

//		String pinRegex = getPinRegex(pinRegexKey);
//		if (pinRegex != null) {
//			Pattern pattern = Pattern.compile(pinRegex);
//			Matcher matcher = pattern.matcher(message.getPrimarySenderInfo().getNewPin());
//			if (!matcher.matches()) {
//				throw new BusinessUserException(BusinessUserException.NEW_PIN_IS_NOT_VALID);
//			}
//		}

        String pinRegexKey = "pin.length";
        int pinLength = Integer.parseInt(getPinRegex(pinRegexKey));
        if (newEnteredPinNoHashed.length() != pinLength)
            throw new BusinessUserException(BusinessUserException.NEW_PIN_IS_NOT_VALID);

        verified = oldPin.equals(customer.getPIN());
        if (!verified) {
            boolean isExceededTrials = limitExceededRetrialsManager.addCounter(customerId, Long.parseLong(serviceCode),
                    walletShortCode);
            if (isExceededTrials) {
                securityRulesManager.lockCustomer(message, customer);
                limitExceededRetrialsManager.resetCounter(customerId, Long.parseLong(serviceCode));
                throw new CustomerException(CustomerException.YOU_EXCEED_NUMBER_OF_RETRIALS);
            } else {
                throw new CustomerException(CustomerException.INVALID_PIN_MUST_MATCHE_OLD_ONE);
            }
        }
        limitExceededRetrialsManager.resetCounter(customerId, Long.parseLong(serviceCode));
        setPin(userKey, message.getPrimarySenderInfo().getNewPin());

    }


    public void setCustomerPassword(BusinessMessage businessMessage) throws GeneralFailureException, IOException {
        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
        Long customerId = businessMessage.getPrimarySenderInfo().getCustomerId();
        String serviceCode = businessMessage.getServiceInfo().getCode();
        String walletShortCode = businessMessage.getPrimarySenderInfo().getWalletShortCode();
        String newPassword = businessMessage.getPrimarySenderInfo().getNewPassword();
        String confirmPassword = businessMessage.getPrimarySenderInfo().getConfirmPassword();
        String hashedPassword = hashPassword(newPassword);

        if (newPassword == null || customerId == null) {
            throw new CustomerException(CustomerException.CUSTOMERID_OR_PASSWORD_IS_NULL);
        }
        Customer customer = getCustomer(customerId);
        if (customer == null) {
            throw new CustomerException(CustomerException.CUSTOMER_DOESNOT_EXIST);
        }
        UserStatus status = customer.getStatus();
        HashMap<String, String> vars = new HashMap<String, String>();

        if (status.ordinal() == UserStatus.LOCKED.ordinal())
            throw new CustomerException(CustomerException.BLOCKED_USER);


        if (!newPassword.equals(confirmPassword)) {
            throw new CustomerException(CustomerException.PASSWORD_AND_CONFIRM_PASSWORD_DO_NOT_MATCH);
        }
        if (StringUtils.isBlank(newPassword)) {
            throw new CustomerException(CustomerException.FRIST_SET_NEW_PASSWORD);
        }
        if (StringUtils.isBlank(newPassword)) {
            throw new CustomerException(CustomerException.FRIST_SET_OLD_PASSWORD);
        }
        if (hashedPassword.equals(customer.getHandsetPassword())) {
            throw new CustomerException(CustomerException.NEW_PASSWORD_SAME_AS_ACTUALY_PASSWORD);
        }
//		securityRulesManager.checkOldPasswordAndBlockCustomer(businessMessage, customer);

        securityRulesManager.applaySecurityRules(customerId, newPassword, hashedPassword, walletShortCode,
                DEFAULPASSWORDPATTERNKEY, PASSWORDVALIDITYPERIOD, OLDPASSWORDTRACKED, serviceCode);

        String query = ServiceQueryEngine.getQueryStringToExecute("setCustomerPassword", this.getClass(), String.valueOf(customerId));
        this.baseDao.executeNativeQuery(query, true);
        updateCustomerPassword(userKey, newPassword, hashedPassword);
    }

    @Override
    public void changePasswordAndValidateOtp(BusinessMessage businessMessage) throws CustomerRollBackException {
        try {
            setCustomerPassword(businessMessage);
        } catch (Exception e) {
            if (e instanceof GeneralFailureException) {
                throw new CustomerRollBackException(((GeneralFailureException) e).getErrorCode());
            }
            throw new CustomerRollBackException(e.getMessage());
        }
    }

    @Override
    public Object authenticateSubscriberByOTP(BusinessMessage message) throws GeneralFailureException, IOException {
        String serviceCode = message.getServiceInfo().getCode();
        String msisdn = message.getPrimarySenderInfo().getMsisdn();
        if (message.getPrimarySenderInfo().getAuthenticationType() != CustomerAuthenticationTypeEnum.OTP
                .getAuthenticationType()) {
            throw new CustomerException(CustomerException.INVALID_AUTHENTICATION_TYPE_ERROR);
        }
        if (message.getPrimarySenderInfo().getAuthenticationType() == CustomerAuthenticationTypeEnum.OTP
                .getAuthenticationType()
                || message.getPrimarySenderInfo().getAuthenticationType() == CustomerAuthenticationTypeEnum.OTP_PIN
                .getAuthenticationType()
                || message.getPrimarySenderInfo()
                .getAuthenticationType() == CustomerAuthenticationTypeEnum.PIN_OTP_FINANCIAL
                .getAuthenticationType()
                || message.getPrimarySenderInfo()
                .getAuthenticationType() == CustomerAuthenticationTypeEnum.PIN_OTP_SECURITY_QUESTION
                .getAuthenticationType()) {
            String otp = message.getPrimarySenderInfo().getOtp();
            boolean isValid = true;
            if (otp == null || (otp != null && otp.trim().equals(""))) {
                isValid = false;
            } else {
                isValid = isValidOTPSubscriber(msisdn, otp, null);

                if (!isValid) {
                    isValid = isValidOTPSubscriber(msisdn, otp, serviceCode);
                }

            }
            if (!isValid) {
                throw new CustomerException(CustomerException.INVALID_OTP);
            }
        }
        return message;
    }

    @Override
    @Transactional
    public BusinessMessage updateCustomerWithNewMSISDN(BusinessMessage businessMessage) throws UserIdException {
        Customer customer = null;
        String walletShortCode = businessMessage.getHeader().getWalletShortCode();
        try {
            customer = getCustomer(businessMessage.getPrimarySenderInfo().getCustomerId());
        } catch (CustomerException e) {
            e.printStackTrace();
        }
        customer.setMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());
        customer.setLastModifiedDate(new Date());
        String identificationKey = RegistrationUtils.generateIdentficationKeyFromFormatedMSISDN(walletShortCode, businessMessage.getPrimarySenderInfo().getMsisdn());
        customer.setIdentificationKey(identificationKey);
	/*	customer.setLastModifiedDate(new Date());
		WalletUserIdsType walletUserIdsType = getWalletUserIdsTypesByWalletShortCode(businessMessage);
		if (walletUserIdsType == null) {
			throw new UserIdException(UserIdException.Wallet_User_Id_Type_Not_Found);

		}
		if (walletUserIdsType.getCustomerUserIdsTypeID() == CustomerUserIdsTypeEnum.MSISDN) {
			customer.setUserId(businessMessage.getPrimarySenderInfo().getMsisdn());
		}*/

        this.baseDao.update(customer);

        return businessMessage;
    }

    @Override
    public WalletUserIdsType getWalletUserIdsTypesByWalletShortCode(BusinessMessage businessMessage) {
        String walletShortCode = businessMessage.getPrimarySenderInfo().getWalletShortCode();
        if (walletShortCode != null)
            walletShortCode = businessMessage.getHeader().getWalletShortCode();
        List<WalletUserIdsType> WalletUserIdsTypes = this.baseDao.findByProperty(WalletUserIdsType.class,
                "walletShortCode", walletShortCode, false);
        if (WalletUserIdsTypes != null && (WalletUserIdsTypes.size() > 0)) {
            return WalletUserIdsTypes.get(0);
        } else
            return null;
    }

    private void validateNewMobileNumber(String newMsisdn) throws UserIdException {
        if (newMsisdn.isEmpty() || newMsisdn.length() < 12 || !newMsisdn.startsWith("+")) {
            throw new UserIdException(CustomerException.INVALID_MSISDN_FORMAT);
        }
//		if(newMsisdn.isEmpty()){
//			throw new UserIdException(CustomerException.NEW_MSISDN_IS_EMPTY);
//		}
//		if(newMsisdn.length()<12){
//			throw new UserIdException(CustomerException.NEW_MSISDN_LENGTH_IS_WRONG);
//		}
//		if(!newMsisdn.startsWith("+")){
//			throw new UserIdException(CustomerException.NEW_MSISDN_COUNTRY_CODE_IS_MISSING);
//		}
    }

    @Override
    public BusinessMessage checkNewMobileNumberExistance(BusinessMessage businessMessage) throws UserIdException, SMEException {
        String newMsisdn = businessMessage.getPrimarySenderInfo().getNewMsisdn();
        validateNewMobileNumber(newMsisdn);
        String Msisdn = businessMessage.getPrimarySenderInfo().getMsisdn();
        if (newMsisdn.equals(Msisdn)) {
            throw new UserIdException(CustomerException.NEW_MSISDN_THE_SAME_CURRENT_MSISDN);
        }
        try {
            businessMessage.getWalletInfo()
                    .setWalletShortCode(businessMessage.getPrimarySenderInfo().getWalletShortCode());
            businessMessage = loadWalletInfo(businessMessage);
            MsisdnFormatterImpl.getInstance().formatMSISDN(businessMessage.getPrimarySenderInfo().getNewMsisdn(),
                    businessMessage.getWalletInfo().getCountryCode());
        } catch (GeneralFailureException e1) {
            e1.printStackTrace();
        }
        Customer customer = null;
        BusinessUser businessUser = null;
        try {
            customer = getCustomerByMsisdn(businessMessage.getPrimarySenderInfo().getNewMsisdn());
            businessUser = getByMsisdn(businessMessage.getPrimarySenderInfo().getNewMsisdn(),
                    businessMessage.getPrimarySenderInfo().getWalletShortCode());
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (customer != null || businessUser != null) {
            throw new SMEException(SMEException.MOBILE_NUMBER_ALREADY_REGISTERED);
        }

        return businessMessage;
    }

    public BusinessUser getByMsisdn(String msisdn, String walletShortCode) throws BusinessUserException {
        BusinessUser businessUser = null;
        List result = this.baseDao.executeNamedQuery("BusinessUser.findByMsisdn", BusinessUser.class, false, false,
                new Object[]{msisdn, walletShortCode});
        if ((result != null) && (!result.isEmpty())) {
            businessUser = (BusinessUser) result.get(0);
        }
        return businessUser;
    }

    public BusinessMessage checkEmailExistance(BusinessMessage businessMessage)
            throws UserIdException, GeneralFailureException {

        Customer customer = null;
        BusinessUser businessUser = null;
        customer = getCustomerByEmail(businessMessage.getPrimarySenderInfo().getPersonalDetails().getEmail());
        businessUser = getBusinessUserByEmail(businessMessage.getPrimarySenderInfo().getPersonalDetails().getEmail());

        if (customer != null || businessUser != null) {
            throw new UserIdException(UserIdException.Email_address_already_registered);
        }

        return businessMessage;
    }

    public BusinessUser getBusinessUserByEmail(String email) throws BusinessUserException {
        BusinessUser businessUser = null;
        List result = this.baseDao.executeNamedQuery("BusinessUser.findByEmail", BusinessUser.class, false, false,
                new Object[]{email});
        if ((result != null) && (!result.isEmpty())) {
            businessUser = (BusinessUser) result.get(0);
        }
        return businessUser;
    }

    private boolean validateEmail(String email) {
        if (email != null) {
            String regex = "^[\\w-_\\.+]*[\\w-_\\.]\\@([\\w]+\\.)+[\\w]+[\\w]$";
            return email.matches(regex);
        }
        return false;
    }

    public BusinessMessage checkUserNameExistance(BusinessMessage businessMessage) throws UserIdException {

        Customer customer = null;
        BusinessUser businessUser = null;
        try {
            customer = getCustomerByUserName(businessMessage.getPrimarySenderInfo().getUserName());
            businessUser = getBusinessUserByUserName(businessMessage.getPrimarySenderInfo().getUserName());
        } catch (Exception e) {
            e.printStackTrace();

        }
        if (customer != null || businessUser != null) {
            throw new UserIdException(UserIdException.username_already_registered);
        }

        return businessMessage;
    }

    private BusinessUser getBusinessUserByUserName(String userName) {
        BusinessUser businessUser = null;
        List result = this.baseDao.executeNamedQuery("BusinessUser.findByUserName", BusinessUser.class, false, false,
                new Object[]{userName});
        if ((result != null) && (!result.isEmpty())) {
            businessUser = (BusinessUser) result.get(0);
        }
        return businessUser;
    }

    private Customer getCustomerByUserName(String userName) {
        Customer customer = null;
        List<Customer> customers = this.baseDao.findByProperty(Customer.class, "userName", userName, false);
        if ((customers != null) && (customers.size() > 0)) {
            customer = (Customer) customers.get(0);
        }

        return customer;
    }

    public void checkPassword(String Password) throws GeneralFailureException {
        if (!Password.matches("^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{6,}$")) {
            throw new GeneralFailureException(CustomerException.PASSWORD_IS_NOT_VALID);
        }

    }

    public void checkPasswordSelfReg(String Password) throws GeneralFailureException {
        if (!Password.matches("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&-_+=()])(?=\\\\S+$).{8,}$")) {
            throw new GeneralFailureException(CustomerException.PASSWORD_FORMAT_NOT_VALID);
        }

    }

    @Override
    public BusinessMessage ValidateUserNameAndMSISDNAndEmail(BusinessMessage businessMessage)
            throws UserIdException, GeneralFailureException {

        boolean isValidEmail;
        boolean isValidPassword;
        String pin = businessMessage.getPrimarySenderInfo().getPin();
        String serviceCode = businessMessage.getServiceInfo().getCode();


        WalletUserIdsType walletUserIdsType = getWalletUserIdsTypesByWalletShortCode(businessMessage);

        if (walletUserIdsType == null) {
            throw new UserIdException(UserIdException.Wallet_User_Id_Type_Not_Found);
        }
        String walletShortCode = businessMessage.getPrimarySenderInfo().getWalletShortCode();
        if (walletUserIdsType.getCustomerUserIdsTypeID() == CustomerUserIdsTypeEnum.USER_NAME) {
            if (!securityRulesManager.validatePattern(businessMessage.getPrimarySenderInfo().getUserName(),
                    walletShortCode, DEFAULTUSERNAMEPATTERNKEY)) {
                throw new UserIdException(UserIdException.INVALID_USER_NAME);
            }
        }
        isValidEmail = validateEmail(businessMessage.getPrimarySenderInfo().getPersonalDetails().getEmail());
        if (!isValidEmail) {
            throw new CustomerException(CustomerException.INVALID_EMAIL_FORMAT);
        }


        securityRulesManagerLocal.pinNotReptitiveSeqential(pin);

        String congirmdPin = hashPassword(businessMessage.getPrimarySenderInfo().getConfirmPin());
        String newEnteredPin = hashPassword(pin);

        String pinRegexKey = "pin.length";
        int pinLength = Integer.parseInt(getPinRegex(pinRegexKey));
        if (pin.length() != pinLength)
            throw new BusinessUserException(CustomerException.PIN_DO_NOT_MATCH_PIN_FORMAT);


        if (!newEnteredPin.equals(congirmdPin)) {
            throw new CustomerException(CustomerException.PIN_AND_CONFIRM_PIN_DO_NOT_MATCH);
        }
        String password = businessMessage.getPrimarySenderInfo().getPassword();
        String confirmPassword = businessMessage.getPrimarySenderInfo().getConfirmPassword();

        securityRulesManager.applySecurityRulesSelfRegistration(password, walletShortCode, DEFAULPASSWORDPATTERNKEY, PASSWORDVALIDITYPERIOD, OLDPASSWORDTRACKED, serviceCode);
        if (!password.equals(confirmPassword)) {
            throw new CustomerException(CustomerException.PASSWORD_AND_CONFIRM_PASSWORD_DO_NOT_MATCH);
        }

        return null;
    }

    @Override
    public BusinessMessage CheckUserNameAndMSISDNAndEmailExistance(BusinessMessage businessMessage)
            throws UserIdException, GeneralFailureException {

        checkMobileNumberExistance(businessMessage);
        checkUserNameExistance(businessMessage);
        checkEmailExistance(businessMessage);
        checkIdentificationId(businessMessage);
        return businessMessage;
    }

    public void checkIdentificationId(BusinessMessage businessMessage) throws CustomerException {
        String identificationType = businessMessage.getPrimarySenderInfo().getPersonalDetails().getIdentifierType();
        String identificationId = businessMessage.getPrimarySenderInfo().getPersonalDetails().getIdentifier();
        String stringBuilder = ServiceQueryEngine.getQueryStringToExecute("checkIdentificationId", this.getClass(), identificationType, identificationId);
        Query query = (Query) baseDao.executeNativeQuery(stringBuilder);


        List<Object> results = query.getResultList();
        if (!results.isEmpty())
            throw new CustomerException(CustomerException.IDENTIFICATION_ID_REGISTERED);
    }

    public boolean isMobileNumberExistForGenricOtpGeneration(BusinessMessage businessMessage) throws UserIdException {

        try {
            businessMessage.getWalletInfo()
                    .setWalletShortCode(businessMessage.getPrimarySenderInfo().getWalletShortCode());
            businessMessage = loadWalletInfo(businessMessage);
            MsisdnFormatterImpl.getInstance().formatMSISDN(businessMessage.getPrimarySenderInfo().getMsisdn(),
                    businessMessage.getWalletInfo().getCountryCode());
        } catch (GeneralFailureException e1) {
            e1.printStackTrace();
        }
        Customer customer = null;
        BusinessUser businessUser = null;
        try {
            customer = getCustomerByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());
            businessUser = getByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn(),
                    businessMessage.getPrimarySenderInfo().getWalletShortCode());
        } catch (Exception e) {
            e.printStackTrace();

        }

        if (customer != null || businessUser != null) {
            return true;
        }

        return false;
    }


    public boolean doesMobileNumberExist(String msisdn, String walletShortCode) {
        Customer customer = null;
        BusinessUser businessUser = null;
        try {
            customer = getCustomerByMsisdn(msisdn);
            businessUser = getByMsisdn(msisdn, walletShortCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return customer != null || businessUser != null;
    }


    public BusinessMessage checkMobileNumberExistance(BusinessMessage businessMessage) throws UserIdException {

        try {
            businessMessage.getWalletInfo()
                    .setWalletShortCode(businessMessage.getPrimarySenderInfo().getWalletShortCode());
            businessMessage = loadWalletInfo(businessMessage);
            MsisdnFormatterImpl.getInstance().formatMSISDN(businessMessage.getPrimarySenderInfo().getMsisdn(),
                    businessMessage.getWalletInfo().getCountryCode());
        } catch (GeneralFailureException e1) {
            e1.printStackTrace();
        }
        Customer customer = null;
        BusinessUser businessUser = null;
        try {
            customer = getCustomerByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());
            businessUser = getByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn(),
                    businessMessage.getPrimarySenderInfo().getWalletShortCode());
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (customer != null || businessUser != null) {
            throw new UserIdException(UserIdException.Phone_number_already_registered);
        }

        return businessMessage;
    }

    //@Async
    public void logCustomerIdUserIdHistory(Long customerId, String oldUserData, String newUserData,
                                           Enums.CustomerUserIdsTypeEnum userIdType, Enums.ActionType actionType) throws CustomerException {
        if (customerId == null || oldUserData == null || newUserData == null) {
            throw new CustomerException(CustomerException.COULDNOT_LOG_WRONG_USERID_OR_CUSTOMERID);
        }
        if (userIdType == null || actionType == null) {
            throw new CustomerException(CustomerException.WRONG_ACTIONTYPE_OR_USERTYPE);
        }
        CustomerDataChangeLog log = new CustomerDataChangeLog();
        log.setChangeDate(new Date());
        log.setCustomerId(customerId);
        log.setActionType(actionType);
        log.setNewUserData(newUserData);
        log.setOldUserData(oldUserData);
        log.setUserIdType(userIdType);
        this.baseDao.save(log);
    }

    public Customer getCustomerByPersonalId(String userPersonalIDNumber, String identificationType)
            throws CustomerException {
        if (userPersonalIDNumber == null || identificationType == null) {
            throw new CustomerException(CustomerException.INVALID_MOBILE_NUMBER_OR_IDENTIFICATION_DATA);
        }
        Customer customer = null;
        StringBuilder query = new StringBuilder();
        query.append(
                "select model from Customer model where model.userPersonalIDNumber='" + userPersonalIDNumber + "'");
        query.append(" AND model.userPersonalIDType='" + identificationType + "'");

        List<Customer> customers = baseDao.getEntityManager().createQuery(query.toString()).getResultList();
        if (customers == null) {
            throw new CustomerException("BUS01009");
        }
        customer = customers.get(0);

        return customer;
    }

    public void initiateForgetUserName(String msisdn, String identificationId, String identificationType)
            throws CustomerException {
        Customer customer = getCustomerByPersonalId(identificationId, identificationType);
        if (!customer.getMsisdn().equals(msisdn.trim()) || msisdn == null) {
            throw new CustomerException(CustomerException.INVALID_MOBILE_NUMBER_OR_IDENTIFICATION_DATA);
        }
    }

    public BusinessMessage getUserName(BusinessMessage message) throws CustomerException {
        String identificationId = message.getPrimarySenderInfo().getPersonalDetails().getIdentifier();
        String msisdn = message.getPrimarySenderInfo().getMsisdn();
        Customer customer = this.baseDao.findSingleResultByProperty(Customer.class, "msisdn", msisdn);
        String userId = customer.getUserId();
        message.getPrimarySenderInfo().setUserId(userId);
        return message;
    }

    public void updateCustomerUserName(BusinessMessage message) throws GeneralFailureException {
        Customer customer = getCustomer(message.getPrimarySenderInfo().getCustomerId());
        Enums.CustomerUserIdsTypeEnum chngedUserId = Enums.CustomerUserIdsTypeEnum.USER_NAME;
        String oldUserName = customer.getUserName() == null ? "" : customer.getUserName().trim();
        String NewUserName = message.getPrimarySenderInfo().getUserName().trim();
        String walletShortCode = message.getPrimarySenderInfo().getWalletShortCode();

        newUsernameIsNotTheOld(message, customer);

        validateUsernameFormat(NewUserName, walletShortCode);

        if (!validateUserNameuniqueness(NewUserName)) {
            HashMap<String, String> map = new HashMap<>();
            map.put("username", "Username already registered");
            throw new UserIdException(UserIdException.INVALID_USER_NAME_ALREADY_REGISTERD);
        }

        customer.setUserName(NewUserName);
        uniqueIdentifierManager.updateUserId(customer, chngedUserId, oldUserName);
        this.baseDao.update(customer);
    }

    @Override
    public void validateUsernameFormat(String newUserName, String walletShortCode) throws CustomerException, UserIdException {
        if (!securityRulesManager.validatePattern(newUserName, walletShortCode, DEFAULTUSERNAMEPATTERNKEY)) {
            HashMap<String, String> map = new HashMap<>();
            map.put("username", "Invalid Username");
            throw new UserIdException(UserIdException.INVALID_USER_NAME);
        }
    }

    public void newUsernameIsNotTheOld(BusinessMessage message, Customer customer) throws UserIdException {
        String oldUserName = customer.getUserName() == null ? "" : customer.getUserName().trim();
        String NewUserName = message.getPrimarySenderInfo().getUserName().trim();
        if (NewUserName.equals(oldUserName)) {
            HashMap<String, String> map = new HashMap<>();
            map.put("username", "Username is the same current username");
            throw new UserIdException(UserIdException.USER_NAME_SAME_AS_ACTUALY_USER_NAME);
        }
    }

    boolean validateUserNameuniqueness(String UserName) {
        boolean isUnique = false;
        List<Customer> CustomersList = this.baseDao.findByProperty(Customer.class, "userName", UserName, false);
        if (CustomersList == null || CustomersList.size() <= 0) {
            isUnique = true;
        }
        return isUnique;
    }

    @Override
    public boolean updatedVerifyPassword(String userId, String password, boolean trialsEnabled, Long serviceLogId,
                                         BusinessMessage message) throws GeneralFailureException, IOException {
        if (Strings.isNullOrEmpty(password)) {
            handleMissingAttributesException("PASSWORD");
        }

        String passwordMaxTrialsKey = "Password_Max_Retrials";
        String lockoutPeriodkey = "Lockout_Period";
//		String passwordMaxTrialsValue = getValueFromGeneralLookup(message, passwordMaxTrialsKey);
        String passwordMaxTrialsValue = propertyLoader.loadProperty("PASS_MAX_TRIALS");
        String passMaxTrials = passwordMaxTrialsValue;
        boolean isCoolOfEnabled = Boolean.parseBoolean(this.propertyLoader.loadProperty("IS_COOLOFF_ENABLED"));
        int MAX_TRIALS = Integer.parseInt(passMaxTrials);
        String lockoutPeriodValue = getValueFromGeneralLookup(message, lockoutPeriodkey);
        String cooloffTimeLimit = lockoutPeriodValue;
        int coolOffTimeLimit = Integer.parseInt(propertyLoader.loadProperty("Cool_Off_TIME_LIMIT"));

        HashMap<String, String> timeRemain = new HashMap<String, String>();
        timeRemain.put("time", cooloffTimeLimit);

        boolean verified = false;

        Customer customer = getCustomer(userId);
        if (customer == null) {
            throw new CustomerException(CustomerException.INVALID_USERNAME_PASSWORD);
        }

        if ((customer.getHandsetPassword() == null) && (customer.getCustomerRegistrationType()
                .intValue() == Enums.CustomerRegisterationTypeEnum.USSD.ordinal())) {
            throw new CustomerException("VAL302022");
        }

        if (password != null) {
            boolean plainPassword = message.getPrimarySenderInfo().isPlainPassword();
            if (plainPassword) {
                String hashedpassword = hashString(password);
                verified = hashedpassword.equals(customer.getHandsetPassword());
            } else {
                verified = password.equals(customer.getHandsetPassword());
            }
        }
        if (trialsEnabled) {
            if (!verified) {

                customer.setLastFailedLogin(new Date());
                Integer numberOfTrials = customer.getLoginTrialsPassword();

                int newNumberOfTrails = numberOfTrials + 1;
                Integer customerCoolOffLimit = customer.getCoolOffCycleLimit();
                if (customerCoolOffLimit == null)
                    coolOffTimeLimit = 0;
                if (newNumberOfTrails < MAX_TRIALS) { // NO LOCK
                    customer.setLoginTrialsPassword(Integer.valueOf(numberOfTrials.intValue() + 1));
                    boolean isLockSoon = islockSoon(newNumberOfTrails, MAX_TRIALS);
                    updateCustomer(customer);
                    if (isLockSoon)
                        throw new CustomerException("VAL012011");
                    else
                        throw new CustomerException(CustomerException.INVALID_USERNAME_PASSWORD);
                } else if (newNumberOfTrails >= MAX_TRIALS) { // LOCK
                    int newCustomerCoolOffLimit = customerCoolOffLimit + 1;

                    if (newCustomerCoolOffLimit < coolOffTimeLimit) {
                        customer = blockingManager.lockUser(customer.getCustomerID(), Enums.UserType.CUSTOMER, userId,
                                serviceLogId, null, "blocked by password");
                        updateCustomer(customer, MAX_TRIALS, newCustomerCoolOffLimit);
                        throw new CustomerException(CustomerException.COOL_OFF_WAIT_TIME, timeRemain);
                    } else if (newCustomerCoolOffLimit == coolOffTimeLimit) { //BLOCK
                        customer = blockingManager.blockUser(customer.getCustomerID(), Enums.UserType.CUSTOMER, userId,
                                serviceLogId, null, "blocked by password");
                        updateCustomer(customer, MAX_TRIALS, newCustomerCoolOffLimit);
                        throw new GeneralFailureException(CustomerException.BLOCKED_USER);
                    }
                }
                throw new CustomerException("VAL012011");
            } else {
                customer.setLastAccessDate(new Date());
                customer.setLoginTrialsPassword(0);
                customer.setCoolOffCycleLimit(0);
            }
        }
        return verified;
    }

    private boolean islockSoon(int newNumberOfTrails, int max_trials) {

        if (++newNumberOfTrails == max_trials)
            return true;
        return false;
    }

    public void updateCustomer(Customer customer, int maxTrails, int coolOfLimit) throws CustomerException {
        customer.setLastModifiedDate(new Date());
        reflectDetailsRelations(customer);
        customer.setCoolOffCycleLimit(coolOfLimit);
        customer.setLoginTrialsPassword(Integer.valueOf(maxTrails));
        customer.setBlockingDate(new Date());
        customer.setReasonOfClosing("blocked by password");
        this.baseDao.update(customer);
    }

    private String getValueFromGeneralLookup(BusinessMessage businessMessage, String Key) {
        String walletShortCode = businessMessage.getPrimarySenderInfo().getWalletShortCode();
        String walletPattern = securityRulesManager.getValeByKeyFromGeneralLookup(walletShortCode + "_" + Key);
        String currentUsedPattern = walletPattern != null ? walletPattern
                : securityRulesManager.getValeByKeyFromGeneralLookup(Key);
        return currentUsedPattern;
    }

    @Override
    public void saveBillDetails(BillerStock billerStock) {
        baseDao.save(billerStock);
    }

    @Override
    public List<BillerStock> getAllCustomerBills(String customerID) {

        return baseDao.executeNamedQuery("BillerStock.findByCustomerID", BillerStock.class, false, false, customerID);

    }

    @Override
    public List<BillerStock> checkBillNameExists(String billName, String customerID) {
        return baseDao.executeNamedQuery("BillerStock.checkBillNameExists", BillerStock.class, false, false, billName,
                customerID);
    }

    @Override
    public void deleteBill(Long id) {
        baseDao.executeNamedQuery("BillerStock.deleteBill", BillerStock.class, true, false, id);
    }

    @Override
    public void editBillName(String billName, String customerID, Long id) {

        baseDao.executeNamedQuery("BillerStock.updateBill", BillerStock.class, true, false, billName, customerID, id);
    }

    @Override
    public List<String> getRecurringForUser(String customerID) {
        List<String> customerMessages = this.baseDao.executeNamedQuery("GetBillsStockAndRecurring", String.class, false,
                false, Long.parseLong(customerID));
        return customerMessages;
    }

    @Override
    public Object saveCreditExpress(String msisdn, Double amount, Long uniqueId, Double rate, Integer tenor,
                                    Double repaymentAmount, String repaymentStarts) {

        Timestamp startTime = new Timestamp(new Date().getTime());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SS");
        String startTimeStr = dateFormat.format(startTime);

        Query sqlQuery = this.baseDao.getEntityManager().createQuery(
                "INSERT INTO CREDIT_EXPRESS(ID, SENDER_MSISDN, REQUEST_AMOUNT, REQUEST_UNIQUEID, RATE, TENOR, REPAYMENT_AMOUNT, REPAYMENT_STARTS, CREATION_DATE)   VALUES(END_TO_END_SEQ.NEXTVAL, :msisdn, :amount, :uniqueId, :rate, :tenor, :repaymentAmount, :repaymentStarts , TO_TIMESTAMP(:startTimeStr, 'yyyy-MM-dd HH24:MI:SS:FF'))");
        sqlQuery.setParameter("msisdn", msisdn);
        sqlQuery.setParameter("amount", amount);
        sqlQuery.setParameter("uniqueId", uniqueId);
        sqlQuery.setParameter("rate", rate);
        sqlQuery.setParameter("tenor", tenor);
        sqlQuery.setParameter("repaymentAmount", repaymentAmount);
        sqlQuery.setParameter("repaymentStarts", repaymentStarts);
        sqlQuery.setParameter("startTimeStr", startTimeStr);

        Object generatedId = sqlQuery.getSingleResult();
        return generatedId;

    }

    @Override
    public void updateCreditExpress(Long generatedID, String statusCode, String statusMessage) {

        Query sqlQuery = this.baseDao.getEntityManager().createQuery(
                "UPDATE CREDIT_EXPRESS SET STATUS_RESPONSE = :statusCode , MESSAGE_RESPONSE = :statusMessage  WHERE ID = :generatedID ");
        sqlQuery.setParameter("statusCode", statusCode);
        sqlQuery.setParameter("statusMessage", statusMessage);
        sqlQuery.setParameter("generatedID", generatedID);
        sqlQuery.executeUpdate();
    }

    @Override
    public BusinessMessage checkMobileNumberExistaance(BusinessMessage businessMessage) throws Exception {

        businessMessage.getWalletInfo().setWalletShortCode(businessMessage.getPrimarySenderInfo().getWalletShortCode());
        businessMessage = loadWalletInfo(businessMessage);
        MsisdnFormatterImpl.getInstance().formatMSISDN(businessMessage.getPrimarySenderInfo().getMsisdn(),
                businessMessage.getWalletInfo().getCountryCode());

        Customer customer = null;
        BusinessUser businessUser = null;

        customer = getCustomerByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());
        businessUser = getByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn(),
                businessMessage.getPrimarySenderInfo().getWalletShortCode());

        if (customer != null || businessUser != null) {
            throw new UserIdException(UserIdException.Phone_number_already_registered);
        }

        return businessMessage;
    }

    @Override
    public Customer getCustomerByIdentificationKey(String identificationKey) {
        Customer customer = baseDao.findSingleResultByProperty(Customer.class, "identificationKey", identificationKey);
        return customer;

    }

    @Override
    public BusinessMessage setBECustomerNewPasswordAndPIN(BusinessMessage businessMessage) throws CustomerException {
        Customer customer = getCustomerByMsisdn(businessMessage.getPrimarySenderInfo().getMsisdn());

        if (businessMessage.getPrimarySenderInfo().getUserKey() == null) {
            String userKey = customer.getWalletShortCode().concat(customer.getMsisdn());
            businessMessage.getPrimarySenderInfo().setUserKey(userKey);
        }

        String userKey = businessMessage.getPrimarySenderInfo().getUserKey();
        String newPassword = businessMessage.getPrimarySenderInfo().getNewPassword();
        String newPin = businessMessage.getPrimarySenderInfo().getNewPin();

        setPassword(userKey, hashPassword(newPassword));
        setPin(userKey, newPin);

        return businessMessage;
    }

    @Override
    public BusinessMessage updateMessageWithCustomerSmeData(BusinessMessage businessMessage, Customer customer)
            throws CustomerException {
        boolean isSme = false;
        boolean isRetail = false;
        String garsSb = ServiceQueryEngine.getQueryStringToExecute("updateMessageWithCustomerSmeData", this.getClass(), String.valueOf(customer.getCustomerID()), String.valueOf(PaymentMethodCategory.RETAIL.ordinal()), String.valueOf(PaymentMethodCategory.STAFF.ordinal()), String.valueOf(PaymentMethodCategory.PREPAID.ordinal()));
        Query query = (Query) this.baseDao.executeNativeQuery(garsSb);
        List<Object[]> rows = query.getResultList();
        if (rows != null && rows.size() > 0) {
            isRetail = true;
        }

        if (customer.getBECustomerId() != null)
            isSme = true;

        if (isSme == true)
            businessMessage = updateMessageWithSmeFlags(businessMessage, customer);
        businessMessage.getPrimarySenderInfo().setIsSme(isSme);
        businessMessage.getPrimarySenderInfo().setIsRetail(isRetail);
        businessMessage = updateMessageWithFamilyCustomerData(businessMessage, customer);
        return businessMessage;
    }

    @Override
    public void updateCustomerSymmetricWorkingKey(String swk, Long customerId) {
        baseDao.executeNamedQuery("Customer.updateSymmetricWorkingKey", Customer.class, true, false, swk, customerId);

    }

    @Override
    public BusinessMessage updateSmeBulkInfo(BusinessMessage businessMessage) throws GeneralFailureException {
        if (!Strings.isNullOrEmpty(businessMessage.getPrimarySenderInfo().getPin())
                && !Strings.isNullOrEmpty(businessMessage.getPrimarySenderInfo().getPassword())
                && businessMessage.getWalletInfo().getWalletShortCode() != null
                && businessMessage.getPrimarySenderInfo().getMsisdn() != null) {
            String idenStringtificationKey = businessMessage.getWalletInfo().getWalletShortCode()
                    + businessMessage.getPrimarySenderInfo().getMsisdn();
            String pin = hashString(businessMessage.getPrimarySenderInfo().getPin());
            checkAndHashPlainPassword(businessMessage.getPrimarySenderInfo());
            String password = businessMessage.getPrimarySenderInfo().getPassword();
            String imei = businessMessage.getPrimarySenderInfo().getImei();

            CustomerAuthenticationTypeEnum customerAuthenticationType = CustomerAuthenticationTypeEnum
                    .values()[businessMessage.getPrimarySenderInfo().getAuthenticationType().intValue()];
            CustomerRegisterationTypeEnum customerRegisterationType = CustomerRegisterationTypeEnum.BULK;
            Customer customer = getCustomer(idenStringtificationKey);
            customer.setCustomerAuthenticationType(Integer.valueOf(customerAuthenticationType.getAuthenticationType()));

            CustomerProfileType customerProfileType = CustomerProfileType.PIN;

            if (customerAuthenticationType == CustomerAuthenticationTypeEnum.PIN) {
                customerProfileType = CustomerProfileType.PIN;
            } else if (customerAuthenticationType == CustomerAuthenticationTypeEnum.TOKEN) {
                customerProfileType = CustomerProfileType.TOKEN;
            }
            if (!businessMessage.getPrimarySenderInfo().isAuthenticated()) {
                customer.setCustomerProfileTypeID((long) CustomerProfileTypeEnum.LOCKED.getProfileType());
            } else {
                customer.setCustomerProfileTypeID((long) CustomerProfileTypeEnum.LIMITED.getProfileType());
            }
            String swk = businessMessage.getPrimarySenderInfo().getSwk();
            String sncryptedSwk = encryptCustomerSWK(swk);
            Long sessionId = createOrUpdateCustomerSession(idenStringtificationKey, sncryptedSwk);
            businessMessage.setSessionId(sessionId);
            customer.setLastModifiedDate(new Date());
            customer.setStatus(UserStatus.ACTIVE);
            customer.setNewLogin(true);
            customer.setReasonOfClosing(null);
            customer.setPIN(pin);
            customer.setHandsetPassword(password);
            customer.setIMEI(imei);
            customer.setSymmetricWorkingKey(sncryptedSwk);
            customer.setCustomerRegistrationType(customerRegisterationType.getRegisterationType());
            customer = assignDefaultSegmintationToCustomer(customer, null,
                    businessMessage.getPrimarySenderInfo().getWalletShortCode(), customerProfileType,
                    businessMessage.getPrimarySenderInfo().getSchmCode(), businessMessage);
            return updateMessageWithCustomerSmeData(businessMessage, customer);
        }
        return businessMessage;
    }

    @Override
    public void updateSmeBulkCustomerPinAndPassword(BusinessMessage businessMessage) {
        if (!Strings.isNullOrEmpty(businessMessage.getPrimarySenderInfo().getPin())
                && !Strings.isNullOrEmpty(businessMessage.getPrimarySenderInfo().getPassword())
                && businessMessage.getWalletInfo().getWalletShortCode() != null
                && businessMessage.getPrimarySenderInfo().getMsisdn() != null) {
            String idenStringtificationKey = businessMessage.getWalletInfo().getWalletShortCode()
                    + businessMessage.getPrimarySenderInfo().getMsisdn();
            String pin = hashString(businessMessage.getPrimarySenderInfo().getPin());
            checkAndHashPlainPassword(businessMessage.getPrimarySenderInfo());
            String password = businessMessage.getPrimarySenderInfo().getPassword();
            String sb = ServiceQueryEngine.getQueryStringToExecute("updateSmeBulkCustomerPinAndPassword", this.getClass(), pin, password, idenStringtificationKey);
            Query query = (Query) this.baseDao.executeNativeQuery(sb);
            query.executeUpdate();
        }

    }

    public void checkAndHashPlainPassword(PartyDetails senderInfo) {
        String subscriperPassword = null;
        String subscriberNewPassword = null;
        if (senderInfo.isPlainPassword()) {
            subscriperPassword = senderInfo.getPassword();
            subscriberNewPassword = senderInfo.getNewPassword();
            if (subscriperPassword != null) {
                String subscriperHashedPassword = hashPassword(subscriperPassword);
                senderInfo.setPassword(subscriperHashedPassword);
            }
            if (subscriberNewPassword != null) {
                String subscriperNewHashedPassword = hashPassword(subscriberNewPassword);
                senderInfo.setNewPassword(subscriperNewHashedPassword);
            }
        }
    }

    @Override
    public FamilyCustomer getFamilyCustomer(Long id) {
        return (FamilyCustomer) this.baseDao.findById(FamilyCustomer.class, id);
    }

    @Override
    public void updateFamilyCustomerToSetClosedDate(FamilyCustomer familyCustomer, int daysToBeClosed) {

        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, daysToBeClosed);
        familyCustomer.setClosedDate(c.getTime());
        this.baseDao.update(familyCustomer);

    }

    private BusinessMessage updateMessageWithSmeFlags(BusinessMessage businessMessage, Customer customer)
            throws CustomerException {

        BusinessEntityCustomer businessEntityCustomer = baseDao.findById(BusinessEntityCustomer.class,
                customer.getBECustomerId());
        PartyDetails primarySenderInfo = businessMessage.getPrimarySenderInfo();
        primarySenderInfo.setBEHierarchyId(businessEntityCustomer.getCorporateId());
        primarySenderInfo.setBECustomerId(businessEntityCustomer.getId());
        primarySenderInfo.setBEParentCustomerId(businessEntityCustomer.getParentCustomerId());
        primarySenderInfo.setBECustomerRoleId(businessEntityCustomer.getRole());
        primarySenderInfo.setIsOwner(getCustomerByMsisdn("SME" + customer.getMsisdn()) != null ? true : false);
        primarySenderInfo.setCustomerId(customer.getCustomerID());
        String CorporateRegistrationNo = getCorporateRegistrationNoById(businessEntityCustomer.getCorporateId());
        boolean hasCorporateId = CorporateRegistrationNo != null;
        primarySenderInfo.setHasCorporateRegistrationNo(hasCorporateId);
        if (businessEntityCustomer.getStatus().equals(1L))
            primarySenderInfo.setIsSmeActive(true);
        else
            primarySenderInfo.setIsSmeActive(false);
        if (businessEntityCustomer.getStatus().equals(2L))
            primarySenderInfo.setIsSmeBlocked(true);

        if (businessEntityCustomer.getSuperUser() != null && businessEntityCustomer.getSuperUser().equals(1))
            primarySenderInfo.setIsSuperUser(true);

        businessMessage.setPrimarySenderInfo(primarySenderInfo);
        return businessMessage;
    }

    @Override
    public boolean saveCustomerimagePath(String path, int customerId) throws Exception {
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("saveCustomerimagePath", this.getClass(),
                path, String.valueOf(customerId));
        EntityManager em = baseDao.getEntityManager();
        int query = em.createNativeQuery(queryStr).executeUpdate();
        if (query != 0) {
            return true;
        } else {
            return false;
        }

    }

    private String getCorporateRegistrationNoById(Long corporateId) {
        String corporateRegistrationNo = null;
        if (corporateId != null) {
            try {
                corporateRegistrationNo = baseDao.executeNamedQueryForSingleResult(
                        "BusinessEntity.getRegistrationNoById", String.class, false, false, corporateId);
            } catch (Exception e) {
                System.out.println("FAILED TO GET CORPORATE REGISTRATION NUMBER");
            }
        }
        return corporateRegistrationNo;
    }

    private void closeFamilyAccount(BusinessMessage message) {

        Customer customer = null;
        Customer parentCustomer = null;
        boolean sholudBeClosed = false;
        try {
            customer = getCustomerByMsisdn(message.getPrimarySenderInfo().getMsisdn());
        } catch (CustomerException e) {
            e.printStackTrace();
        }

        FamilyCustomer findById = baseDao.findById(FamilyCustomer.class, customer.getFamilyCustomerID());

        if (findById.getClosedDate() != null) {
            Date closedDate = findById.getClosedDate();
            int isSentSMS = findById.getIsSentSMS();
            if ((closedDate.equals(new Date()) || closedDate.before(new Date())) && isSentSMS == 0) {
                sholudBeClosed = true;
            }
        }
        if (sholudBeClosed == true) {
            String closeFamilyMemberRecord = ServiceQueryEngine.getQueryStringToExecute("closeFamilyAccountFamilyMemberRecord", this.getClass(), String.valueOf(customer.getFamilyCustomerID()));
            Query query = (Query) this.baseDao.executeNativeQuery(closeFamilyMemberRecord);
            query.executeUpdate();


            HashMap<String, Object> parameters = new HashMap<String, Object>();

            try {
                parentCustomer = getCustomer(findById.getParentCustomerID());
            } catch (CustomerException e) {
                e.printStackTrace();
            }
            parameters.put("parentMsisdn", parentCustomer.getMsisdn());
            if (customer.getGender().ordinal() == 0)
                parameters.put("childGender", "he");
            else
                parameters.put("childGender", "she");
            parameters.put("memberName", customer.getFirstName());
            parameters.put("WALLET_SHORT_CODE", message.getWalletInfo().getWalletShortCode());
            parameters.put("Wallet_Info", message.getWalletInfo());
            parameters.put("SERVICE_LOG", (ServiceLog) message.getSoftFields().get("SERVICE_LOG"));
            this.notificationManager.sendNotification("closeFamilyMemberAccount", parameters);
            String setISentSms = ServiceQueryEngine.getQueryStringToExecute("closeFamilyAccountISentSms", this.getClass(), String.valueOf(customer.getFamilyCustomerID()));
            query = (Query) this.baseDao.executeNativeQuery(setISentSms);
            query.executeUpdate();
        }

    }

    private void checkCustomerBussinessEntityIsActive(Long beHierarchyId) throws GeneralFailureException {
        BusinessEntity beCustomerEntity = baseDao.findById(BusinessEntity.class, beHierarchyId);
        if (beCustomerEntity == null || !beCustomerEntity.getBusinessEntityStatus().equals(BusinessEntityStatus.ACTIVE))
            throw new GeneralFailureException(SMEException.INVALID_BUSINESS_ENTITY_STATUS);
    }

    private BusinessMessage updateMessageWithFamilyCustomerData(BusinessMessage businessMessage, Customer customer) {
        if (customer.getFamilyCustomerID() != null) {
            FamilyCustomer findById = baseDao.findById(FamilyCustomer.class, customer.getFamilyCustomerID());
            if (findById != null && findById.getStatus() != null && findById.getStatus() == ACTIVE_FAMILY_PROFILE) {
                businessMessage.getPrimarySenderInfo().setIsFamilyMember(true);
                businessMessage.getPrimarySenderInfo().setFamilyParentId(findById.getParentCustomerID());
                businessMessage.getPrimarySenderInfo().setFamilyChildId(customer.getFamilyCustomerID());
                businessMessage.getPrimarySenderInfo().setBEHierarchyId(customer.getWallet());
            }
            businessMessage.getPrimarySenderInfo().setCustomerId(customer.getCustomerID());
        }
        if (businessMessage.getSessionId() == null && businessMessage.getParameter() != null
                && businessMessage.getParameter() == 1) {
            String swk = businessMessage.getPrimarySenderInfo().getSwk();
            String encNewSwk = null;
            try {
                encNewSwk = encryptCustomerSWK(swk);
            } catch (SecurityException e) {
                e.printStackTrace();
            }
            Long sessionId = createOrUpdateCustomerSession(customer.getIdentificationKey(), encNewSwk);
            businessMessage.setSessionId(sessionId);
        }

        return businessMessage;

    }

    @Override
    public boolean isSmeCustomerBySchemCode(String schemCode) {
        if (schemCode == null || (schemCode != null && schemCode.isEmpty())) {
            return false;
        }
        BigDecimal smeAccountsCount = new BigDecimal(0);
        String sb = ServiceQueryEngine.getQueryStringToExecute("isSmeCustomerBySchemCode", this.getClass(), schemCode);
        Query query = (Query) this.baseDao.executeNativeQuery(sb);
        List results = query.getResultList();
        smeAccountsCount = (BigDecimal) results.get(0);
        if (smeAccountsCount != null && smeAccountsCount.equals(new BigDecimal("1"))) {
            return true;
        }
        return false;
    }

    public boolean validateSourecPaymentMethodBelongsToSenderCustomerProfile(Long senderPaymentMethodType,
                                                                             Long CustomerProfileID) {
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateSourecPaymentMethodBelongsToSenderCustomerProfile", this.getClass(), String.valueOf(senderPaymentMethodType), String.valueOf(CustomerProfileID));

        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        } else {
            return false;
        }
    }

    public boolean validateDestinationPaymentMethodBelongsToSenderCustomerProfile(Long receiverPaymentMethodType,
                                                                                  Long CustomerProfileID) {
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateDestinationPaymentMethodBelongsToSenderCustomerProfile", this.getClass(), String.valueOf(receiverPaymentMethodType), String.valueOf(CustomerProfileID));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        } else {
            return false;
        }
    }

    public boolean validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar(Long senderPaymentMethodType,
                                                                                 Long CustomerGarID, Long customerId) {
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar", this.getClass(), String.valueOf(senderPaymentMethodType), String.valueOf(customerId), String.valueOf(AccountStatus.ACTIVE.ordinal()), String.valueOf(CustomerGarID));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        } else {
            return false;
        }
    }


    public boolean validateDestinationPaymentMethodIsActive(Long destinationPaymentMethodType,
                                                            Long CustomerGarID) {
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("validateDestinationPaymentMethodIsActive", this.getClass(), String.valueOf(destinationPaymentMethodType), String.valueOf(AccountStatus.ACTIVE.ordinal()), String.valueOf(CustomerGarID));
        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();

        if ((resultList != null) && (resultList.size() > 0)) {
            return true;
        } else {
            return false;
        }
    }

//	public void  validateHealthyPaymentMethodIsActiveAndBelongsToHisCustomerGar(boolean isreciver ,boolean issender,Long senderPaymentMethodType,
//			Long CustomerGarID, Long customerId ,Long CustomertypeID)throws Exception {
//		StringBuilder querysb = new StringBuilder("select  cus_pro.ID ,CG.ID");
//		if ( issender ) {
//
//			querysb.append("PAYMENT_METHOD_CUST_PROF_CG   cus_pro");
//		}
//
//
//		else if (isreciver) {
//
//			querysb.append("DEST_PAYMENT_METHOD_CUST_PROF   cus_pro");
//		}
//
//		else if ( (!issender  && !isreciver) ) {
//
//			throw new CustomerException(CustomerException.invailed_input_paramter);
//		}
//
//
//
//
//		querysb.append(" left join  CUSTOMER_GAR CG  ON  CG.PAYMENT_METHOD_TYP =cus_pro.PAYMENT_METHOD_ID where  cus_pro.PAYMENT_METHOD_ID = '"+senderPaymentMethodType+"'");
//
//		querysb.append(" and cus_pro.CUSTOMER_TYPE_ID = "+ CustomertypeID +" and CG .STATUS = " + AccountStatus.ACTIVE.ordinal() + " and GARID = '"+CustomerGarID+"'");
//
//
//
//      if (issender) {
//
//    	  querysb.append("and CG.CUSTOMER_ID='"+customerId+"'");
//		}
//
//
//      Query query=(Query)baseDao.executeNativeQuery(querysb.toString());
//		List<Object> results = query.getResultList();
//		BigDecimal customerprofile =(BigDecimal)results.get(0);
//		BigDecimal  customergar=(BigDecimal)results.get(1);
//
//		if (issender ==true ) {
//
//			if(customerprofile==null )
//			{
//				throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
//			}
//
//			if(customergar==null ) {
//				throw new CustomerException(CustomerException.SOURCE_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
//
//			}
//
//		}
//
//
//		if(isreciver ==true ) {
//
//			if(customerprofile==null )
//			{
//				throw new CustomerException(CustomerException.DESTINATION_PAYMENT_METHOD_NOT_ASSIGNED_TO_CUSTOMER_PROFILE);
//			}
//
//
//			if(customergar==null ) {
//				throw new CustomerException(CustomerException.DESTINATION_PAYMENT_METHOD_NOT_FOUND_OR_NOT_ACTIVE);
//
//			}
//
//		}
//
//
//	}


    public PaymentMethod getPaymentMethodById(Long paymentMethodId) {
        return baseDao.findById(PaymentMethod.class, paymentMethodId);
    }

    public boolean validateSenderCanUseSenderPMdAndReceiverPMthod(Long SenderPaymentMethodType,
                                                                  Long ReceiverPaymentMethodType, Long paymentMethodCode, String userId,
                                                                  Boolean SMEFlag, Long BEParentCustomerId, String CorporateId) throws CustomerException {
        Customer customer = null;
        if (SMEFlag) {

            if (BEParentCustomerId != null)
                customer = baseDao.findById(Customer.class, BEParentCustomerId);


        } else {
            customer = getCustomerByUserId(userId);
        }


        if (!validateSourecPaymentMethodBelongsToSenderCustomerProfile(SenderPaymentMethodType,
                customer.getCustomerType())) {
            return false;
        }
        if (!validateDestinationPaymentMethodBelongsToSenderCustomerProfile(ReceiverPaymentMethodType,
                customer.getCustomerType())) {
            return false;
        }
        return true;
    }

    public void validateCustomerBankAccount(BusinessMessage businessMessage) throws Exception {
        validateCustomerExistance(businessMessage);
        validateWalletInfoForCustomerAccount(businessMessage);


    }

    private void validateCustomerExistance(BusinessMessage businessMessage) throws Exception {

        String msisdn = businessMessage.getExternalIntegrationResponse().getPhoneNumber();
        Query query = this.baseDao.getEntityManager().createQuery("select model from Customer  model where model.msisdn= :msisdn");
        query.setParameter("msisdn", msisdn);
        List customerList = new ArrayList(query.getResultList());
        if (customerList.size() > 0) {
            throw new CustomerException(CustomerException.CUSTOMER_ALREADY_EXIST);
        }
    }

    private void validateWalletInfoForCustomerAccount(BusinessMessage businessMessage) throws Exception {

        com.cit.mpaymentapp.common.message.WalletInfo walletInfo = businessMessage.getWalletInfo();

        if (!walletInfo.getCurrency().equals(businessMessage.getExternalIntegrationResponse().getCurrency())) {
            throw new CustomerException(CustomerException.DIFFERENT_BANK_ACCont_Currency);
        }

        if (!walletInfo.getCountryIso2().equals(businessMessage.getExternalIntegrationResponse().getCountryCode())) {
            throw new CustomerException(CustomerException.DIFFERENT_BANK_ACCont_Cuntry);
        }
    }

    public Properties loadConfigProperties() {
        String fileName = System.getenv("VERICASH_APIS_CONFIG") + File.separator
                + System.getenv("PROJECT") + "-config" + File.separator + "application" + File.separator + "application.properties";
        Properties configurationProperty = new Properties();
        try (InputStream input = new FileInputStream(fileName)) {
            configurationProperty.load(input);
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return configurationProperty;
    }

    public BusinessMessage LoadSetlementAccountFromEvoucher(BusinessMessage businessMessage) throws Exception {
        Map map = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("300"));
        String evoucherDeffId = (map != null && map.size() > 0) ? (String) map.get("parameterValue") : null;
//		DynamicPayload dynamicPayload = businessMessage.getDynamicPayload();
//		Payload payload = (Payload) payloadTransformer.transform(dynamicPayload);
//		String evoucherDeffId = payload.getAttributeAsString("evoucherDeffId");
        if (StringUtils.isBlank(evoucherDeffId)) {
            throw new CustomerException(CustomerException.EVOUCHER_ID_MUST_BE_NOT_EMPTY);
        }
        Long settelementAccountType = null;
        String natvieQuery = ServiceQueryEngine.getQueryStringToExecute("loadSetlementAccountFromEvoucher",
                this.getClass(), evoucherDeffId);

        Query query = (Query) this.baseDao.executeNativeQuery(natvieQuery);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            Object[] resultArray = (Object[]) resultList.get(0);
            if (resultArray[0] != null) {
                BigDecimal bigDecimal = ((BigDecimal) resultArray[0]);
                settelementAccountType = Long.valueOf(bigDecimal.longValue());
            }
            if (resultArray[1] != null) {
                businessMessage.getParameters().put("evoucherDeffName", (resultArray[1].toString()));
            }
        }


        if (settelementAccountType == null) {
            throw new CustomerException(CustomerException.INVALID_SETTELEMENT_ACCOUNT_TYPE);
        }
        businessMessage.getPrimaryReceiverInfo().setSettelementAccountType(settelementAccountType);
        return businessMessage;
    }

    @Override
    public BusinessMessage loadRecieverByBE(BusinessMessage message) throws GeneralFailureException {
        Long getSettelementAccountType = message.getPrimaryReceiverInfo().getSettelementAccountType();
        String walletShortCode = message.getWalletInfo().getWalletShortCode();
        Map map = (LinkedHashMap) (message.getDynamicPayload().getPayload().get("300"));
        String evoucherDeffId = (map != null && map.size() > 0) ? (String) map.get("parameterValue") : null;

        if (StringUtils.isBlank(evoucherDeffId)) {
            throw new CustomerException(CustomerException.EVOUCHER_ID_MUST_BE_NOT_EMPTY);
        }
        BusinessEntity businessEntity = null;
        if (getSettelementAccountType != null && getSettelementAccountType == Enums.settelementAccountType.MyAccount.ordinal()) {
//			BusinessEntity businessEntityWallet = businessEntityManager.findByBuId(Long.valueOf(walletShortCode));
//			businessEntity = businessEntityWallet.getParentBusinessEntity();
            businessEntity = businessEntityManager.getWalletByShortCode(walletShortCode);
        } else if (getSettelementAccountType != null && getSettelementAccountType == Enums.settelementAccountType.PartnerAccount.ordinal()) {
            BigDecimal businessEntityId = businessEntityManager.getBuinessEntityIDByEvoucherDeffId(Long.parseLong(evoucherDeffId));
            if (businessEntityId == null) {
                throw new CustomerException(CustomerException.INVALID_EVOUCHER_ID);
            }
            businessEntity = businessEntityManager.findByBusinessEntityIdAndParentShortCode(businessEntityId.longValue(),walletShortCode);
        } else {
            throw new CustomerException(CustomerException.INVALID_SETTELEMENT_ACCOUNT_TYPE);
        }
        PartyDetails primaryReceiverInfo = new PartyDetails();
        if (businessEntity != null) {
            primaryReceiverInfo.setWalletShortCode(walletShortCode);
            primaryReceiverInfo.setOwnerType(Enums.OwnerTypeEnum.BUSINESSENTITY);
            primaryReceiverInfo.getEntityInfo().setId(businessEntity.getBusinessEntityID());
            primaryReceiverInfo.getEntityInfo().setShortCode(walletShortCode);
            primaryReceiverInfo.setSynonym(businessEntity.getBusinessEntityName());
            message.setPrimaryReceiverInfo(primaryReceiverInfo);
        }
        return message;
    }


    public BusinessMessage getEvoucherAmount(BusinessMessage businessMessage) throws Exception {
        BigDecimal buyPrice = null;

        Map map = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("302"));
        String redemtionCode = (map != null && map.size() > 0) ? (String) map.get("parameterValue") : null;
        String encryptedRedemtionCode = AESEncryption.encrypt(redemtionCode);
        businessMessage.getSoftFields().put("encryptedRedemtionCode", encryptedRedemtionCode);
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("getEvoucherAmount", this.getClass(),
                encryptedRedemtionCode);
        Query query = (Query) this.baseDao.executeNativeQuery(queryStr);
        List resultList = query.getResultList();
        if ((resultList != null) && (resultList.size() > 0)) {
            buyPrice = (BigDecimal) resultList.get(0);


        }

        businessMessage.getTransactionInfo().setTransactionAmount(buyPrice);
        return businessMessage;
    }

    public BusinessMessage validateCutomerExistenceAndStatus(BusinessMessage businessMessage) throws GeneralFailureException {
        Map customerIdMap = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("303"));
        String customerId = (customerIdMap != null && customerIdMap.size() > 0) ? (String) customerIdMap.get("parameterValue") : null;

        Map evoucherDeffIdMap = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("300"));
        String evoucherDeffId = (evoucherDeffIdMap != null && evoucherDeffIdMap.size() > 0) ? (String) evoucherDeffIdMap.get("parameterValue") : null;

        Map assignmentTypeMap = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("298"));
        String assignmentType = (assignmentTypeMap != null && assignmentTypeMap.size() > 0) ? (String) assignmentTypeMap.get("parameterValue") : null;

        Map componentCodeMap = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("304"));
        String componentCode = (componentCodeMap != null && componentCodeMap.size() > 0) ? (String) componentCodeMap.get("parameterValue") : null;

        Map referenceIdMap = (LinkedHashMap) (businessMessage.getDynamicPayload().getPayload().get("305"));
        String referenceId = (referenceIdMap != null && referenceIdMap.size() > 0) ? (String) referenceIdMap.get("parameterValue") : null;


        if (StringUtils.isBlank(customerId)) {
            throw new CustomerException(CustomerException.EVOUCHERCUSTOMERID_MUST_BE_NOT_EMPTY);
        }
        if (StringUtils.isBlank(evoucherDeffId)) {
            throw new CustomerException(CustomerException.EVOUCHER_ID_MUST_BE_NOT_EMPTY);
        }
        if (StringUtils.isBlank(assignmentType)) {
            throw new CustomerException(CustomerException.ASSIGNTYPEID_MUST_BE_NOT_EMPTY);
        }
        Long customerIdLongValue = null;
        try {
            customerIdLongValue = Long.valueOf(customerId);
        } catch (Exception e) {
            throw new CustomerException(CustomerException.INVALID_FORMAT_EVOUCHERCUSTOMERID);
        }
        Customer customer = this.baseDao.findById(Customer.class, customerIdLongValue);
        if (customer == null) {
            throw new CustomerException(CustomerException.INVALID_CUSTOMER_ID);
        }
        if (UserStatus.ACTIVE != customer.getStatus()) {
            throw new CustomerException(CustomerException.Customer_status_is_not_active);
        }

        return businessMessage;
    }

    public Long getCustomeraccountNumberByGarId(Long garId) {

        Long accountNumber = this.baseDao.executeNamedQueryForSingleResult("CustomerGar.getCustomeraccountNumberByGarId",
                Long.class, false, true, new Object[]{garId});

        return accountNumber;

    }

    @Override
    public BusinessMessage validateCutomerExistenceAndStatusByCustId(BusinessMessage businessMessage) throws GeneralFailureException, Exception {
        Long customerId = businessMessage.getPrimarySenderInfo().getCustomerId();
        if (customerId == null) {
            throw new CustomerException(CustomerException.EVOUCHERCUSTOMERID_MUST_BE_NOT_EMPTY);
        }

        Customer customer = this.baseDao.findById(Customer.class, customerId);
        if (customer == null) {
            throw new CustomerException(CustomerException.INVALID_CUSTOMER_ID);
        }
        if (UserStatus.ACTIVE != customer.getStatus()) {
            throw new CustomerException(CustomerException.Customer_status_is_not_active);
        }

        if (businessMessage != null && businessMessage.getDynamicPayload() != null) {
            Payload payload = (Payload) dynamicPayloadTransformer.transform(businessMessage.getDynamicPayload());
            payload.put("customerId", customerId);
        }

        return businessMessage;
    }

    @Override
    public BusinessMessage validateReceiverIsCustomer(BusinessMessage businessMessage) throws GeneralFailureException {
        String queryStr = ServiceQueryEngine.getQueryStringToExecute("validateReceiverIsCustomer", this.getClass(),
                businessMessage.getPrimaryReceiverInfo().getMsisdn());
        Query query = (Query) this.baseDao.executeNativeQuery(queryStr);
        List customer = query.getResultList();
        if (customer.size() > 0) {
            throw new CustomerException(CustomerException.Validate_Receiver_Is_Customer);
        }
        return businessMessage;
    }

    @Override
    public void setCustomerRemoteRegistration(CustomerRemoteRegistration customerRemoteRegistration) {
        this.customerRemoteManager = customerRemoteRegistration;
    }

    @Override
    public CustomerRemoteRegistration getCustomerRemoteRegistration() {
        return this.customerRemoteManager;
    }

    @Override
    public void setGarManager(GarManager garManager) {
        this.garManager = garManager;
    }

    @Override
    public GarManager getGarManager() {
        return this.garManager;
    }

    public BusinessMessage validateAmountForLoan(BusinessMessage businessMessage) throws GeneralFailureException {
        businessMessage.getTransactionInfo().setTransactionAmount(new BigDecimal(0));
        businessMessage.getTransactionInfo().setNarration("0");
        return businessMessage;
    }

    @Override
    public BusinessMessage validateReceiverData(BusinessMessage businessMessage) throws Exception {
        Map<String, String> varMap = new HashMap<>();
        Payload payload = (Payload) dynamicPayloadTransformer.transform(businessMessage.getDynamicPayload());
        int moneyRequestTypeId =  payload.getAttributeAsInteger("moneyRequestTypeId");
        boolean isSplit = splitUtils.isSplit(moneyRequestTypeId);
        ArrayList<Map<String, String>> recipients = (ArrayList<Map<String, String>>) payload.getAttribute("recipientsMobileNumber");

        if (recipients == null)
            throw new GeneralFailureException(GeneralFailureException.NO_DATA_FOUND);


        Map<String, Double> recipientMobileAmounts = recipients.stream()
                .filter(Objects::nonNull)
                .peek( recipient -> {
                    String recipientNumber = recipient.get(RECIPIENT_MOBILE_NUMBER);
                    String recipientAmount = recipient.get(RECIPIENT_AMOUNT);
                    if (recipientNumber == null || recipientAmount == null) {
                        try {
                            throw new CustomerException(CustomerException.INVALID_RECIPIENT_KEY);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                    if (Double.parseDouble(recipientAmount) <= 0){
                        varMap.put(AMOUNT_MUST_BE_GREATER_THAN_ZERO, recipientNumber);
                        try {
                            handleAmountMustBeGreaterThanZero(varMap, isSplit);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                })
                .collect(Collectors.groupingBy(
                        recipientDataMap -> recipientDataMap.get(RECIPIENT_MOBILE_NUMBER),
                        Collectors.summingDouble(recipientDataMap -> Double.parseDouble(recipientDataMap.get(RECIPIENT_AMOUNT)))
                ));


        List<CustomerDto> customerDtos = GetAllCustomersByReceiver(recipientMobileAmounts.keySet());
        Map<String, CustomerDto> mapExistReceivers = customerDtos
                .stream()
                .parallel()
                .collect(Collectors.toMap(
                        CustomerDto::getMsisdn,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        List<MuleMoneyRequestRecipientDTO> muleMoneyRequestRecipientDTOS = recipientMobileAmounts.keySet()
                .stream()
                .map(entry -> {
                    Double recipientAmount = recipientMobileAmounts.get(entry);

//                    if (recipientAmount <= 0){
//                        varMap.put(AMOUNT_MUST_BE_GREATER_THAN_ZERO, entry);
//                        try {
//                            handleAmountMustBeGreaterThanZero(varMap, isSplit);
//                        } catch (Exception e) {
//                            throw new RuntimeException(e);
//                        }
//                    }

                    CustomerDto customerDto = mapExistReceivers.get(entry);

                    if (customerDto == null) {
                        varMap.put(NUMBER_NOT_FOUND, entry);
                        try {
                            handleNotFound(varMap, isSplit);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    } else if (customerDto.getStatus() != UserStatus.ACTIVE) {
                        varMap.put(NUMBER_INACTIVE, entry);
                        try {
                            handleInActive(varMap, isSplit);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                    //todo:add get customer name to function
                    Customer PrimaryReceivercustomer = this.baseDao.findSingleResultByProperty(Customer.class, "msisdn", entry);
                    businessMessage.getPrimaryReceiverInfo().setUserId(String.valueOf(PrimaryReceivercustomer.getCustomerID()));

                    // Concatenate names
                    String ReceiverfullName = String.join(" ",
                            PrimaryReceivercustomer.getFirstName() != null ? PrimaryReceivercustomer.getFirstName() : "",
                            PrimaryReceivercustomer.getMiddleName() != null ? PrimaryReceivercustomer.getMiddleName() : "",
                            PrimaryReceivercustomer.getLastName() != null ? PrimaryReceivercustomer.getLastName() : ""
                    ).replaceAll("\\s+", " ").trim();
                    businessMessage.getPrimaryReceiverInfo().setUserName(ReceiverfullName);
                    businessMessage.getPrimaryReceiverInfo().setMsisdn(PrimaryReceivercustomer.getMsisdn());

                    return MuleMoneyRequestRecipientDTO.builder()
                            .customerID(customerDto.getCustomerId())
                            .amount(recipientAmount)
                            .mobileNumber(customerDto.getMsisdn())
                            .userID(customerDto.getUserId())
                            .fullName(ReceiverfullName)
                            .build();
                })
                .collect(Collectors.toList());

        Customer PrimarySenderCustomer = this.baseDao.findSingleResultByProperty(Customer.class, "msisdn", businessMessage.getPrimarySenderInfo().getMsisdn());
        businessMessage.getPrimarySenderInfo().setUserId(String.valueOf(PrimarySenderCustomer.getCustomerID()));

        String SenderfullName = String.join(" ",
                PrimarySenderCustomer.getFirstName() != null ? PrimarySenderCustomer.getFirstName() : "",
                PrimarySenderCustomer.getMiddleName() != null ? PrimarySenderCustomer.getMiddleName() : "",
                PrimarySenderCustomer.getLastName() != null ? PrimarySenderCustomer.getLastName() : ""
        ).replaceAll("\\s+", " ").trim();
        businessMessage.getPrimarySenderInfo().setUserName(SenderfullName);
        businessMessage.getPrimarySenderInfo().setMsisdn(PrimarySenderCustomer.getMsisdn());

        List<Map<String, Object>> listOfMaps = muleMoneyRequestRecipientDTOS.stream()
                .filter(Objects::nonNull) // Filter out null values
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("customerId", dto.getCustomerID());
                    map.put("amount", dto.getAmount());
                    map.put("mobileNumber", dto.getMobileNumber());
                    map.put("userID", dto.getUserID());
                    map.put("fullName", dto.getFullName());
                    return map;
                })
                .collect(Collectors.toList());
        businessMessage.getDynamicPayload().getPayload().setAttribute("recipientMobileNumbers", listOfMaps);

        return businessMessage;
    }

    private void handleAmountMustBeGreaterThanZero(Map<String, String> varMap, boolean isSplit) throws CustomerException {
        if (isSplit){
            String mobileNumber = varMap.get(AMOUNT_MUST_BE_GREATER_THAN_ZERO);
            String lastFourDigits = mobileNumber.length() > 4 ? mobileNumber.substring(mobileNumber.length() - 4) : mobileNumber;
            varMap.put(CUSTOMER_KEY, lastFourDigits);
            throw new CustomerException(CustomerException.AMOUNT_MUST_BE_GREATER_THAN_ZERO_SPLIT, varMap);
        }else {
            throw new CustomerException(CustomerException.AMOUNT_MUST_BE_GREATER_THAN_ZERO);
        }
    }

    private void handleNotFound(Map<String, String> varMap, boolean isSplit) throws GeneralFailureException {
        if (isSplit){
            String mobileNumber = varMap.get(NUMBER_NOT_FOUND);
            String lastFourDigits = mobileNumber.length() > 4 ? mobileNumber.substring(mobileNumber.length() - 4) : mobileNumber;
            varMap.put(CUSTOMER_KEY, lastFourDigits);
            throw new CustomerException(CustomerException.NUMBER_NOT_FOUND_SPLIT, varMap);
        }else {
            throw new CustomerException(CustomerException.NUMBER_NOT_FOUND);
        }
    }
    private void handleInActive(Map<String, String> varMap, boolean isSplit) throws GeneralFailureException {
        if (isSplit){
            varMap.put(CUSTOMER_KEY, getCustomerNameByMsisdn(varMap.get(NUMBER_INACTIVE)).orElse(THE_RECIPIENT));
            throw new CustomerException(CustomerException.NUMBER_INACTIVE_SPLIT, varMap);
        }else {
            throw new CustomerException(CustomerException.NUMBER_INACTIVE);
        }
    }

    private List<CustomerDto> GetAllCustomersByReceiver(Set<String> recipientMobilNumbers) {
        String jpql = ServiceQueryEngine.getQueryStringToExecute("getCustomerDtos",this.getClass());
        Query query = baseDao.getEntityManager().createQuery(jpql);
        query.setParameter("recipientMobilNumbers", recipientMobilNumbers);

        return query.getResultList();

    }
    public Optional<String> getCustomerNameByMsisdn(String msisdn) {
        try {
            TypedQuery<String> customerNameQuery = this.baseDao.getEntityManager()
                    .createQuery("SELECT CONCAT(COALESCE(c.firstName,''), ' ', COALESCE(c.lastName,'')) FROM Customer c " +
                                    "WHERE c.msisdn = :msisdn",
                            String.class);
            customerNameQuery.setParameter("msisdn", msisdn);
            return Optional.ofNullable(customerNameQuery.getSingleResult());
        }catch (Exception ex){
            ex.printStackTrace();
            return Optional.empty();
        }
    }

    @Override
    public Optional<Long> getCustomerFeeProfile(Long customerId) {
        try {
            TypedQuery<Long> feeProfileQuery = this.baseDao.getEntityManager()
                    .createQuery("SELECT c.feeProfile FROM Customer c " +
                                    "WHERE c.customerID = :customerID",
                            Long.class);
            feeProfileQuery.setParameter("customerID", customerId);
            return Optional.ofNullable(feeProfileQuery.getSingleResult());
        }catch (Exception ex){
            ex.printStackTrace();
            return Optional.empty();
        }
    }

}