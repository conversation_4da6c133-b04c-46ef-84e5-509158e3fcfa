package com.cit.mpaymentapp.service.reward.executionmodemanager.implementation.serviceconditions;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.model.reward.RewardCustomerExecution;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service("minAmount")
public class MinAmount extends RewardServiceCondition {
    @Override
    public RewardServiceConditionContainer isConditionMet(BusinessMessage businessMessage, Long serviceConditionValue, RewardCustomerExecution customerExecution) {
        BigDecimal amount = businessMessage.getParameters().getAttributeAsBigDecimal("amount");
        BigDecimal amountAccumlated = BigDecimal.valueOf(customerExecution.getAmount()).add(amount);

        customerExecution.setAmount(amountAccumlated.longValue());
        RewardServiceConditionContainer rewardServiceConditionContainer = new RewardServiceConditionContainer();
        rewardServiceConditionContainer.setRewardCustomerExecution(customerExecution);
        if(amountAccumlated.longValue() >= serviceConditionValue){
            rewardServiceConditionContainer.setConditionMet(true);
            return rewardServiceConditionContainer;
        }
        rewardServiceConditionContainer.setConditionMet(false);
        return rewardServiceConditionContainer;    }
}
