package com.cit.mpaymentapp.service.salary.disbursement;

import java.io.BufferedInputStream;
import java.sql.Blob;

import org.springframework.beans.factory.annotation.Autowired;

import org.hibernate.Hibernate;
import org.hibernate.Session;
import org.springframework.stereotype.Service;

import com.cit.mpaymentapp.common.salary.disbursement.UploadFileManager;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentpp.model.salary.disbursement.DisbursementFile;



@Service("uploadFileManager")
public class UploadFileManagerImpl implements UploadFileManager{
	
	@Autowired
	IBaseDao baseDao;
	
	@Override
	public void saveDisbursementFile(String fileName, String extention, byte[] payload) {
		DisbursementFile disbursementFile = new DisbursementFile();
		disbursementFile.setGroupId(Long.parseLong(fileName));
		disbursementFile.setFileExtention(extention);
		disbursementFile.setFile(payload);
		baseDao.update(disbursementFile);
	}
	
}
