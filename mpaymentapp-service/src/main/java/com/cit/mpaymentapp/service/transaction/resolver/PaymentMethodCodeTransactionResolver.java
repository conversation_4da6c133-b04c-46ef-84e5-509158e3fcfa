package com.cit.mpaymentapp.service.transaction.resolver;

import com.cit.mpaymentapp.common.transaction.TransactionDefinitionResolver;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.Enums.AccountOwner;
import com.cit.mpaymentapp.model.Enums.OwnerTypeEnum;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.mpaymentapp.model.customer.CustomerGar;
import com.cit.mpaymentapp.model.transaction.PaymentOrder;
import com.cit.mpaymentapp.model.transaction.TransactionDefinitionStep;
import com.cit.mpaymentapp.model.transaction.TransactionInfo;
import com.cit.mpaymentapp.model.transaction.TransactionInfo.EntityInfo;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.CustomerException;
import com.cit.shared.error.exception.GarException;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.shared.error.exception.TransactionException;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.List;

@Service
@AllArgsConstructor(onConstructor = @__({@Autowired, @Lazy}))
public class PaymentMethodCodeTransactionResolver implements TransactionDefinitionResolver {
    private final IBaseDao baseDao;

    @Override
    public PaymentOrder resolve(TransactionDefinitionStep step, TransactionInfo transInfo) throws TransactionException {
        PaymentOrder paymentOrder = new PaymentOrder();
        Object targetAccount = null;
        if (step.getAccountOwner() == AccountOwner.SENDER) {
            targetAccount = getAccount(transInfo.getSender());
            paymentOrder.setFeeProfileId(transInfo.getSender().getFeeProfileId());
            paymentOrder.setRiskProfileId(transInfo.getSender().getRiskProfileId());
        } else if (step.getAccountOwner() == AccountOwner.RECEIVER) {
            targetAccount = getAccount(transInfo.getReceiver());
            paymentOrder.setFeeProfileId(transInfo.getReceiver().getFeeProfileId());
            paymentOrder.setRiskProfileId(transInfo.getReceiver().getRiskProfileId());
        }
        if (targetAccount instanceof CustomerGar) {
            CustomerGar gar = (CustomerGar) targetAccount;
            paymentOrder.setGeneralAccountRef(gar.getAccountId());
            paymentOrder.setWalletShortCode(gar.getCustomer().getWalletShortCode());
            paymentOrder.setCustomerAccount(true);
            paymentOrder.setOwnerType(OwnerTypeEnum.CUSTOMER.ordinal());
        }
        return paymentOrder;
    }

    private Object getAccount(EntityInfo info) throws TransactionException {
        Object returnType;
        String userId = info.getIdentificationKey();
        Long paymentMethodCode = info.getPaymentMethodCode();
        returnType = getSvaGar(paymentMethodCode);
        return returnType;
    }

    private CustomerGar getSvaGar(Long paymentMethodCode) throws TransactionException {
        try {
            if (paymentMethodCode != null) {
                CustomerGar svaGar = getGar(paymentMethodCode);
                if (svaGar != null) {
                    return svaGar;
                } else {
                    throw new GarException(GarException.SVA_GAR_NOT_FOUND);
                }
            } else {
                throw new CustomerException(CustomerException.CUSTOMER_NOT_FOUND_ERROR);
            }
        } catch (GeneralFailureException e) {
            throw new TransactionException(e.getErrorCode());
        }

    }


    public CustomerGar getGar(Long paymentMethodCode) {
        try {
            CustomerGar svaGar = null;
            String nativeQuery = ServiceQueryEngine.getQueryStringToExecute("getGar", this.getClass(), String.valueOf(paymentMethodCode));
            Query query = (Query) baseDao.executeNativeQuery(nativeQuery);
            List<Object> results = query.getResultList();
            if (results != null && results.size() > 0) {
                Object[] result = (Object[]) results.get(0);
                svaGar = new CustomerGar();
                BigDecimal intValue = (BigDecimal) result[0];
                if (intValue != null) svaGar.setAccountId(intValue.longValue());
                intValue = (BigDecimal) result[1];
                Customer customer = new Customer();
                if (intValue != null) customer.setCustomerID(intValue.longValue());
                String walletShortCode = (String) result[2];
                if (walletShortCode != null) {
                    customer.setWalletShortCode(walletShortCode);
                }
                svaGar.setCustomer(customer);
            }
            return svaGar;
        } catch (Exception e) {
            return null;
        }


    }
}
