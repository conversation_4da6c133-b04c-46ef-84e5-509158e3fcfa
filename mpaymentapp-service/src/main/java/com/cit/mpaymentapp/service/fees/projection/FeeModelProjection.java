package com.cit.mpaymentapp.service.fees.projection;

import com.cit.mpaymentapp.model.fees.Duration;
import com.cit.mpaymentapp.model.fees.Event;
import com.cit.mpaymentapp.model.fees.FeeModel;
import com.cit.mpaymentapp.model.service.ServiceConfigMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Enumerated;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class FeeModelProjection {
    private Long id;
    private boolean isTiered;
    @Enumerated
    private FeeModel.FeeTypeEnum feeType;
    private ServiceConfigMapProjection businessServiceType;
    private Event event;
    private Duration duration;

    public long getChargableEntityId() {
        switch (getFeeType()) {
            case SERVICE:
                return getBusinessServiceType().getId();
            case EVENT:
                return getEvent().getId();
            case DURATION:
                return getDuration().getId();
            default:
                return -1;
        }
    }
    public String getName() {
        switch (getFeeType()) {
            case SERVICE:
                return getBusinessServiceType().getName();
            case EVENT:
                return getEvent().getName();
            case DURATION:
                return getDuration().getName();
            default:
                return null;
        }
    }
}
