package com.cit.mpaymentapp.service.agencybanking;

import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;

import mpayment.logging.ServerLoggerInf;

import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.xml.sax.InputSource;

import com.cit.mpaymentapp.common.agencybanking.AgencyBankingManager;
import com.cit.mpaymentapp.common.agencybanking.AgencyBankingManagerLocal;
import com.cit.mpaymentapp.common.agencybanking.AgencyBankingManagerRemote;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.agencybanking.QuickTellerTransaction;
import com.cit.mpaymentapp.model.message.AgencyBankingInfo;
import com.cit.mpaymentapp.model.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;

import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.MimeHeaders;
import jakarta.xml.soap.SOAPBody;
import jakarta.xml.soap.SOAPConnection;
import jakarta.xml.soap.SOAPConnectionFactory;
import jakarta.xml.soap.SOAPElement;
import jakarta.xml.soap.SOAPEnvelope;
import jakarta.xml.soap.SOAPException;
import jakarta.xml.soap.SOAPMessage;
import jakarta.xml.soap.SOAPPart;

@Service
public class AgencyBankingManagerImpl implements AgencyBankingManager {

	@Autowired
	private IBaseDao baseDao;
	
	@Override
	public void deposit(AgencyBankingInfo agencyBankingInfo) throws GeneralFailureException {
		throw new UnsupportedOperationException("Not implemented yet.");
	}

	@Override
	public void withdraw(AgencyBankingInfo agencyBankingInfo) throws GeneralFailureException {
		throw new UnsupportedOperationException("Not implemented yet.");
	}

	@Override
	public Map<String, BigDecimal> showbalance(AgencyBankingInfo agencyBankingInfo) throws GeneralFailureException {
		throw new UnsupportedOperationException("Not implemented yet.");
	}

	@Override
	public void saveQuickTellerTransaction(BusinessMessage businessMessage, String transactionReference, String responseCode, String senderMobile) {
		QuickTellerTransaction quickTellerTransactionHistory = new QuickTellerTransaction();
		quickTellerTransactionHistory.setBusinessMessage(businessMessage);
		quickTellerTransactionHistory.setCreationDate(new Date());
		quickTellerTransactionHistory.setTransactionReference(transactionReference);
		quickTellerTransactionHistory.setResponseCode(responseCode);
		quickTellerTransactionHistory.setSenderMobile(senderMobile);
		baseDao.save(quickTellerTransactionHistory);
	}

	private String getQueryTransactionRes(String url, String xmlParamData) throws SOAPException, Exception {

		StringWriter responseStr = new StringWriter();

		SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
		SOAPConnection soapConnection = soapConnectionFactory.createConnection();
		SOAPMessage soapRequest = createSoapRequest(xmlParamData);
		SOAPMessage soapResponse = soapConnection.call(soapRequest, url);

		TransformerFactory transformerFactory = TransformerFactory.newInstance();
		Transformer transformer = transformerFactory.newTransformer();
		Source sourceContent = soapResponse.getSOAPPart().getContent();
		ServerLoggerInf.SERVICE_LOGGER.info("\n----------SOAP Response-----------");

		StreamResult result = new StreamResult(responseStr);
		transformer.transform(sourceContent, result);

		soapConnection.close();

		return responseStr.toString();

	}

	private SOAPMessage createSoapRequest(String xmlParamData) throws Exception {
		MessageFactory messageFactory = MessageFactory.newInstance();
		SOAPMessage soapMessage = messageFactory.createMessage();
		MimeHeaders mimeHeaders = soapMessage.getMimeHeaders();
		mimeHeaders.setHeader("SOAPAction", "QueryTransaction");
		SOAPPart soapPart = soapMessage.getSOAPPart();
		SOAPEnvelope soapEnvelope = soapPart.getEnvelope();
		soapEnvelope.addNamespaceDeclaration("quic", "http://services.interswitchng.com/quicktellerservice/");
		SOAPBody soapBody = soapEnvelope.getBody();
		SOAPElement soapElement = soapBody.addChildElement("QueryTransaction", "quic");
		SOAPElement xmlParams = soapElement.addChildElement("xmlParams", "quic");
		xmlParams.addTextNode(xmlParamData);
		soapMessage.saveChanges();
		ServerLoggerInf.SERVICE_LOGGER.info("----------SOAP Request------------");
		return soapMessage;
	}
	
	public String queryTransaction(String requestReference) {
		String xmlResponseStr = null;
		try {
			String requestMessage = "<![CDATA[<RequestDetails><RequestReference>" + requestReference + "</RequestReference></RequestDetails>]]>";
			String requestUrl = "https://orion.interswitchng.com/quicktellerservice/quickteller.svc";
			xmlResponseStr = getQueryTransactionRes(requestUrl, requestMessage);
			ServerLoggerInf.SERVICE_LOGGER.info(">>>>Do Transfer Response : \n" + xmlResponseStr);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		String responseCode = "";
		try {
			responseCode = getTrxCode(xmlResponseStr);
			ServerLoggerInf.SERVICE_LOGGER.info(responseCode);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return responseCode;
	}
	
	public String getTrxCode(String xmlDoc) {

		String resCode = null;
		if (xmlDoc == null) {
			return resCode;
		}
		Document doc = convertStringToDocument(xmlDoc);

		if (doc.getChildNodes() != null && doc.getChildNodes().getLength() > 0) {
			Node envelop = doc.getChildNodes().item(0);
			if (envelop.getChildNodes() != null && envelop.getChildNodes().getLength() > 0) {
				Node body = envelop.getChildNodes().item(0);
				if (body.getChildNodes() != null && body.getChildNodes().getLength() > 0) {
					Node queryTransactionResponse = body.getChildNodes().item(0);
					if (queryTransactionResponse.getChildNodes() != null && queryTransactionResponse.getChildNodes().getLength() > 0) {
						Node QueryTransactionResult = queryTransactionResponse.getChildNodes().item(0);
						if (QueryTransactionResult.getTextContent() != null) {
							Document resultContent = convertStringToDocument(QueryTransactionResult.getTextContent());
							if (resultContent != null && resultContent.getElementsByTagName("ResponseCode").getLength() > 0) {
								resCode = resultContent.getElementsByTagName("ResponseCode").item(0).getTextContent();
							}
						}
					}
				}
			}
		}
		return resCode;
	}

	private Document convertStringToDocument(String xmlStr) {
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		DocumentBuilder builder = null;
		try {
			builder = factory.newDocumentBuilder();
			Document doc = builder.parse(new InputSource(new StringReader(xmlStr)));
			return doc;
		} catch (Exception e) {
			ServerLoggerInf.SERVICE_LOGGER.error("", e);
		}
		return null;
	}

	@Override
	public List<QuickTellerTransaction> retreiveQuickTellerTransactions(String transactionReference, String senderMobile, String creationDateFromStr, String creationDateToStr, String statusValue) {

		StringBuilder query = new StringBuilder("select model from QuickTellerTransaction model where 1=1 ");

		if (senderMobile != null && !"".equals(senderMobile)) {
			query.append(" and model.senderMobile Like '%" + senderMobile + "%' ");
		}
		if (transactionReference != null && !transactionReference.equals("")) {
			query.append(" and model.transactionReference='" + transactionReference + "' ");
		}
		if (creationDateFromStr != null && !"".equals(creationDateFromStr)) {
			query.append(" and DATEADD(D, 0, DATEDIFF(D, 0, model.creationDate)) >= '" + creationDateFromStr + "' ");
		}
		if (creationDateToStr != null && !"".equals(creationDateToStr)) {
			query.append(" and DATEADD(D, 0, DATEDIFF(D, 0, model.creationDate)) <= '" + creationDateToStr + "' ");
		}
		if (statusValue != null && !"".equals(statusValue)) {
			query.append(" and model.responseCode = '" + statusValue + "' ");
		}
		return baseDao.executeDynamicQuery(query.toString(), QuickTellerTransaction.class, false, false);
	}

	@Override
	public List<com.cit.mpaymentapp.model.facts.common.Transaction> getMiniStatement(AgencyBankingInfo agencyBankingInfo) throws GeneralFailureException {
		throw new UnsupportedOperationException("Not implemented yet.");
	}

}
