package com.cit.mpaymentapp.service.group.transfers;

import com.cit.mpaymentapp.common.group.GroupPaymentsService;
import com.cit.mpaymentapp.common.message.*;
import com.cit.mpaymentapp.common.projection.TransactionStatusAndErrorProjection;
import com.cit.mpaymentapp.model.dto.PaymentItemUpdateDto;
import com.cit.mpaymentapp.model.dto.ReceiverDto;
import com.cit.mpaymentapp.model.dto.SenderDto;
import com.cit.mpaymentapp.model.transaction.TransactionStatus;
import com.cit.mpaymentapp.service.businessMessage.BusinessMessageManager;
import com.cit.mpaymentapp.service.util.ProjectionsLoaderUtil;
import com.cit.shared.error.exception.BusinessEntityException;
import com.cit.shared.error.exception.CustomerException;
import com.cit.shared.error.exception.GeneralFailureException;
import com.cit.shared.error.exception.TransactionException;
import com.cit.shared.error.status.codes.StatusCodes;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class GroupPaymentsServiceImpl implements GroupPaymentsService {
    public static final String PAYMENT_METHOD_CODE = "paymentMethodCode";
    public static final String USER_TYPE = "userType";
    public static final String GROUP_PAYMENTS_ITEM_ID = "groupPaymentsItemId";
    private final ProjectionsLoaderUtil projectionsLoaderUtil;
    private final BusinessMessageManager businessMessageManager;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public BusinessMessage createGroupPaymentBusinessMessage(SenderDto senderDto, ReceiverDto receiver) throws CustomerException, BusinessEntityException, IOException {
        BusinessMessage newBusinessMessage = new BusinessMessage();
        ServiceInformation serviceInformation = businessMessageManager.loadServiceInfo(receiver.getChildServiceCode(), senderDto.getServiceType());
        newBusinessMessage.setServiceInfo(serviceInformation);
        newBusinessMessage.getHeader().setApiCode(serviceInformation.getApiCode());
        newBusinessMessage.setApiCode(serviceInformation.getApiCode());
        String walletShortCode = senderDto.getWalletShortCode();
        newBusinessMessage.getHeader().setWalletShortCode(walletShortCode);
        newBusinessMessage.getAdditionalParameters().put(USER_TYPE, senderDto.getUserType());
        TransactionInformation trxInfo = businessMessageManager.createTransactionInfo(senderDto.getNarration(), receiver.getReceiverAmount());
        newBusinessMessage.setTransactionInfo(trxInfo);

        if (receiver.getBeneficiaryCustomerId() != null) {
            PartyDetails primaryReceiverInfo = businessMessageManager.createPrimaryReceiverInfo(receiver.getBeneficiaryCustomerId(), walletShortCode);
            newBusinessMessage.setPrimaryReceiverInfo(primaryReceiverInfo);
        }

        businessMessageManager.setDestinationPaymentMethodData(receiver.getAlias(), receiver.getPmParameters(), Long.valueOf(receiver.getPmType()), newBusinessMessage);
        businessMessageManager.setSourcePaymentMethodData(senderDto.getSourcePaymentMethod(), newBusinessMessage);

        PartyDetails primarySenderInfo = businessMessageManager.createPrimarySenderInfo(senderDto.getOwnerType(),
                senderDto.getSenderFeeProfileId(),
                Long.valueOf(senderDto.getSourcePaymentMethod().get(PAYMENT_METHOD_CODE).toString()),
                walletShortCode
        );
        newBusinessMessage.setPrimarySenderInfo(primarySenderInfo);

        WalletInfo walletInfo = businessMessageManager.createWalletInfo(walletShortCode, newBusinessMessage.getDestinationPaymentMethod().getCurrency());
        newBusinessMessage.setWalletInfo(walletInfo);

        if (newBusinessMessage.getSoftFields() == null) {
            newBusinessMessage.setSoftFields(new HashMap<>());
        }
        newBusinessMessage.getSoftFields().put(GROUP_PAYMENTS_ITEM_ID, receiver.getGroupPaymentItemId());
        return newBusinessMessage;
    }

    @Override
    public void checkForErrors(BusinessMessage businessMessage) throws GeneralFailureException {
        if (businessMessage.getStatus() != null &&
                businessMessage.getStatus().getErrorFlag()) {
            log.error("######################################### error while Run Service Steps ########################################");
            log.error("statusCode {}", businessMessage.getStatus().getStatusCode());
            throw new GeneralFailureException(businessMessage.getStatus().getStatusCode());
        }
    }

    @Override
    public PaymentItemUpdateDto createPaymentItemUpdateDto(ReceiverDto receiver, BusinessMessage businessMessage)  {
        PaymentItemUpdateDto paymentItemUpdateDto = new PaymentItemUpdateDto();
        paymentItemUpdateDto.setGroupPaymentItemId(receiver.getGroupPaymentItemId());
        paymentItemUpdateDto.setGroupPaymentId(receiver.getGroupPaymentId());
        if (businessMessage == null ||
                businessMessage.getTransactionInfo() == null) {
            paymentItemUpdateDto.setStatus(TransactionStatus.FAILED.ordinal());
            paymentItemUpdateDto.setErrorCode(TransactionException.TRANSACTION_NOT_EXIST);
            return paymentItemUpdateDto;
        }
        Long transactionId = businessMessage.getTransactionInfo().getTransactionId();

        paymentItemUpdateDto.setErrorCode(businessMessage.getStatus().getStatusCode());
        if (transactionId == null) {
            paymentItemUpdateDto.setStatus(TransactionStatus.FAILED.ordinal());
            paymentItemUpdateDto.setFailedReason(
                    businessMessage.getStatus().getStatusMsg() != null ?
                    businessMessage.getStatus().getStatusMsg() :
                    businessMessage.getResponseTextMessage()
            );
        } else if (businessMessage.getStatus().getErrorFlag()) {
            paymentItemUpdateDto.setTransactionId(transactionId);
            paymentItemUpdateDto.setStatus(TransactionStatus.FAILED.ordinal());
            paymentItemUpdateDto.setFailedReason(businessMessage.getStatus().getStatusMsg());
        } else {
            paymentItemUpdateDto.setTransactionId(transactionId);
            projectionsLoaderUtil.loadTransactionStatusErrorProjection(transactionId)
                    .ifPresentOrElse(
                            projection -> {
                                paymentItemUpdateDto.setStatus(projection.getStatus().ordinal());
                                paymentItemUpdateDto.setFailedReason(projection.getFailedReason());
                            },
                            () -> {
                                paymentItemUpdateDto.setStatus(TransactionStatus.FAILED.ordinal());
                                paymentItemUpdateDto.setFailedReason(businessMessage.getStatus().getStatusMsg());
                            }
                    );
        }
        return paymentItemUpdateDto;
    }

    @Override
    public boolean isValidSender(SenderDto senderDto) {
        return senderDto != null &&
                senderDto.getSourcePaymentMethod() != null &&
                !senderDto.getSourcePaymentMethod().isEmpty() &&
                senderDto.getReceivers() != null &&
                !senderDto.getReceivers().isEmpty();
    }

    @Override
    public boolean isValidaReceiver(ReceiverDto receiver) {
        return receiver != null &&
                receiver.getGroupPaymentItemId() != null &&
                receiver.getReceiverAmount() != null &&
                (receiver.getAlias() != null || receiver.getPmParameters() != null) &&
                receiver.getChildServiceCode() != null;
    }

    @Override
    public List<PaymentItemUpdateDto> checkStatus(List<Long> groupPaymentItemsIds) {
        List<PaymentItemUpdateDto> transactionStatusByGroupPaymentItemsIds = projectionsLoaderUtil.getTransactionStatusByGroupPaymentItemsIds(groupPaymentItemsIds);
        groupPaymentItemsIds.forEach(id -> {
            Optional<PaymentItemUpdateDto> paymentItemUpdateDto = transactionStatusByGroupPaymentItemsIds
                    .stream()
                    .filter(t -> t.getGroupPaymentItemId().equals(id))
                    .findFirst();
            if (paymentItemUpdateDto.isEmpty()) {
                PaymentItemUpdateDto updateDto = new PaymentItemUpdateDto();
                updateDto.setGroupPaymentItemId(id);
                updateDto.setStatus(TransactionStatus.FAILED.ordinal());
                updateDto.setFailedReason("Transaction not found");
                updateDto.setErrorCode(TransactionException.TRANSACTION_NOT_EXIST);
                transactionStatusByGroupPaymentItemsIds.add(updateDto);
            }
    });
        return transactionStatusByGroupPaymentItemsIds;
    }
}
