package com.cit.mpaymentapp.service.ruleengine.rules;

import java.math.BigDecimal;
import java.util.ArrayList;

import com.cit.mpaymentapp.model.Enums.ValueTypeEnum;
import com.cit.mpaymentapp.model.facts.common.Transaction;
import com.cit.mpaymentapp.model.facts.fees.FeesRuleOutputModel;
import com.cit.mpaymentapp.model.facts.fees.ProfileServiceFeeValue;

public class ServiceFeesRule implements FeeRule {
	private BigDecimal amountTransaction = null;
	private BigDecimal feeValue = null;
	private ValueTypeEnum valueType = null;
	private Integer maxCappedValue = null;
	private Double vatValue = null;
	ArrayList<FeesRuleOutputModel> output = new ArrayList<FeesRuleOutputModel>();
	@Override
	public void addFact(Object object) {
		if(object instanceof Transaction){
			Transaction transaction = (Transaction) object;
			amountTransaction =  transaction.getAmount() ;
		}
		if(object instanceof ProfileServiceFeeValue){
			ProfileServiceFeeValue profileServiceFeeValue = (ProfileServiceFeeValue)object;
			feeValue = profileServiceFeeValue.getValue();
			valueType = profileServiceFeeValue.getType();
			maxCappedValue = profileServiceFeeValue.getMaxCappValue()!=0 ? profileServiceFeeValue.getMaxCappValue() : null ;
			vatValue=profileServiceFeeValue.getVatValue();
		}
	}

	@Override
	public ArrayList<FeesRuleOutputModel> calculate() {
		if(amountTransaction != null && feeValue!=null){
			FeesRuleOutputModel feesRuleOutputModel = new FeesRuleOutputModel();
			feesRuleOutputModel.setRuleName("ServiceFeesRule");
			feesRuleOutputModel.setLevel(1);
			switch (valueType) {
			case FIXED: 
				if(vatValue!=null)
				{
					BigDecimal vatPercentageValue = BigDecimal.valueOf(vatValue).divide(new BigDecimal(100));
					BigDecimal vatValue= feeValue.multiply(vatPercentageValue) ;
					feesRuleOutputModel.setValue(feeValue.add(vatValue));
					feesRuleOutputModel.setVatValue(vatValue);
				}
				else
				{
					feesRuleOutputModel.setValue(feeValue);
				}
				break;
			case PERCENTAGE:
				BigDecimal feePercentageValue = feeValue.divide(new BigDecimal(100));
				BigDecimal value= amountTransaction .multiply(feePercentageValue) ;
				if(vatValue!=null)
				{
					BigDecimal vatPercentageValue = BigDecimal.valueOf(vatValue).divide(new BigDecimal(100));
					BigDecimal vatValue= value.multiply(vatPercentageValue) ;
					feesRuleOutputModel.setValue(value.add(vatValue));
					feesRuleOutputModel.setVatValue(vatValue);
				}
				else
				{
					feesRuleOutputModel.setValue(value);
				}
				break;
			}
			if(maxCappedValue!=null && feesRuleOutputModel.getValue().intValue() > maxCappedValue)
				feesRuleOutputModel.setValue(new BigDecimal(maxCappedValue));
			output.add(feesRuleOutputModel);
		}
		
		return output;
	}

}
