package com.cit.mpaymentapp.service.organization;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import javax.interceptor.Interceptors;

import org.springframework.stereotype.Service;

import com.cit.mpaymentapp.common.organization.OrganizationManager;
import com.cit.mpaymentapp.common.organization.OrganizationSettlementReport;
import com.cit.mpaymentapp.common.organization.OrganizationTransaction;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.interceptors.ExceptionInterceptor;
import com.cit.mpaymentapp.model.businessentities.BusinessEntity;
import com.cit.mpaymentapp.model.businessentities.CurrencyExchangeRate;
import com.cit.mpaymentapp.model.organization.Organization;
import com.cit.mpaymentapp.model.transaction.TransactionExecutionSummary;
import com.cit.shared.error.exception.OrganizationException;

@Interceptors(ExceptionInterceptor.class)

@Service
public class OrganizationManagerImpl implements OrganizationManager {

	@Autowired
	IBaseDao baseDao;
	private static final int COBRANDED_WALLET_TYPE_ID = 2;

	public void createOrganization(Organization organization) throws OrganizationException {
		String shortCode = organization.getShortCode().toLowerCase();
		
		if(baseDao.findByProperty(Organization.class, "shortCode", shortCode, false).size() != 0){
			throw new OrganizationException(OrganizationException.CODE_ALREADY_EXIST);
		}
		baseDao.save(organization);
	}

	public void updateOrganization(Organization organization) throws OrganizationException {
		String shortCode = organization.getShortCode().toLowerCase();
		List<Organization> list=baseDao.findByProperty(Organization.class, "shortCode", shortCode, false);
		if(list.size() > 0 && (((Organization)list.get(0)).getOrganizationID().equals(organization.getOrganizationID()))==false){
			throw new OrganizationException(OrganizationException.CODE_ALREADY_EXIST);
		}
		baseDao.update(organization);
	}

	public void removeOrganization(Long organizationId) {

		baseDao.removeById(Organization.class, organizationId);
	}

	public Organization getOrganization(Long organizationId) {

		return baseDao.findById(Organization.class, organizationId);
	}

	public List<Organization> getAllOrganizationList() {

		return baseDao.findAll(Organization.class);
	}

	public boolean isBusinessEntityInCobrandedOrganization(Long businessEntityId) {

		boolean cobranded = false;
		BusinessEntity busEntity = baseDao.findById(BusinessEntity.class,
				businessEntityId);
		if (busEntity.getOrganization().getOrganizationType().getOrganizationTypeID().intValue() == COBRANDED_WALLET_TYPE_ID) {
			cobranded = true;
		}
		return cobranded;
	}

	public OrganizationSettlementReport runOrganizationSettlementReport(Long organizationID,
			Date fromDate, Date toDate) {

		OrganizationSettlementReport report = null;
		double totalAmmount = 0;

		String query = "select model from TransactionExecutionSummary model where "
				+ "model.businessService.businessServiceType.organization.organizationID = "
				+ organizationID
				+ "and model.transactionStartDate >= "
				+ fromDate
				+ "and model.transactionEndDate <= " + toDate;

		List<TransactionExecutionSummary> trxExecutionList = baseDao
				.executeDynamicQuery(query, TransactionExecutionSummary.class,false, false);

		if (trxExecutionList != null && trxExecutionList.size() > 0) {

			for (TransactionExecutionSummary trx : trxExecutionList) {

				report = new OrganizationSettlementReport();
				OrganizationTransaction organizationTrx = new OrganizationTransaction();
				
				organizationTrx.setTransactionAmmount(trx.getTransactionAmount()
						.doubleValue());
				organizationTrx.setTransactionDate(trx.getTransactionEndDate());

				report.addOrganizationTransaction(organizationTrx);
				totalAmmount += trx.getTransactionAmount().doubleValue();
			}
			report.setTotalOrganizationAmount(totalAmmount);
		}

		return report;
	}
	
	private String getCustomerName(String msisdn){
		return "";
	}

	
	public void saveCurrencyExchange(CurrencyExchangeRate currencyExchangeRate) {
		baseDao.save(currencyExchangeRate);
	}
	
	public void deleteCurrencyExchange(CurrencyExchangeRate currencyExchangeRate) {
		baseDao.removeById(CurrencyExchangeRate.class, currencyExchangeRate.getId());
	}
	
	public void updateCurrencyExchange(CurrencyExchangeRate currencyExchangeRate) {
		baseDao.update(currencyExchangeRate);
	}

	
	public List<CurrencyExchangeRate> getCurrencyExchangeRatesByWallet(
			Long walletId) {
		return baseDao.executeDynamicQuery("select model from CurrencyExchangeRate model where model.wallet.businessEntityID="+walletId, CurrencyExchangeRate.class, false, false);
	}

}
