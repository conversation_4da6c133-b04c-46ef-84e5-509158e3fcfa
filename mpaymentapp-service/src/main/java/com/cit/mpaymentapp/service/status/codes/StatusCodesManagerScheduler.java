package com.cit.mpaymentapp.service.status.codes;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.ejb.AccessTimeout;
import org.springframework.beans.factory.annotation.Autowired;
import javax.ejb.Schedule;
import org.springframework.stereotype.Component;
import javax.ejb.Startup;
import javax.ejb.Timeout;
import javax.ejb.Timer;
import javax.persistence.EntityManager;

import org.apache.commons.lang.StringEscapeUtils;
import org.hibernate.Session;
import org.hibernate.Transaction;

import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.status.codes.StatusCodePK;
import com.cit.mpaymentapp.model.status.codes.StatusCodes;
import com.cit.shared.error.status.StatusCodeBean;
import com.cit.shared.error.util.ExceptionResolver;

@Component
public class StatusCodesManagerScheduler{

	@Autowired
	private IBaseDao baseDao;
	
	public static HashMap<String,StatusCodeBean> statusCodesMap = new HashMap<String,StatusCodeBean>();

	public void readStatusCodesFilesAndInsertToDB() {

		System.out.println(
				"-----------------------------------loadStatus()---------------------------------------------------");
		try {
			com.cit.shared.error.status.codes.StatusCodes englishErrorCodes = com.cit.shared.error.status.StatusCodes.getInstance("en").getErrorsHM();
			processStatusCodes(englishErrorCodes,"en");
			com.cit.shared.error.status.codes.StatusCodes frenchErrorCodes = com.cit.shared.error.status.StatusCodes.getInstance("fr").getErrorsHM();
			processStatusCodes(frenchErrorCodes,"fr");
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void processStatusCodes(com.cit.shared.error.status.codes.StatusCodes statusCodes,String lang){
		try {
			System.out.println("-----------------------------------get from ErroHandler---------------------------------------------------");
			System.out.println("-----------------------------------Hash Map Size: " + statusCodes.getStatusCode().size() + " ,Lang:"+ lang +"---------------------------------------------------");
			int scusse = 0;
			int faild = 0;
			
			EntityManager entityManager = baseDao.getEntityManager();
			Session session = entityManager.unwrap(Session.class);
			Set<String> nonDuplicate = new HashSet<String>();
			Set<String> duplicate = new HashSet<String>();
	
			int index = 0;
			String graterThan = "\\s*&\\s*[g,G]\\s*[t,T]\\s*;\\s*";
			String lessThan = "\\s*&\\s*[l,L]\\s*[t,T]\\s*;\\s*";
			for (Object obj : statusCodes.getStatusCode()) {
				index++;
				com.cit.shared.error.status.codes.StatusCodes.StatusCode sCodeBean = (com.cit.shared.error.status.codes.StatusCodes.StatusCode)obj;
				StatusCodes sCodes = new StatusCodes();
				StatusCodePK sCodePK = new StatusCodePK();
	
				System.out.println("-----------------------------------get in session  " + sCodeBean.getCode() + "---------------------------------------------------");
	
				try {
					
					if(sCodeBean.getDescription() == null) {
						sCodeBean.setDescription("Description Is Null");
					}
					if(sCodeBean.getCode() == null) {
						sCodeBean.setCode("Code Is Null");
					}
					
					String description = sCodeBean.getDescription();
					String description2 = description.replaceAll(graterThan, ">");
					String description3 = description2.replaceAll(lessThan, "<");
					sCodeBean.setDescription(description3);
	
					
					String ldescription = sCodeBean.getLocalDescription();
					String ldescription2 = ldescription.replaceAll(graterThan, ">");
					String ldescription3 = ldescription2.replaceAll(lessThan, "<");
					sCodeBean.setLocalDescription(ldescription3);
					
					
					
					
					
					if(!nonDuplicate.contains(sCodeBean.getCode())) {
						nonDuplicate.add(sCodeBean.getCode());
						sCodePK.setCode(sCodeBean.getCode());
						sCodePK.setLanguage(lang);
	
						sCodes.setId(sCodePK);
						sCodes.setSource(sCodeBean.getSource());
						sCodes.setDescription(sCodeBean.getDescription());
						sCodes.setLocalDescription(sCodeBean.getLocalDescription());
						sCodes.setEmailAlert(sCodeBean.isEmailAlert());
						sCodes.setsMSAlert(sCodeBean.isSMSAlert());
						sCodes.setShortDescription(sCodeBean.getShortDescription());
						session.save(sCodes);
	
						System.out.println("-----------------------------------Saved in db   " + sCodeBean.getCode()
								+ "---------------------------------------------------");
						scusse++;
						
					}else {
						duplicate.add(sCodeBean.getCode());
					}
					
				} catch (Exception e) {
					faild++;
					e.printStackTrace();
				}
	
				if (index % 20 == 0) {
					session.flush();
					session.clear();
				}
	
			}
			
			session.flush();
			session.clear();
			session.close();
	
			System.out.println("-----------------------------------Finsh Loop---------------------------------------------------");
			System.out.println("-----------------------------------Success Loop = " + scusse
					+ " ---------------------------------------------------");
			System.out.println("-----------------------------------Faild Loop =" + faild
					+ "  ---------------------------------------------------");
			
			for(String duplicateCodes : duplicate) {
				System.out.println(duplicateCodes);
			}
			System.out.println("-----------------------------------duplicateCodes =    " +  duplicate.size());
			System.out.println("-----------------------------------nonDuplicateCodes =    " +  nonDuplicate.size());
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	
	}
	
	@PostConstruct
	public void loadStatusCodes() {
		List<StatusCodes> statusCodesList = baseDao.findAll(StatusCodes.class);
		
		synchronized(statusCodesMap) {
			
			for(StatusCodes sCode : statusCodesList) {
				
				StatusCodeBean statusCodeBean = new StatusCodeBean();
				
				if(sCode.getId().getCode().equals("VAL06056"))
				{
					System.out.println(sCode.getShortDescription());
				}
				statusCodeBean.setCode(sCode.getId().getCode());
				
				String decription = StringEscapeUtils.escapeHtml(sCode.getDescription());
				decription = StringEscapeUtils.unescapeXml(decription);
				statusCodeBean.setDescription(decription);
				
				String localDescription = StringEscapeUtils.escapeHtml(sCode.getLocalDescription());
				localDescription = StringEscapeUtils.unescapeXml(localDescription);
				statusCodeBean.setLocalDescription(localDescription);
				
				
				statusCodeBean.setMailaiAlert(sCode.isEmailAlert());
				
				String shortDescription = StringEscapeUtils.escapeHtml(sCode.getShortDescription());
				shortDescription = StringEscapeUtils.unescapeXml(shortDescription);
				statusCodeBean.setShortDescription(shortDescription);
				
				statusCodeBean.setSmsAlert(sCode.isSMSAlert());
				statusCodeBean.setSource(sCode.getSource());
				statusCodeBean.setDiplayHint(sCode.getDisplayHint());
				statusCodesMap.put(sCode.getId().toString(),statusCodeBean);
			}
			ExceptionResolver.statusCodesMap = statusCodesMap;
			
		}
		
		
	}
	
	private void scheduledTimeout(final Timer t) {
		loadStatusCodes();
	}

}
