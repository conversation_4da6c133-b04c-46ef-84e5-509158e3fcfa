package com.cit.mpaymentapp.service.ruleengine;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.model.facts.fees.FeesRuleOutputModel;
import com.cit.mpaymentapp.service.ruleengine.rules.FeeRule;
import com.cit.mpaymentapp.service.ruleengine.rules.ServiceFeesRule;
import com.cit.mpaymentapp.service.ruleengine.rules.ServicePercentageRule;

public class FeeMemoryEngine extends MemoryEngine{
	ArrayList<FeeRule> rules = new ArrayList<FeeRule>();
	ArrayList<FeesRuleOutputModel> output = new ArrayList<FeesRuleOutputModel>();
	public FeeMemoryEngine(){
		init();
	}

	@Override
	public void init() {
		ServiceFeesRule serviceFeesRule = new ServiceFeesRule();
		registerRule(serviceFeesRule);
	}

	@Override
	public void addFact(Object factInstance) {
		for(FeeRule rule:rules){
			rule.addFact(factInstance);
		}
	}

	@Override
	public void addFacts(List<Object> factInstances) {
		for(Object obj : factInstances){
			for(FeeRule rule:rules){
				rule.addFact(obj);
			}
		}
	}

	@Override
	public void start() {
		for(FeeRule rule:rules){
			output = rule.calculate();
		}
	}
	private void registerRule(FeeRule rule){
		rules.add(rule);
	}
	private void unregisterRule(FeeRule rule){
		rules.remove(rule);
	}

	@Override
	public ArrayList<FeesRuleOutputModel> getFeesRuleOutput() {
			return output;
	}
}
