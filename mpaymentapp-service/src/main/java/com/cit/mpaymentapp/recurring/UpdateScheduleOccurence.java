package com.cit.mpaymentapp.recurring;


import com.cit.mpaymentapp.common.message.AdditionalParameters;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.serviceorcestration.ServiceStep;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.shared.error.exception.CustomerException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Objects;

@Service("UpdateScheduleOccurence")
@AllArgsConstructor(onConstructor = @__({@Autowired, @Lazy}))
public class UpdateScheduleOccurence implements ServiceStep {

    private final IBaseDao baseDao;

    @Override
    public void execute(BusinessMessage businessMessage) throws Exception {
        Long before = System.currentTimeMillis();
        Long occurenceID = businessMessage.getParameters().getAttributeAsLong("occurrenceId");
        String dateUpdateStr = businessMessage.getParameters().getAttributeAsString("dateUpdate");
        AdditionalParameters additionalParameters = businessMessage.getAdditionalParameters();
        String request = businessMessage.getParameters().getAttributeAsString("request");
        String details = businessMessage.getParameters().getAttributeAsString("details");
        ObjectMapper objectMapper = new ObjectMapper();
        SchedulerRequest requestMap = objectMapper.readValue(request, SchedulerRequest.class);
        HashMap detailsMap = objectMapper.readValue(details, HashMap.class);
        details = getDetails(additionalParameters, detailsMap);
        if (requestMap.getMessage().getPayload().getAttribute("dynamicGroup") == null) {
            additionalParameters.remove("dynamicGroup");
            requestMap.getMessage().getPayload().putAll(additionalParameters);
        } else {
            ArrayList<LinkedHashMap<String, Object>> dynamicGroup = (ArrayList<LinkedHashMap<String, Object>>) requestMap.getMessage().getPayload().getAttribute("dynamicGroup");
            ArrayList<LinkedHashMap<String, Object>> dynamicParam = (ArrayList<LinkedHashMap<String, Object>>) additionalParameters.getAttribute("dynamicGroup");
            for (LinkedHashMap<String, Object> mapCaptured : dynamicGroup) {
                Integer parentGroupId = (Integer) mapCaptured.get("groupId");
                LinkedHashMap<String, Object> inputParams = (LinkedHashMap<String, Object>) mapCaptured.get("inputParameters");
                for (LinkedHashMap<String, Object> caputredAdditional : dynamicParam) {
                    LinkedHashMap<String, Object> additionalInputParams = (LinkedHashMap<String, Object>) caputredAdditional.get("inputParameters");
                    Integer childGroupId = (Integer) caputredAdditional.get("groupId");
                    if (Objects.equals(parentGroupId, childGroupId)) {
                        inputParams.putAll(additionalInputParams);
                    }
                }
                mapCaptured.put("inputParameters", inputParams);
            }
            Long after = System.currentTimeMillis();
            System.out.println("execution time: " + (after - before) / 1000d);
            requestMap.getMessage().getPayload().setAttribute("dynamicGroup", dynamicGroup);
        }

        request = objectMapper.writeValueAsString(requestMap);
        updateQuery(occurenceID, dateUpdateStr, request, details);
    }

    public String getDetails(AdditionalParameters additionalParameters, HashMap detailsMap) throws JsonProcessingException {
        HashMap newAdd = new HashMap();
        newAdd.putAll(additionalParameters);
        newAdd.remove("dynamicGroup");
        detailsMap.putAll(newAdd);
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(detailsMap);
    }

    public void updateQuery(Long occurenceID, String dateUpdateStr, String request, String details) {
        dateUpdateStr = dateUpdateStr.split("\\.")[0];
        String queryUpdate = ServiceQueryEngine.getQueryStringToExecute("UpdateScheduleOccurenceQuery",
                this.getClass(), dateUpdateStr, request, details, String.valueOf(occurenceID));

        baseDao.executeNativeQuery(queryUpdate, true);

    }
}
