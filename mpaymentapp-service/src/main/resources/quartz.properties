org.quartz.scheduler.instanceName = TestScheduler
org.quartz.scheduler.instanceId = AUTO
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 5

org.quartz.jobStore.class: org.quartz.simpl.RAMJobStore

#org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
#org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreCMT
#org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.oracle.OracleDelegate  
#org.quartz.jobStore.dataSource = myDS
#org.quartz.jobStore.isClustered =false 
#org.quartz.jobStore.tablePrefix = QRTZ_

#
# Configure Datasources 
#

#org.quartz.dataSource.myDS.driver = oracle.jdbc.driver.OracleDriver
#org.quartz.dataSource.myDS.URL = jdbc\:oracle\:thin\://@Localhost\:1521\:orcl
#org.quartz.dataSource.myDS.user = mpayment
#org.quartz.dataSource.myDS.password = MpNt277
#org.quartz.dataSource.myDS.maxConnections = 5

# non managed data source
#org.quartz.jobStore.nonManagedTXDataSource=nonMDS
#org.quartz.dataSource.nonMDS.driver = oracle.jdbc.driver.OracleDriver
#org.quartz.dataSource.nonMDS.URL = jdbc\:oracle\:thin\://@Localhost\:1521\:xe
#org.quartz.dataSource.nonMDS.user = root
#org.quartz.dataSource.nonMDS.password = root
#org.quartz.dataSource.nonMDS.maxConnections = 5