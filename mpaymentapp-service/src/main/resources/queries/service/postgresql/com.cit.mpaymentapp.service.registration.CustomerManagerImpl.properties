getRiskProfilesByCustomerType = select * from RISK_PROFILE where RISK_PROFILE_ID in \
  (select risk_id from CUSTOMER_GROUP_RISK where customer_type_id= %1$s)
isCustomerAuthorizedForThisServiceType = select service_type from CUSTOMER,USER_PROFILES,PROFILES_ROLES,ROLES where CUSTOMER.USER_PROFILE=USER_PROFILES.ID \
  and PROFILES_ROLES.PROFILE_ID=USER_PROFILES.ID \
  and PROFILES_ROLES.ROLE_ID=ROLES.ID \
  and CUSTOMER.IDENTIFICATION_KEY='%1$s' and SERVICE_TYPE= %2$s
getFeeProfilesByCustomerType = select * from FEE_PROFILE where FEE_PROFILE_ID in \
  (select fee_id from CUSTOMER_GROUP_FEE where customer_type_id= %1$s)
getCustomerMinimalData = SELECT  CUSTOMER.CUSTOMER_ID, CUSTOMER.CUSTOMERAUTHENTICATIONTYPE, CUSTOMER.STATUS, \
  CUSTOMER.ENABLE_INDEMNITY_PROFILE,CUSTOMER.CUSTOMER_PROFILE_TYPE_ID FROM CUSTOMER WHERE IDENTIFICATION_KEY ='%1$s'
verifyPin = select NO_LOGIN_TRIALS,Customer_ID from CUSTOMER where IDENTIFICATION_KEY ='%1$s'
verifyPinCoreImpl = select NO_LOGIN_TRIALS,Customer_ID from CUSTOMER where IDENTIFICATION_KEY ='%1$s'
verifyPinCoreImplUssd = select NO_LOGIN_TRIALS,Customer_ID from CUSTOMER where IDENTIFICATION_KEY ='%1$s'
validate = select Customer_ID from CUSTOMER where IDENTIFICATION_KEY ='%1$s' AND Activation_Code='%2$s'
isCustomerInNegativeDb = SELECT CUSTOMER.IDENTIFICATION_KEY,CUSTOMER.REASON_OF_CLOSING FROM CUSTOMER INNER JOIN CUSTOMER_GAR ON CUSTOMER.CUSTOMER_ID = CUSTOMER_GAR.CUSTOMER_ID INNER JOIN PAYMENT_METHOD ON CUSTOMER_GAR.PAYMENT_METHOD_TYPE = PAYMENT_METHOD.ID \
  WHERE CUSTOMER.IDENTIFICATION_KEY ='%1$s' \
  AND((CUSTOMER.STATUS = 2)OR(CUSTOMER_GAR.STATUS = 2)AND(PAYMENT_METHOD.TYPE = 2))
getCustomerSWK = select SYMMETRIC_WROKING_KEY from CUSTOMER where IDENTIFICATION_KEY='%1$s'
getCustomerNameByMSISDN = select FIRST_NAME,MIDDLE_NAME,LAST_NAME from CUSTOMER where IDENTIFICATION_KEY ='%1$s'
getCustomerNameByMSISDNBusinessEntity = select Business_Entity_Name from BusinessEntity where MSISDN ='%1$s'
isActive = select STATUS from CUSTOMER where IDENTIFICATION_KEY ='%1$s' AND STATUS= %2$s
validateCustomer = select CUSTOMER_ID , PIN from CUSTOMER where IDENTIFICATION_KEY='%1$s'
getCustomerId = select CUSTOMER_ID from CUSTOMER where IDENTIFICATION_KEY='%1$s'
getCustomerIdByMsisdn = select CUSTOMER_ID from CUSTOMER where MSISDN='%1$s'
checkPaymentMethodBelongsToCustomer = select * from CUSTOMER_GAR where PAYMENT_METHOD_TYPE= %1$s \
  and CUSTOMER_ID= %2$s and STATUS = %3$s and GARID = %4$s
checkPaymentMethodBelongsToCustomerAndSVA = select * from CUSTOMER_GAR where PAYMENT_METHOD_TYPE= %1$s \
  and CUSTOMER_ID= %2$s and STATUS = %3$s
checkPaymentMethodBelongsToCustomerProfile = select * from PAYMENT_METHOD_CUST_PROF_CG where PAYMENT_METHOD_ID = %1$s \
  and CUSTOMER_TYPE_ID = %2$s
getCustomerPIN = select PIN from CUSTOMER where IDENTIFICATION_KEY='%1$s'
verifyPassword = select HandSet_Password from CUSTOMER where IDENTIFICATION_KEY ='%1$s' AND HandSet_Password= '%2$s'
getNewLoginStatus = select new_Login from CUSTOMER where IDENTIFICATION_KEY = '%1$s' AND new_Login= True
isCustomerServiceRestricted = select * from PAYMENT_METHOD_RESTRICTED_SERVICES \
  where Payment_Method_Type_Id= %1$s and Service_Type_ID= %2$s
getAllCustomersIDsUnderBusinessEntity = select customer_id from customer where Registration_Agent in (%1$s)
getAllCustomersUserKeysUnderBusinessEntity = select IDENTIFICATION_KEY from customer where Registration_Agent in (%1$s)
releaseCustomer = UPDATE BLACK_LIST_RECORD SET STATUS=1 WHERE MOBILE_DEVICE_NUMBER='%1$s'
saveBvnEnquiry = INSERT INTO NIBSS_BVN_ENQUIRY_KYC_TABLE VALUES \
  ('%1$s', '%2$s', '%3$s', '%4$s', '%5$s', '%6$s', %7$s,  current_date , %8$s )
checkRiskInfo = SELECT D_SUM as D_SUM, COALESCE(SERVICE_TYPE_ID, '0') as SERVICE_TYPE_ID,USER_ID \
  FROM USER_SERVICE_COUNTERS \
  WHERE  (USER_SERVICE_COUNTERS.KEY='%1$s') \
  AND USER_SERVICE_COUNTERS.PAYMENT_METHOD_TYPE='%2$s' \
  ORDER BY D_SUM DESC
checkRiskProfiles = SELECT PROFILE_CODE, PROFILE_NAME, VALUE, BUSINESS_SERVICE_TYPE, SERVICE_TYPE_NAME FROM RISK_PROFILES
getCustomerBlockingData = SELECT CUSTOMER_ID,REASON_OF_CLOSING,STATUS,BLOCKING_DATE,COOL_OFF_CYCLE_LIMIT,NO_LOGIN_TRIALS,NO_LOGIN_TRIALS_PASSWORD \
  from CUSTOMER where MSISDN='%1$s'
UnblockCustomerForCoolOffService = UPDATE CUSTOMER SET NO_LOGIN_TRIALS=0,NO_LOGIN_TRIALS_PASSWORD=0,REASON_OF_CLOSING=NULL,STATUS=0,BLOCKING_DATE=NULL \
  WHERE MSISDN='%1$s'
getServiceCode = SELECT SERVICE_CODE FROM API_VERICASH_APIS WHERE CODE = '%1$s'
getCustomerByMsisdnAndWalletShortCodeNative = select CUSTOMER_ID from CUSTOMER where msisdn='%1$s' \
  and WALLET_SHORT_CODE='%2$s'
getCustomerBankNumberByIdentificationKeynNative = select CUST_BANK_NUM,customer_id from CUSTOMER where IDENTIFICATION_KEY ='%1$s'
getCustomerNativeByIdentificationKeyAndWalletShortCode = select c.CUSTOMER_ID,c.ACTIVATION_CODE,c.ACTIVATION_DATE,c.IMEI,c.HANDSET_PASSWORD,c.SYMMETRIC_WROKING_KEY, \
  c.NEW_LOGIN,c.REASON_OF_CLOSING,c.NO_LOGIN_TRIALS,c.NO_LOGIN_TRIALS_PASSWORD,c.BLOCKING_DATE, \
  c.LAST_MODIFIED_DATE,c.FIRST_NAME,c.Status,c.BUSINESS_ENTITY_ID,c.CUSTOMERREGISTRATIONTYPE, \
  c.CUSTOMERAUTHENTICATIONTYPE \
  from CUSTOMER c WHERE IDENTIFICATION_KEY='%1$s'
getCustomerAccountNumberForCIF = SELECT cg.ACCOUNT_ID, cg.PAYMENT_METHOD_TYPE \
  FROM CUSTOMER c INNER JOIN CUSTOMER_GAR cg ON c.CUSTOMER_ID = cg.CUSTOMER_ID \
  WHERE c.IDENTIFICATION_KEY = '%1$s' AND cg.PAYMENT_METHOD_TYPE IN(121,102)
getCustomerIndemnityProfileStatus = select ENABLE_INDEMNITY_PROFILE from CUSTOMER where IDENTIFICATION_KEY='%1$s'
updateCustomerNateive = update Customer set status = %1$s, ACTIVATION_CODE= %2$s, ACTIVATION_DATE=TO_TIMESTAMP('%3$s', 'YYYY-MM-DD HH24:MI:SS'), \
  IMEI='%4$s', HANDSET_PASSWORD ='%5$s', SYMMETRIC_WROKING_KEY='%6$s', NEW_LOGIN= %7$s, \
  REASON_OF_CLOSING ='%8$s', NO_LOGIN_TRIALS = %9$s, NO_LOGIN_TRIALS_PASSWORD = %10$s ,BLOCKING_DATE = %11$s, \
  FIRST_NAME='%12$s',BUSINESS_ENTITY_ID = %13$s where CUSTOMER_ID = %14$s
getCustomerIndemnityCreationDate = select Indemnity_Creation_Date from CUSTOMER where IDENTIFICATION_KEY='%1$s'
checkCustomerRegDateForIndemity = SELECT CUSTOMER.CUSTOMER_ID FROM CUSTOMER WHERE CREATION_DATE<=current_date- %1$s \
  AND IDENTIFICATION_KEY='%2$s'
addCustomerPromoProfile = select * from customer_promo_profile where customer_id = %1$s and status ='active'
addCustomerBulkRiskProfile = select * from customer_bulk_risk_profile where customer_id = %1$s and status ='active'
getStuffSchemCodes = select DISTINCT is_stuff from SchmCode_CustomerType where  Schm_Code = '%1$s' and is_stuff =True
checkBulkCutomerProfileStatus = SELECT CUSTOMER_PROFILE_TYPE_ID FROM CUSTOMER WHERE IDENTIFICATION_KEY='%1$s'
loadAllDailyRiskLimits = SELECT beh.SHORT_CODE||'-'||ct.CUSTOMER_GROUP,rvm.VALUE,rp.PROFILE_CODE,rp.PROFILE_NAME,ct.CUSTOMER_REGISTRATION_TYPE \
  FROM RISK_PROFILE rp INNER JOIN CUSTOMERTYPE ct ON rp.RISK_PROFILE_ID=ct.DEFAULT_RISK_ID \
  INNER JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID=ct.BUSINESS_ENTITY_ID \
  INNER JOIN RISK_TYPE rt ON rt.BUSINESS_ENTITY_ID = rp.BUSINESS_ENTITY_ID AND rt.NAME = 'DebitsAmountPerDurationThreshold' \
  INNER JOIN RISK_MODEL rm ON rm.RISK_TYPE_ID = rt.RISK_TYPE_ID \
  INNER JOIN RISK_VALUE_MODEL rvm ON rp.RISK_PROFILE_ID = rvm.RISK_PROFILE_ID AND rvm.RISK_MODEL_ID=rm.RISK_MODEL_ID \
  WHERE ct.CUSTOMER_GROUP IS NOT NULL AND ct.CUSTOMER_REGISTRATION_TYPE IS NOT NULL AND rvm.VALUE IS NOT NULL
getAccountMaxDailyRiskLimit = SELECT rvm.VALUE , ct.NAME FROM RISK_PROFILE rp INNER JOIN CUSTOMERTYPE ct ON \
  rp.RISK_PROFILE_ID=ct.DEFAULT_RISK_ID \
  INNER JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID=ct.BUSINESS_ENTITY_ID \
  INNER JOIN RISK_TYPE rt ON rt.BUSINESS_ENTITY_ID = rp.BUSINESS_ENTITY_ID AND rt.NAME = 'DebitsAmountPerDurationThreshold' \
  INNER JOIN RISK_MODEL rm ON rm.RISK_TYPE_ID = rt.RISK_TYPE_ID \
  INNER JOIN RISK_VALUE_MODEL rvm ON rp.RISK_PROFILE_ID = rvm.RISK_PROFILE_ID AND rvm.RISK_MODEL_ID=rm.RISK_MODEL_ID \
  WHERE ct.CUSTOMER_REGISTRATION_TYPE = '%1$s' AND beh.SHORT_CODE = '%2$s' AND ct.CUSTOMER_GROUP = '%3$s'
isSmeCustomer = SELECT COUNT(*) FROM CUSTOMER_GAR cg INNER JOIN SCHEME_CODE sc ON cg.SCHEME_CODE= sc.SCHEME_CODE \
  WHERE SCHEME_TYPE = %1$s AND cg.CUSTOMER_ID = %2$s
getIndemnityMaxRiskLimit = SELECT rvm.VALUE FROM RISK_PROFILE rp INNER JOIN CUSTOMERTYPE ct ON rp.RISK_PROFILE_ID=ct.DEFAULT_RISK_ID \
  INNER JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID=ct.BUSINESS_ENTITY_ID \
  INNER JOIN RISK_TYPE rt ON rt.BUSINESS_ENTITY_ID = rp.BUSINESS_ENTITY_ID AND rt.NAME = 'DebitsAmountPerDurationThreshold' \
  INNER JOIN RISK_MODEL rm ON rm.RISK_TYPE_ID = rt.RISK_TYPE_ID \
  INNER JOIN RISK_VALUE_MODEL rvm ON rp.RISK_PROFILE_ID = rvm.RISK_PROFILE_ID AND rvm.RISK_MODEL_ID=rm.RISK_MODEL_ID \
  WHERE ct.CUSTOMER_REGISTRATION_TYPE = %1$s AND beh.SHORT_CODE = '%2$s' AND ct.CUSTOMER_GROUP = %3$s
getConsumedAmountForAccount = SELECT D_SUM as D_SUM, coalesce(SERVICE_TYPE_ID, '0') as SERVICE_TYPE_ID,USER_ID \
  FROM USER_SERVICE_COUNTERS \
  WHERE  USER_SERVICE_COUNTERS.KEY='%1$s' \
  ORDER BY D_SUM DESC
getRegularConsumedAmount =  SELECT D_SUM as D_SUM, coalesce(SERVICE_TYPE_ID, '0') as SERVICE_TYPE_ID,USER_ID \
  FROM USER_SERVICE_COUNTERS \
  WHERE  (USER_SERVICE_COUNTERS.KEY='%1$s') AND USER_SERVICE_COUNTERS.PAYMENT_METHOD_TYPE= '%2$s' \
  ORDER BY D_SUM DESC
getSmeConsumedAmount = SELECT D_SUM as D_SUM,coalesce(SERVICE_TYPE_ID, '0') as SERVICE_TYPE_ID,USER_ID \
  FROM USER_SERVICE_COUNTERS \
  WHERE  (USER_SERVICE_COUNTERS.KEY='%1$s') \
  AND USER_SERVICE_COUNTERS.PAYMENT_METHOD_TYPE='%2$s' \
  ORDER BY D_SUM DESC
getSessionSwkById = SELECT SWK FROM CUSTOMER_SESSION WHERE SESSION_ID = %1$s
isValidCustomerSubscriberCategory = SELECT ct.ID from CUSTOMERTYPE ct LEFT JOIN BUSINESS_ENTITY_HIERARCHY bec \
  on bec.BUSINESS_ENTITY_ID= ct.BUSINESS_ENTITY_ID \
  where bec.SHORT_CODE='%1$s' and ct.ID= %2$s
updateMessageWithCustomerSmeData = SELECT cg.SCHEME_CODE , cg.PAYMENT_METHOD_CATEGORY FROM CUSTOMER_GAR cg INNER JOIN PAYMENT_METHOD pm \
  ON cg.PAYMENT_METHOD_TYPE=pm.ID WHERE cg.CUSTOMER_ID =%1$s  AND cg.STATUS IN (0,1,2,4) \
  AND (cg.PAYMENT_METHOD_CATEGORY IN  (%2$s,%3$s,%4$s) OR cg.PAYMENT_METHOD_CATEGORY IS NULL ) AND pm.TYPE != 2
updateSmeBulkCustomerPinAndPassword = UPDATE CUSTOMER SET PIN = '%1$s', HANDSET_PASSWORD = '%2$s'\
  \ WHERE IDENTIFICATION_KEY = '%3$s'
closeFamilyAccountFamilyMemberRecord = UPDATE FAMILY_CUSTOMER SET STATUS = 2 WHERE ID = %1$s
closeFamilyAccountISentSms = UPDATE FAMILY_CUSTOMER SET IS_SENT_SMS = 1 WHERE ID = %1$s
isSmeCustomerBySchemCode = SELECT IS_BRANCH FROM BANK_ACCOUNT_TYPES_MAPPING WHERE KEY='%1$s'
validateSourecPaymentMethodBelongsToSenderCustomerProfile = select CUSTOMER_TYPE_ID from PAYMENT_METHOD_CUST_PROF_CG where \
  PAYMENT_METHOD_ID = %1$s and CUSTOMER_TYPE_ID = %2$s
validateDestinationPaymentMethodBelongsToSenderCustomerProfile = select CUSTOMER_TYPE_ID from DEST_PAYMENT_METHOD_CUST_PROF \
  where PAYMENT_METHOD_ID = %1$s and CUSTOMER_TYPE_ID = %2$s
validateSourecPaymentMethodIsActiveAndBelongsToHisCustomerGar = select GARID from CUSTOMER_GAR where PAYMENT_METHOD_TYPE= %1$s and \
  CUSTOMER_ID= %2$s and STATUS = %3$s and GARID = %4$s
validateSenderPaymentMethodExistAndActive= select GARID from CUSTOMER_GAR where PAYMENT_METHOD_TYPE= %1$s and STATUS = %2$s and GARID = %3$s
validateDestinationPaymentMethodIsActive = select GARID  from CUSTOMER_GAR where PAYMENT_METHOD_TYPE= %1$s and \
  STATUS = %2$s and GARID = %3$s
setCustomerPassword = UPDATE CUSTOMER SET STATUS=1 WHERE CUSTOMER_ID= %1$s
createOrUpdateCustomerSession = DELETE FROM CUSTOMER_SESSION WHERE IDENTIFICATION_KEY = '%1$s'
unlockBulkCustomerProfile = UPDATE CUSTOMER SET CUSTOMER_PROFILE_TYPE_ID=2 WHERE IDENTIFICATION_KEY='%1$s'
updateCustomerCoolOffCyclesLimit = UPDATE CUSTOMER SET COOL_OFF_CYCLE_LIMIT=%1$s WHERE CUSTOMER_ID= %2$s
verifyPinUpdateLoginTrials = UPDATE CUSTOMER SET NO_LOGIN_TRIALS = NO_LOGIN_TRIALS+1 WHERE IDENTIFICATION_KEY='%1$s'
updateEmail = update customer set last_Modified_Date=current_date, email='%1$s' , user_id='%1$s' \
  where identification_Key='%2$s'
updateEmailLastModified = update customer set last_Modified_Date=current_date, email='%1$s' \
  where identification_Key='%2$s'
checkIdentificationId = SELECT * FROM CUSTOMER WHERE USER_IDENTIFICATION_TYPE = %1$s \
  AND USER_PERSONAL_ID_NUMBER = '%2$s'
search_ = select *  from (select rownum r,FIRST_NAME,MIDDLE_NAME,LAST_NAME,customer.CREATION_DATE,LANGUAGE.LANGUAGE,\
  COUNTRY.COUNTRY,customer.STATUS,MSISDN,CUSTOMER_ID,customer.BUSINESS_ENTITY_ID,Business_Entity_Hierarchy.Short_Code \
  from customer,country,LANGUAGE,Business_Entity_Hierarchy \
  where customer.PREFERRED_LANGUAGE=LANGUAGE.LANGUAGE_ID \
  and customer.COUNTRY=country.COUNTRY_ID \
  and customer.BUSINESS_ENTITY_ID=Business_Entity_Hierarchy.Business_Entity_ID 
search_FirstName = AND UPPER(FIRST_NAME) LIKE UPPER('%%%1$s%%') 
search_LastName = AND UPPER(LAST_NAME) LIKE UPPER('%%%1$s%%') 
search_CreationDate = AND TO_CHAR(customer.CREATION_DATE,'YYYY-MM-DD')= '%1$s' 
search_MSISDN = AND msisdn = '%1$s' 
search_Status = AND status = %1$s 
search_RegistrationAgent = AND Registration_Agent = %1$s 
search_CustomerType = AND Customer_Type_ID = %1$s 
search_WalletId = AND customer.BUSINESS_ENTITY_ID = %1$s 
search_MiddleName = AND UPPER(Middle_Name) LIKE UPPER('%%%1$s%%') 
search_DateOfBirth = AND TO_CHAR(customer.Date_Of_Birth,'YYYY-MM-DD') = '%1$s' 
search_Country = AND customer.Country = %1$s 
search_Nationality = = AND Nationality = %1$s 
search_Language = AND Preferred_Language = %1$s 
search_NationalIdType = AND National_Id_Type= %1$s 
search_Alias = AND Alias = '%1$s' 
search_UserId = AND customer.Registration_Number LIKE '%%%1$s%%' 
search_Gender = AND Gender = %1$s 
search_End = ) result 
search_Between = where result.r between %1$s and %2$s

loadSetlementAccountFromEvoucher = SELECT ef.SETTLEMENT_ACCOUNT,ed.NAME FROM EVOUCHER_DEFINITION ed \
  LEFT JOIN EVOUCHER_FINANCIAL ef ON ed.FINANCIAL_ID =ef.ID WHERE ed.ID= %1$s

assignDefaultSegmintationToCustomer_AccountId = SELECT ct.ID,ct.DEFAULT_FEE_ID,ct.DEFAULT_RISK_ID,ct.NAME FROM \
  CUSTOMER_GAR cg INNER JOIN CUSTOMER c ON cg.CUSTOMER_ID = c.CUSTOMER_ID \
  INNER JOIN CUSTOMER_ACCOUNT_TYPE_MAPPING catm ON catm.CUSTOMER_PROFILE_ID = c.CUSTOMER_PROFILE_TYPE_ID \
  AND cg.SCHEME_CODE = catm.SCHEME_CODE \
  INNER JOIN CUSTOMERTYPE ct ON catm.CUSTOMER_GROUP_ID = ct.CUSTOMER_GROUP \
  INNER JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID = ct.BUSINESS_ENTITY_ID \
  WHERE cg.ACCOUNT_ID = '%1$s' AND ct.CUSTOMER_REGISTRATION_TYPE = %2$s \
  AND beh.SHORT_CODE = '%3$s' AND c.IDENTIFICATION_KEY='%4$s'

assignDefaultSegmintationToCustomer_schemeCode = SELECT ct.ID,ct.DEFAULT_FEE_ID,ct.DEFAULT_RISK_ID,ct.NAME FROM CUSTOMERTYPE ct \
  INNER JOIN  CUSTOMER_ACCOUNT_TYPE_MAPPING catm ON catm.CUSTOMER_GROUP_ID = ct.CUSTOMER_GROUP \
  INNER JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID = ct.BUSINESS_ENTITY_ID \
  WHERE catm.CUSTOMER_PROFILE_ID = %1$s AND catm.SCHEME_CODE='%2$s' \
  AND ct.CUSTOMER_REGISTRATION_TYPE = %3$s AND beh.SHORT_CODE = '%4$s'

assignDefaultSegmintationToCustomer_getCutomerTypeSubsubscriberCategory = SELECT ct.ID,ct.DEFAULT_FEE_ID,ct.DEFAULT_RISK_ID,ct.NAME FROM CUSTOMERTYPE ct \
  INNER JOIN  CUSTOMER_ACCOUNT_TYPE_MAPPING catm ON catm.CUSTOMER_GROUP_ID = ct.CUSTOMER_GROUP \
  INNER JOIN BUSINESS_ENTITY_HIERARCHY beh ON beh.BUSINESS_ENTITY_ID = ct.BUSINESS_ENTITY_ID \
  WHERE ct.ID = %1$s

assignDefaultSegmintationToCustomer_getDefaultCustomerTypeNativeQuery = select \"CUSTOMERTYPE\".\"ID\" ,\
  \"CUSTOMERTYPE\".\"DEFAULT_FEE_ID\",\"CUSTOMERTYPE\".\"DEFAULT_RISK_ID\", \
  \"CUSTOMERTYPE\".\"IS_DEFAULT\", \"CUSTOMERTYPE\".\"IS_SELF_REGISTERED\", \
  \"CUSTOMERTYPE\".\"NAME\",\"CUSTOMERTYPE\".\"CUSTOMER_GROUP\",\"CUSTOMERTYPE\".\"OFFERED_SERVICE_TYPE\", \
  \"CUSTOMERTYPE\".\"BUSINESS_ENTITY_ID\",\"CUSTOMERTYPE\".\"CUSTOMER_REGISTRATION_TYPE\"  \
  from \"SchmCode_CustomerType\",\"CUSTOMERTYPE\",\"BUSINESS_ENTITY_HIERARCHY\" \
  where \"SchmCode_CustomerType\".\"CustomerType_Id\" = \"CUSTOMERTYPE\".\"ID\" and \
  \"BUSINESS_ENTITY_HIERARCHY\".\"BUSINESS_ENTITY_ID\" = \"CUSTOMERTYPE\".\"BUSINESS_ENTITY_ID\" \
  and \"CUSTOMERTYPE\".\"IS_DEFAULT\" = 1 \
  and \"BUSINESS_ENTITY_HIERARCHY\".\"SHORT_CODE\" = '%1$s' \
  and \"CUSTOMERTYPE\".\"CUSTOMER_REGISTRATION_TYPE\" = %2$s and \"SchmCode_CustomerType\".\"Schm_Code\" is null

assignDefaultSegmintationToCustomer_getCustomerTypeNativeQuery = select \"CUSTOMERTYPE\".\"ID\" , \
  \"CUSTOMERTYPE\".\"DEFAULT_FEE_ID\",\"CUSTOMERTYPE\".\"DEFAULT_RISK_ID\", \
  \"CUSTOMERTYPE\".\"IS_DEFAULT\",\"CUSTOMERTYPE\".\"IS_SELF_REGISTERED\", \
  \"CUSTOMERTYPE\".\"NAME\",\"CUSTOMERTYPE\".\"CUSTOMER_GROUP\",\"CUSTOMERTYPE\".\"OFFERED_SERVICE_TYPE\", \
  \"CUSTOMERTYPE\".\"BUSINESS_ENTITY_ID\",\"CUSTOMERTYPE\".\"CUSTOMER_REGISTRATION_TYPE\" \
  from \"SchmCode_CustomerType\",\"CUSTOMERTYPE\",\"BUSINESS_ENTITY_HIERARCHY\" \
  where \"SchmCode_CustomerType\".\"CustomerType_Id\" = \"CUSTOMERTYPE\".\"ID\" \
  and \"BUSINESS_ENTITY_HIERARCHY\".\"BUSINESS_ENTITY_ID\" = \"CUSTOMERTYPE\".\"BUSINESS_ENTITY_ID\" \
  and \"BUSINESS_ENTITY_HIERARCHY\".\"SHORT_CODE\" = '%1$s' \
  and \"CUSTOMERTYPE\".\"CUSTOMER_REGISTRATION_TYPE\" = %2$s \
  and \"SchmCode_CustomerType\".\"Schm_Code\" = '%3$s'



clearActivationCode=
setPin=
updateEmail_select=
saveCustomerSWK=
setActivationCode=
completeRegistration=
getReciverVeriCode=
checkRiskInfo_empty=
updateCustomerBankNumber=
saveCustomerimagePath=
getEvoucherAmount=
validateReceiverIsCustomer=
getAccountMaxDailyRiskLimit_customerId=
verifyPinCoreImpl_Update=