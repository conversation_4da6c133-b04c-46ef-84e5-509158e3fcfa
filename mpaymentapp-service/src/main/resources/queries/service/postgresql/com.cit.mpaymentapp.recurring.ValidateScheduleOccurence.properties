validateSchedulerOccurenceSelect = SELECT SCHEDULER.RECURRING_PATTERN, SCHEDULER.START_TIME, \
  SCHEDULER.END_TIME, SCHEDULER.REPEAT_COUNT, SCHEDULER.RECURRING_VALUE, SCHEDULER_OCCURRENCE.DETAILS, \
  SCHEDULER_OCCURRENCE.REQUEST FROM SCHEDULER_OCCURRENCE \
  INNER JOIN SCHEDULER ON SCHEDULER.ID = SCHEDULER_OCCURRENCE.SCHEDULER_ID \
  WHERE SCHEDULER_OCCURRENCE.STATUS = 0 AND SCHEDULER_OCCURRENCE.ID = %1$s
