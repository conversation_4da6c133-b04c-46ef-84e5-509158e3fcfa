executeReward = SELECT GARID FROM CUSTOMER_GAR WHERE  PAYMENT_METHOD_TYPE =842 AND STATUS = 1 AND CUSTOMER_ID = %1$s
executeRewardPoints = SELECT GARID FROM CUSTOMER_GAR WHERE  PAYMENT_METHOD_TYPE =840 AND STATUS = 1 AND CUSTOMER_ID = %1$s
getServiceConfigId = SELECT ID FROM BUSINESS_SERVICE_CONFIG WHERE BUSINESS_SERVICE_TYPE = %1$s

loadserviceInfo= SELECT  bsc.ID , AVA.CODE FROM   API_VERICASH_APIS A<PERSON> left  JOIN SERVICE_CONFIG_MAP scm  ON \
AVA.SERVICE_CODE =SCM.SERVICE_CODE \
LEFT JOIN BUSINESS_SERVICE_CONFIG bsc ON \
bsc.BUSINESS_SERVICE_TYPE =SCM.SERVICE_CODE \
WHERE AVA.service_code=%1$s