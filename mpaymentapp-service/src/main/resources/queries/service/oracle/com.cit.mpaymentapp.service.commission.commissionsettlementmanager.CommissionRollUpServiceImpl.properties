getGarInfo=SELECT GARID ,PAYMENT_METHOD_TYPE, pm.TYPE FROM CUSTOMER_GAR cg LEFT JOIN PAYMENT_METHOD pm \
  ON cg.PAYMENT_METHOD_TYPE = pm.ID WHERE CUSTOMER_ID = '%1$s' AND cg.COMMISION_DEFAULT = 1 and STATUS = 1

getCommissionFlages=SELECT  ba.ACCOUNTSTATUS, beh.ALLOW_COMMISSION ,  beh2.Commission_Type_ID  , beh.PARENT_CUSTOMER_ID , beh.HIERARCHY_LEVEL \
, beh.PARENT_BUSINESS_ENTITY , beh.WALLET , beh2.SHORT_CODE, ba.BUSINESS_ENTITY_ID , beh.SHORT_CODE as CHILD_SHORT_CODE \
  FROM BUSINESS_ACCOUNT ba left join BUSINESS_ENTITY_HIERARCHY beh on  beh.BUSINESS_ENTITY_ID = ba.BUSINESS_ENTITY_ID \
  left join BUSINESS_ENTITY_HIERARCHY beh2 on  beh2.BUSINESS_ENTITY_ID = beh.wallet  \
  WHERE ba.ACCOUNT_TYPE = 2 AND  ba.ACCOUNT_ID = %1$s

getCommissionRollUpMode= SELECT cru.COMMISSION_EXECUTION_MODE \
FROM COMMISSION_ROLL_UP cru \
WHERE cru.BUSINESS_ENTITY_ID = %1$s \
UNION ALL \
SELECT cru.COMMISSION_EXECUTION_MODE FROM COMMISSION_ROLL_UP cru \
WHERE cru.BUSINESS_ENTITY_ID IS NULL AND cru.WALLET_SHORT_CODE = '%2$s' \
AND NOT EXISTS ( \
  SELECT 1 FROM COMMISSION_ROLL_UP \
  WHERE BUSINESS_ENTITY_ID = %1$s )


checkPaymentMethodType=SELECT cru.PAYMENT_METHOD_TYPE  FROM COMMISSION_ROLL_UP cru \
WHERE cru.BUSINESS_ENTITY_ID = '%1$s'   UNION ALL \
SELECT cru.PAYMENT_METHOD_TYPE FROM COMMISSION_ROLL_UP cru \
WHERE cru.BUSINESS_ENTITY_ID IS NULL AND cru.WALLET_SHORT_CODE = '%2$s' \
AND NOT EXISTS ( \
                 SELECT 1 FROM COMMISSION_ROLL_UP \
                 WHERE BUSINESS_ENTITY_ID = '%1$s' \
                )
getConfigForSchedulerCommission=SELECT cru.BUSINESS_ENTITY_ID,cru.WALLET_SHORT_CODE,\
cru.COMMISSION_EXECUTION_MODE,cru.SCHEDULER_FREQUENCY,cru.FREQUENCY_DAY FROM COMMISSION_ROLL_UP cru \
WHERE cru.ID=%1$s

getAccountsIf=select ba.ACCOUNT_ID,ba.BALANCE from BUSINESS_ACCOUNT ba \
where ba.ACCOUNT_TYPE=2 and ba.BUSINESS_ENTITY_ID=%1$s and ba.BALANCE != 0 and ba.BALANCE is not null and ba.ACCOUNTSTATUS=1

getAccountsElse=select ba.ACCOUNT_ID,ba.BALANCE from BUSINESS_ACCOUNT ba \
where ba.ACCOUNT_TYPE=2 and ba.WALLET_SHORT_CODE='%1$s' and ba.BALANCE != 0 and ba.BALANCE is not null and ba.ACCOUNTSTATUS=1

getBusinessEntityShortCode=SELECT beh.SHORT_CODE AS MAIN_SHORT_CODE,beh2.SHORT_CODE AS PARENT_SHORT_CODE FROM BUSINESS_ENTITY_HIERARCHY beh \
left join BUSINESS_ENTITY_HIERARCHY beh2 on  beh2.BUSINESS_ENTITY_ID = beh.wallet \
WHERE beh.BUSINESS_ENTITY_ID =%1$s