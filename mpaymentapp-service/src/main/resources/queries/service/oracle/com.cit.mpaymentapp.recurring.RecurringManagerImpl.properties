createRecurringOccurence = SELECT R.RECURRING_ID,R<PERSON>TITLE,R.CREATION_DATE,R.AMOUNT,R.FREQUENCY_VALUE,R.NUMBER_OF_TIMES,R.NUMBER_OF_EXCUTED,O.STATUS,O<PERSON>TRANSACTION_DATE, \
  R.TRX_PASSED_DATA,R.TRX_TAG,R.TRX_TITLE,R.RECIEVER_NAME,R.RECIEVER_NUMBER,R.NOTIFY_BY,\
  O.OCCURENCE_INDEX,R.FIRST_TRANSACTION_DATE \
  FROM RECURRING R,RECURRING_OCCURENCE O \
  WHERE DELETED <> 1 AND \
  upper(O.STATUS) <> 'DELETED' AND \
  upper(O.STATUS) <> 'CANCELED' AND \
  CUSTOMER_ID = %1$s AND \
  R.RECURRING_ID = O.RECURRING_ID AND \
  TO_DATE (TRANSACTION_DATE) >= TO_DATE ('%2$s', 'yyyy-mm-dd') AND \
  TO_DATE (TRANSACTION_DATE) <= TO_DATE('%3$s', 'yyyy-mm-dd')
retrieveSingleRecurringDetails = SELECT O.STATUS, O.TRANSACTION_DATE, O.OCCURENCE_INDEX \
  FROM RECURRING_OCCURENCE O \
  WHERE O.RECURRING_ID = %1$s \
  AND upper(O.STATUS) <> 'DELETED' \
  ORDER BY TRANSACTION_DATE
retreiveExecutedServiceRequest = SELECT TRANSACTION_REQUEST FROM RECURRING WHERE RECURRING_ID = %1$s
executeRecurring = SELECT R.RECURRING_ID,O.OCCURENCE_ID,R.TRANSACTION_REQUEST \
  FROM RECURRING R,RECURRING_OCCURENCE O \
  WHERE R.RECURRING_ID = O.RECURRING_ID \
  AND upper(O.STATUS) = 'ACTIVE' \
  AND TO_DATE (TRANSACTION_DATE) <= TO_DATE('%1$s', 'yyyy-mm-dd')
updateRecurring = UPDATE RECURRING_OCCURENCE SET STATUS = 'IN_PROGRESS' \
  WHERE upper(STATUS) = 'ACTIVE' \
  AND TO_DATE (TRANSACTION_DATE) <= TO_DATE('%1$s', 'yyyy-mm-dd')
