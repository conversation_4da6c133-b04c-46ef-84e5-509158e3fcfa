<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ValidatePinExpiration.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.registration.login</a> &gt; <span class="el_source">ValidatePinExpiration.java</span></div><h1>ValidatePinExpiration.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.registration.login;

import com.cit.mpaymentapp.common.customer.security.rules.SecurityRulesManager;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.registration.CustomerManager;
import com.cit.mpaymentapp.common.serviceorcestration.ServiceStep;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.query.engine.impl.ServiceQueryEngine;
import com.cit.service.commons.codeMapping.PropertyLoaderInt;
import com.cit.shared.error.exception.CustomerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Query;
import java.util.Date;


@Service(&quot;ValidatePinExpiration&quot;)
<span class="nc" id="L19">public class ValidatePinExpiration implements ServiceStep {</span>

    @Autowired
    SecurityRulesManager securityRulesManager;

    @Autowired
    PropertyLoaderInt propertyLoader;
    @Autowired
    CustomerManager customerManager;
    @Autowired
    private IBaseDao baseDao;

<span class="nc" id="L31">    static String PIN_NEVER_EXPIRE = &quot;.pin.never.expire&quot;;</span>

<span class="nc" id="L33">    private static String PIN_EXPIRES_SOON = &quot;Your pin will expire soon&quot;;</span>

    @Override
    public void execute(BusinessMessage businessMessage) throws Exception {
<span class="nc" id="L37">        checkExpirationDateForPin(businessMessage);</span>
<span class="nc" id="L38">    }</span>
    public BusinessMessage checkExpirationDateForPin(BusinessMessage businessMessage)throws Exception{
<span class="nc" id="L40">        String walletShortCode = businessMessage.getPrimarySenderInfo().getWalletShortCode();</span>
<span class="nc" id="L41">        String customerId= String.valueOf(businessMessage.getPrimarySenderInfo().getCustomerId());</span>
<span class="nc" id="L42">        Boolean pinNeverExpire = Boolean.parseBoolean(propertyLoader.loadProperty(walletShortCode+PIN_NEVER_EXPIRE));</span>
<span class="nc bnc" id="L43" title="All 2 branches missed.">        if (pinNeverExpire)</span>
<span class="nc" id="L44">            return businessMessage;</span>
<span class="nc" id="L45">        Date expirationDate = getExpirationDateFromPinLog(customerId);</span>
<span class="nc bnc" id="L46" title="All 4 branches missed.">        if (expirationDate!=null&amp;&amp;expirationDate.before(new Date()))</span>
<span class="nc" id="L47">            throw new CustomerException(CustomerException.PIN_IS_EXPIRED);</span>

        // Comment for now PIN EXPIRE SOON IMPLEMENTION
//        String pinExpirationReminderKey=&quot;Pin_Expiration_Reminder_Period&quot;;
//        int pinExpirationReminderValue=Integer.parseInt(propertyLoader.loadProperty(walletShortCode+&quot;.&quot;+pinExpirationReminderKey));
//        Date reminderExpiration=securityRulesManager.calcExpirationdate(pinExpirationReminderValue);
//        if (reminderExpiration.equals(expirationDate) || reminderExpiration.after(expirationDate)) {
//            businessMessage.getParameters().put(&quot;isPasswordExpireSoon&quot;, true);
//            businessMessage.getParameters().put(&quot;PasswordExpirationMessage&quot;, PIN_EXPIRES_SOON);
//        }

<span class="nc" id="L58">        return businessMessage;</span>
    }
    public Date getExpirationDateFromPinLog(String customerId){
<span class="nc" id="L61">        Query query = this.baseDao.getEntityManager()</span>
<span class="nc" id="L62">                .createNativeQuery(ServiceQueryEngine.getQueryStringToExecute(&quot;getExpirationDateFromPinLog&quot;,this.getClass()));</span>
<span class="nc" id="L63">        query.setParameter(&quot;customerId&quot;, customerId);</span>
<span class="nc" id="L64">        Date ExpirationDate = (Date) query.getResultList().get(0);</span>
<span class="nc" id="L65">        return ExpirationDate;</span>
    }
   }
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>