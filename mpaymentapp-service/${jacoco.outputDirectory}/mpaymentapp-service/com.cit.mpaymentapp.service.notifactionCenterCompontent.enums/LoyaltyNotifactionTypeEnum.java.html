<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LoyaltyNotifactionTypeEnum.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.notifactionCenterCompontent.enums</a> &gt; <span class="el_source">LoyaltyNotifactionTypeEnum.java</span></div><h1>LoyaltyNotifactionTypeEnum.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.notifactionCenterCompontent.enums;

import lombok.Getter;

<span class="fc" id="L5">@Getter</span>
public enum LoyaltyNotifactionTypeEnum {
<span class="fc" id="L7">    PointsTransferSender(1L),</span>
<span class="fc" id="L8">    PointsTransferReceiver(2L),</span>
<span class="fc" id="L9">    VoucherRedemption(3L),</span>
<span class="fc" id="L10">    CashRedemption(4L),</span>
<span class="fc" id="L11">    ExpirationReminder(5L)</span>
    ;

<span class="fc" id="L14">    final Long value;</span>

<span class="fc" id="L16">    LoyaltyNotifactionTypeEnum(Long value) {</span>
<span class="fc" id="L17">        this.value = value;</span>
<span class="fc" id="L18">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>