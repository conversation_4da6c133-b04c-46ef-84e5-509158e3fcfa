<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.cit.mpaymentapp.service.fees.calculateFees</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <span class="el_package">com.cit.mpaymentapp.service.fees.calculateFees</span></div><h1>com.cit.mpaymentapp.service.fees.calculateFees</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">438 of 636</td><td class="ctr2">31%</td><td class="bar">36 of 44</td><td class="ctr2">18%</td><td class="ctr1">47</td><td class="ctr2">60</td><td class="ctr1">111</td><td class="ctr2">159</td><td class="ctr1">28</td><td class="ctr2">38</td><td class="ctr1">2</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a0"><a href="CalculateFees.java.html" class="el_source">CalculateFees.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="356" alt="356"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">29</td><td class="ctr2" id="g0">29</td><td class="ctr1" id="h0">81</td><td class="ctr2" id="i0">81</td><td class="ctr1" id="j1">12</td><td class="ctr2" id="k1">12</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="FeeParameters.java.html" class="el_source">FeeParameters.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="66" alt="66"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">14</td><td class="ctr2" id="g2">14</td><td class="ctr1" id="h1">28</td><td class="ctr2" id="i2">28</td><td class="ctr1" id="j0">14</td><td class="ctr2" id="k0">14</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="CalculateFeesForGroup.java.html" class="el_source">CalculateFeesForGroup.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="66" height="10" title="198" alt="198"/></td><td class="ctr2" id="c0">92%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">80%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g1">17</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i1">50</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k2">12</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>