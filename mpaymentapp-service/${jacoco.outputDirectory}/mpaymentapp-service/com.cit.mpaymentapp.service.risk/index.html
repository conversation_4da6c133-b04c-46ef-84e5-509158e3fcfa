<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.cit.mpaymentapp.service.risk</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <span class="el_package">com.cit.mpaymentapp.service.risk</span></div><h1>com.cit.mpaymentapp.service.risk</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,381 of 2,381</td><td class="ctr2">0%</td><td class="bar">199 of 199</td><td class="ctr2">0%</td><td class="ctr1">168</td><td class="ctr2">168</td><td class="ctr1">492</td><td class="ctr2">492</td><td class="ctr1">67</td><td class="ctr2">67</td><td class="ctr1">4</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a3"><a href="RiskServiceImpl.html" class="el_class">RiskServiceImpl</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="1,542" alt="1,542"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="122" alt="122"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">110</td><td class="ctr2" id="g0">110</td><td class="ctr1" id="h0">290</td><td class="ctr2" id="i0">290</td><td class="ctr1" id="j0">48</td><td class="ctr2" id="k0">48</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="RiskRulesManager.html" class="el_class">RiskRulesManager</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="383" alt="383"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="28" alt="28"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">26</td><td class="ctr2" id="g1">26</td><td class="ctr1" id="h1">101</td><td class="ctr2" id="i1">101</td><td class="ctr1" id="j1">12</td><td class="ctr2" id="k1">12</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="RiskCountersServiceImpl.html" class="el_class">RiskCountersServiceImpl</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="262" alt="262"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="29" alt="29"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">18</td><td class="ctr2" id="g2">18</td><td class="ctr1" id="h2">58</td><td class="ctr2" id="i2">58</td><td class="ctr1" id="j3">3</td><td class="ctr2" id="k3">3</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="RiskInheritanceManager.html" class="el_class">RiskInheritanceManager</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="194" alt="194"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="20" alt="20"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">14</td><td class="ctr2" id="g3">14</td><td class="ctr1" id="h3">43</td><td class="ctr2" id="i3">43</td><td class="ctr1" id="j2">4</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>