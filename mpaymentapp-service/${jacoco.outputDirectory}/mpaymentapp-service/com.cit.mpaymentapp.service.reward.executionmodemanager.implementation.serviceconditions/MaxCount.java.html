<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MaxCount.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.reward.executionmodemanager.implementation.serviceconditions</a> &gt; <span class="el_source">MaxCount.java</span></div><h1>MaxCount.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.reward.executionmodemanager.implementation.serviceconditions;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.model.reward.RewardCustomerExecution;
import com.cit.shared.error.exception.GeneralFailureException;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service(&quot;maxCount&quot;)
<span class="nc" id="L11">public class MaxCount extends RewardServiceCondition {</span>
    @Override
    public RewardServiceConditionContainer isConditionMet(BusinessMessage businessMessage, Long serviceConditionValue, RewardCustomerExecution customerExecution) throws IOException, GeneralFailureException {
<span class="nc" id="L14">        String rewardServiceCode = (String) businessMessage.getParameters().get(&quot;rewardServiceCode&quot;);</span>
<span class="nc" id="L15">        Integer count = customerExecution.getCount();</span>
<span class="nc" id="L16">        count = getCountFromQuery(businessMessage,rewardServiceCode,count);</span>
<span class="nc" id="L17">        customerExecution.setCount(count);</span>
<span class="nc" id="L18">        RewardServiceConditionContainer rewardServiceConditionContainer = new RewardServiceConditionContainer();</span>
<span class="nc" id="L19">        rewardServiceConditionContainer.setRewardCustomerExecution(customerExecution);</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">        if(count &lt;= serviceConditionValue){</span>
<span class="nc" id="L21">            rewardServiceConditionContainer.setConditionMet(true);</span>
<span class="nc" id="L22">            return rewardServiceConditionContainer;</span>
        }
<span class="nc" id="L24">        rewardServiceConditionContainer.setConditionMet(false);</span>
<span class="nc" id="L25">        return rewardServiceConditionContainer;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>