<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OpenBankAccountManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.html" class="el_package">com.cit.mpaymentapp.service.openBankAccount</a> &gt; <span class="el_class">OpenBankAccountManager</span></div><h1>OpenBankAccountManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">37 of 557</td><td class="ctr2">93%</td><td class="bar">12 of 66</td><td class="ctr2">81%</td><td class="ctr1">12</td><td class="ctr2">42</td><td class="ctr1">11</td><td class="ctr2">117</td><td class="ctr1">0</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a1"><a href="OpenBankAccountManager.java.html#L178" class="el_method">loadDynamicInputParametersOpenBankAccont(List, BusinessMessage)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="30" alt="30"/><img src="../jacoco-resources/greenbar.gif" width="77" height="10" title="74" alt="74"/></td><td class="ctr2" id="c8">71%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">66%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i0">25</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="OpenBankAccountManager.java.html#L219" class="el_method">isParameterUnique(String, Long, Long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="46" alt="46"/></td><td class="ctr2" id="c7">88%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">60%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="OpenBankAccountManager.java.html#L129" class="el_method">loadOpenBankAccountSourcePaymentMethodInputParametersMap(BusinessMessage)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="118" height="10" title="113" alt="113"/></td><td class="ctr2" id="c6">99%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">66%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">25</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="OpenBankAccountManager.java.html#L90" class="el_method">validatePaymentMethodParameters(BusinessMessage)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="96" height="10" title="92" alt="92"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">19</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="OpenBankAccountManager.java.html#L39" class="el_method">validateRequestDataAndPrepareForIntegration(BusinessMessage)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="73" alt="73"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">13</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a8"><a href="OpenBankAccountManager.java.html#L75" class="el_method">validateThirdPartyIntegrationResponse(BusinessMessage)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="46" alt="46"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="OpenBankAccountManager.java.html#L61" class="el_method">validateAliasIntegrationResponse(BusinessMessage)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="37" alt="37"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="OpenBankAccountManager.java.html#L238" class="el_method">removeUnstorableParameters(BusinessMessage, List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="20" alt="20"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a3"><a href="OpenBankAccountManager.java.html#L25" class="el_method">OpenBankAccountManager()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="19" alt="19"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.************</span></div></body></html>