<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WalletsAccountManagerImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.wallet</a> &gt; <span class="el_source">WalletsAccountManagerImpl.java</span></div><h1>WalletsAccountManagerImpl.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.wallet;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;

import com.cit.mpaymentapp.common.wallet.LoadWalletsAccountTypeLocal;
import com.cit.mpaymentapp.common.wallet.WalletsAccountManagerLocal;
import com.cit.shared.error.exception.TransactionException;

@Service(&quot;walletsAccountManagerImpl&quot;)
<span class="nc" id="L14">public class WalletsAccountManagerImpl implements WalletsAccountManagerLocal {</span>
//    @Autowired
//    protected LoadWalletsAccountTypeLocal loadWalletAccountTypeLocal;

    public boolean isWalletAccountTypeSVA(String walletIdORshotCode) throws Exception {

////        if (LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP == null
////                || LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP.isEmpty()) {
////            loadWalletAccountTypeLocal.getAllWalletsAccountsTypeStartUp();
////        }
////        if (walletIdORshotCode == null || walletIdORshotCode.trim().isEmpty()) {
////
////            throw new TransactionException(TransactionException.WALLETS_ID_OR_SHORT_CODE_EMPTY);
////        }
////
////        if (LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP == null
////                || LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP.isEmpty()) {
////            throw new TransactionException(TransactionException.WALLETS_MAP_NOT_LOADED);
////        }
////        Boolean isWalletSVA = false;
////        if (!LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP.containsKey(walletIdORshotCode)) {
////            loadWalletAccountTypeRunTime(walletIdORshotCode);
////        }
////        isWalletSVA = LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP.get(walletIdORshotCode);
////
////        if(isWalletSVA==null){
////            isWalletSVA=false;
////        }
//
//        return isWalletSVA;
<span class="nc" id="L44">        return true;</span>
    }

    public synchronized void loadWalletAccountTypeRunTime(String walletIdORshotCode) throws TransactionException {
//        if (LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP.containsKey(walletIdORshotCode)) {
//            return;
//        }
//        loadWalletAccountTypeLocal.getWalletAccountTypeByWallertIdOrShortCodeInRunTime(walletIdORshotCode);
//
//        if (!LoadWalletsAccountTypeImpl.LOAD_Wallets_ACCOUNT_TYPE_MAP.containsKey(walletIdORshotCode)) {
//            throw new TransactionException(TransactionException.WALLETS_ID_OR_SHORT_CODE_NOT_LOADED);
//        }
<span class="nc" id="L56">    }</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.************</span></div></body></html>