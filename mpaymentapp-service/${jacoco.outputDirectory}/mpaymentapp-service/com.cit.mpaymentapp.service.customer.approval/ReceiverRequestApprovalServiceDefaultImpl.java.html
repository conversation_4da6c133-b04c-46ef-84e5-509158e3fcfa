<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReceiverRequestApprovalServiceDefaultImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.customer.approval</a> &gt; <span class="el_source">ReceiverRequestApprovalServiceDefaultImpl.java</span></div><h1>ReceiverRequestApprovalServiceDefaultImpl.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.customer.approval;

import com.cit.mpaymentapp.common.gar.GarManager;
import com.cit.mpaymentapp.common.registration.CustomerManager;
import com.cit.mpaymentapp.common.utility.general.lookups.GeneralLookupsManager;
import com.cit.mpaymentapp.dao.base.IBaseDao;
import com.cit.mpaymentapp.model.customer.CustomerGar;
import com.cit.mpaymentapp.model.customer.approval.ReceiverRequestApproval;
import com.cit.shared.error.exception.CustomerException;
import com.cit.shared.error.exception.GarException;
import com.cit.vericash.backend.commons.dynamicinputparameters.mapping.DynamicPayloadTransformer;
import com.cit.vericash.backend.commons.dynamicpayload.DynamicPayload;
import com.cit.vericash.backend.commons.dynamicpayload.Payload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.cit.mpaymentapp.model.Constants.*;

@Transactional
<span class="fc" id="L21">@Slf4j</span>
@Service
public class ReceiverRequestApprovalServiceDefaultImpl extends ReceiverRequestApprovalServiceTemplate {

    public ReceiverRequestApprovalServiceDefaultImpl(IBaseDao baseDao,
                                                     GeneralLookupsManager generalLookupsManager,
                                                     DynamicPayloadTransformer dynamicPayloadTransformer,
                                                     CustomerManager customerManager,
                                                     GarManager garManager) {
<span class="fc" id="L30">        super(baseDao, generalLookupsManager, dynamicPayloadTransformer, customerManager, garManager);</span>
<span class="fc" id="L31">    }</span>

    @Override
    protected ReceiverRequestApproval createReceiverRequestApproval(DynamicPayload dynamicPayload) throws Exception  {
<span class="fc" id="L35">        Payload payload = dynamicPayload.getPayload();</span>
<span class="fc" id="L36">        String timeOut = generalLookupsManager.getLookupValueByKeyWithException(TIMEOUT);</span>
        long senderCustomerId;
<span class="pc bpc" id="L38" title="1 of 2 branches missed.">        if (payload.get(SENDER_CUSTOMER_ID) != null) {</span>
<span class="nc" id="L39">            senderCustomerId = Long.parseLong(payload.get(SENDER_CUSTOMER_ID).toString());</span>
<span class="pc bpc" id="L40" title="1 of 2 branches missed.">        } else if (payload.get(SENDER_MOBILE_NUMBER) != null){</span>
<span class="nc" id="L41">            senderCustomerId = customerManager.getCustomerIdByMsisdn(payload.get(SENDER_MOBILE_NUMBER).toString());</span>
<span class="pc bpc" id="L42" title="1 of 2 branches missed.">        } else if (payload.get(SENDER_GAR_ID) != null) {</span>
<span class="fc" id="L43">            CustomerGar senderCustomerGar = garManager.getCustomerIdAndCurrency(payload.get(SENDER_GAR_ID).toString());</span>
<span class="pc bpc" id="L44" title="1 of 2 branches missed.">            if (senderCustomerGar == null) {</span>
<span class="nc" id="L45">                throw new GarException(GarException.NOT_REGISTERED_GAR);</span>
            }
<span class="fc" id="L47">            senderCustomerId = senderCustomerGar.getCustomer().getCustomerID();</span>
<span class="fc" id="L48">        } else {</span>
<span class="nc" id="L49">            throw new CustomerException(CustomerException.INVALID_CUSTOMER_ID);</span>
        }
<span class="fc" id="L51">        ReceiverRequestApproval receiverRequestApproval = new ReceiverRequestApproval(payload);</span>
<span class="fc" id="L52">        receiverRequestApproval.setExpiryDate(receiverRequestApproval.getCreationDate().plusSeconds(Integer.parseInt(timeOut)));</span>
<span class="pc bpc" id="L53" title="1 of 2 branches missed.">        if (payload.get(RECEIVER_GAR_ID) == null) {</span>
<span class="nc" id="L54">            throw new GarException(GarException.NOT_REGISTERED_GAR);</span>
        }
<span class="fc" id="L56">        CustomerGar receiverCustomerGar = garManager.getCustomerIdAndCurrency(payload.get(RECEIVER_GAR_ID).toString());</span>
<span class="pc bpc" id="L57" title="1 of 2 branches missed.">        if (receiverCustomerGar == null) {</span>
<span class="nc" id="L58">            throw new GarException(GarException.NOT_REGISTERED_GAR);</span>
        }
<span class="fc" id="L60">        receiverRequestApproval.setReceiverCustomerId(receiverCustomerGar.getCustomer().getCustomerID());</span>
<span class="fc" id="L61">        receiverRequestApproval.setSenderCustomerId(senderCustomerId);</span>
<span class="fc" id="L62">        receiverRequestApproval.setCorrelationID(payload.getAttributeAsString(CORRELATION_ID));</span>
<span class="fc" id="L63">        dynamicPayload.getPayload().put(CURRENCY, receiverCustomerGar.getCurrency());</span>
<span class="fc" id="L64">        return receiverRequestApproval;</span>
    }

    @Override
    protected void validate(DynamicPayload dynamicPayload) throws Exception {

<span class="nc" id="L70">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>