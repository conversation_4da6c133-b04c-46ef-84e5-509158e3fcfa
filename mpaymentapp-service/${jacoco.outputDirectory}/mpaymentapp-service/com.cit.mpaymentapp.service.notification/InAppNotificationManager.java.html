<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InAppNotificationManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.notification</a> &gt; <span class="el_source">InAppNotificationManager.java</span></div><h1>InAppNotificationManager.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.notification;

import com.cit.mpaymentapp.common.notification.InAppNotification;
import com.cit.mpaymentapp.common.notification.NotificationManagerLocal;
import com.cit.mpaymentapp.common.registration.CustomerManager;
import com.cit.mpaymentapp.model.customer.Customer;
import com.cit.mpaymentapp.model.multi.devices.CustomerDevices;
import com.cit.service.commons.codeMapping.PropertyLoaderInt;
import com.cit.shared.error.exception.CustomerException;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;
import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;


@Service(&quot;inAppNotificationManager&quot;)
<span class="nc bnc" id="L32" title="All 6 branches missed.">@RequiredArgsConstructor(onConstructor = @__({@Autowired,@Lazy}))</span>
public class InAppNotificationManager implements InAppNotification{

    @NonNull
    @Qualifier(&quot;customerManager&quot;)
	private CustomerManager customerManager;
    @NonNull
    @Qualifier(&quot;notificationManager&quot;)
	private final NotificationManagerLocal notificationManager;
    @NonNull
    @Qualifier(&quot;propertyLoaderBean&quot;)
	private final PropertyLoaderInt propertyLoader;


	@Override
    public void createAndPushNotification(String customerIds, String msgTitle, String msgContent) {
<span class="nc" id="L48">        Data dataObj= new Data();</span>
<span class="nc" id="L49">        dataObj.setTitle(msgTitle);</span>
<span class="nc" id="L50">        dataObj.setMessage(msgContent);</span>

<span class="nc" id="L52">        Data dataObjIOS= new Data();</span>
<span class="nc" id="L53">        dataObjIOS.setTitle(msgTitle);</span>
<span class="nc" id="L54">        String isoMsgContent= msgContent.replace(&quot;&lt;br&gt;&quot;, &quot;\\n&quot;);</span>
<span class="nc" id="L55">        dataObjIOS.setMessage(isoMsgContent);</span>

<span class="nc" id="L57">        CustomerNotificationMessage iosCustomerNotificationMsg = createCustomerIOSPlatormRequest(customerIds, dataObjIOS, false);</span>
<span class="nc" id="L58">        CustomerNotificationMessage androidCustomerNotificationMsg = createCustomerAndroidPlatormRequest(customerIds, dataObj, false);</span>
<span class="nc bnc" id="L59" title="All 6 branches missed.">        if(androidCustomerNotificationMsg.getPlatforms()!=null &amp;&amp; !androidCustomerNotificationMsg.getPlatforms().isEmpty() &amp;&amp; !androidCustomerNotificationMsg.getPlatforms().get(0).getRegistrationIdsList().isEmpty()) {</span>
            try {
<span class="nc" id="L61">                pushNotification(androidCustomerNotificationMsg);</span>
<span class="nc" id="L62">            }catch(Exception e) {</span>
<span class="nc" id="L63">                e.printStackTrace();</span>
<span class="nc" id="L64">            }</span>
        }
<span class="nc bnc" id="L66" title="All 6 branches missed.">        if(iosCustomerNotificationMsg.getPlatforms()!=null &amp;&amp; !iosCustomerNotificationMsg.getPlatforms().isEmpty() &amp;&amp; !iosCustomerNotificationMsg.getPlatforms().get(0).getRegistrationIdsList().isEmpty()) {</span>
            try {
<span class="nc" id="L68">                pushNotification(iosCustomerNotificationMsg);</span>
<span class="nc" id="L69">            }catch(Exception e) {</span>
<span class="nc" id="L70">                e.printStackTrace();</span>
<span class="nc" id="L71">            }</span>
        }
<span class="nc" id="L73">    }</span>
    CustomerNotificationMessage createCustomerAndroidPlatormRequest(String customerIds, Data data, boolean inboxView) {

<span class="nc" id="L76">        List&lt;CustomerDevices&gt; customerDevicesList= notificationManager.getAllCustomerDevices(customerIds);</span>

<span class="nc" id="L78">        List&lt;CustomerDevices&gt; androidCustomerDevicesList= new ArrayList&lt;CustomerDevices&gt;();</span>
<span class="nc" id="L79">        List&lt;CustomerDevices&gt; iosCustomerDevicesList= new ArrayList&lt;CustomerDevices&gt;();</span>

<span class="nc bnc" id="L81" title="All 4 branches missed.">        if(customerDevicesList!=null &amp;&amp; customerDevicesList.size()&gt;0) {</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">            for(CustomerDevices customerDevice: customerDevicesList) {</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">                if(&quot;Android&quot;.equalsIgnoreCase(customerDevice.getOs())) {</span>
<span class="nc" id="L84">                    androidCustomerDevicesList.add(customerDevice);</span>
                }
<span class="nc bnc" id="L86" title="All 2 branches missed.">                else if(&quot;iOS&quot;.equalsIgnoreCase(customerDevice.getOs()))</span>
<span class="nc" id="L87">                    iosCustomerDevicesList.add(customerDevice);</span>
<span class="nc" id="L88">            }</span>
        }


<span class="nc" id="L92">        ArrayList&lt;DevicePlatform&gt; platformList= new ArrayList&lt;DevicePlatform&gt;();</span>

<span class="nc bnc" id="L94" title="All 4 branches missed.">        if(androidCustomerDevicesList!=null &amp;&amp; androidCustomerDevicesList.size()&gt;0) {</span>
<span class="nc" id="L95">            DevicePlatform devicePlatform= new DevicePlatform();</span>
<span class="nc" id="L96">            devicePlatform.setPlatformName(&quot;android&quot;);</span>
<span class="nc" id="L97">            ArrayList&lt;String&gt; registrationIdsList= new ArrayList&lt;String&gt;();</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">            for(CustomerDevices customerDevice: androidCustomerDevicesList) {</span>
<span class="nc bnc" id="L99" title="All 4 branches missed.">                if(customerDevice!=null &amp;&amp; customerDevice.getRegistrationId()!=null)</span>
<span class="nc" id="L100">                    registrationIdsList.add(customerDevice.getRegistrationId());</span>
<span class="nc" id="L101">            }</span>
<span class="nc" id="L102">            devicePlatform.setRegistrationIdsList(registrationIdsList);</span>
<span class="nc" id="L103">            platformList.add(devicePlatform);</span>
        }

<span class="nc" id="L106">        CustomerNotificationMessage customerNotificationMessage= new CustomerNotificationMessage();</span>

<span class="nc bnc" id="L108" title="All 2 branches missed.">        if(!inboxView)</span>
<span class="nc" id="L109">            data.setNavigateTo(&quot;none&quot;);</span>
<span class="nc" id="L110">        customerNotificationMessage.setData(data);</span>
<span class="nc" id="L111">        customerNotificationMessage.setPlatforms(platformList);</span>

<span class="nc" id="L113">        return customerNotificationMessage;</span>
    }
    CustomerNotificationMessage createCustomerIOSPlatormRequest(String customerIds, Data data, boolean inboxView) {

<span class="nc" id="L117">        List&lt;CustomerDevices&gt; customerDevicesList= notificationManager.getAllCustomerDevices(customerIds);</span>

<span class="nc" id="L119">        List&lt;CustomerDevices&gt; androidCustomerDevicesList= new ArrayList&lt;CustomerDevices&gt;();</span>
<span class="nc" id="L120">        List&lt;CustomerDevices&gt; iosCustomerDevicesList= new ArrayList&lt;CustomerDevices&gt;();</span>

<span class="nc bnc" id="L122" title="All 4 branches missed.">        if(customerDevicesList!=null &amp;&amp; customerDevicesList.size()&gt;0) {</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">            for(CustomerDevices customerDevice: customerDevicesList) {</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">                if(&quot;Android&quot;.equalsIgnoreCase(customerDevice.getOs())) {</span>
<span class="nc" id="L125">                    androidCustomerDevicesList.add(customerDevice);</span>
                }
<span class="nc bnc" id="L127" title="All 2 branches missed.">                else if(&quot;iOS&quot;.equalsIgnoreCase(customerDevice.getOs()))</span>
<span class="nc" id="L128">                    iosCustomerDevicesList.add(customerDevice);</span>
<span class="nc" id="L129">            }</span>
        }


<span class="nc" id="L133">        ArrayList&lt;DevicePlatform&gt; platformList= new ArrayList&lt;DevicePlatform&gt;();</span>


<span class="nc bnc" id="L136" title="All 4 branches missed.">        if(iosCustomerDevicesList!=null &amp;&amp; iosCustomerDevicesList.size()&gt;0) {</span>
<span class="nc" id="L137">            DevicePlatform devicePlatform= new DevicePlatform();</span>
<span class="nc" id="L138">            devicePlatform.setPlatformName(&quot;ios&quot;);</span>
<span class="nc" id="L139">            ArrayList&lt;String&gt; registrationIdsList= new ArrayList&lt;String&gt;();</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">            for(CustomerDevices customerDevice: iosCustomerDevicesList) {</span>
<span class="nc bnc" id="L141" title="All 4 branches missed.">                if(customerDevice!=null &amp;&amp; customerDevice.getRegistrationId()!=null)</span>
<span class="nc" id="L142">                    registrationIdsList.add(customerDevice.getRegistrationId());</span>
<span class="nc" id="L143">            }</span>
<span class="nc" id="L144">            devicePlatform.setRegistrationIdsList(registrationIdsList);</span>
<span class="nc" id="L145">            platformList.add(devicePlatform);</span>
        }

<span class="nc" id="L148">        CustomerNotificationMessage customerNotificationMessage= new CustomerNotificationMessage();</span>

<span class="nc bnc" id="L150" title="All 2 branches missed.">        if(!inboxView)</span>
<span class="nc" id="L151">            data.setNavigateTo(&quot;none&quot;);</span>
<span class="nc" id="L152">        customerNotificationMessage.setData(data);</span>
<span class="nc" id="L153">        customerNotificationMessage.setPlatforms(platformList);</span>

<span class="nc" id="L155">        return customerNotificationMessage;</span>
    }
    public Customer getCustomerById(Long id) {
<span class="nc" id="L158">        Customer customer = null;</span>
		try {
<span class="nc" id="L160">			customer = customerManager.getCustomer(id);</span>
<span class="nc" id="L161">		} catch (CustomerException e) {</span>
<span class="nc" id="L162">			e.printStackTrace();</span>
<span class="nc" id="L163">		}</span>
<span class="nc" id="L164">        return customer;</span>
    }

    

    public void pushNotification(CustomerNotificationMessage customerNotificationMessage) throws Exception {

<span class="nc" id="L171">        String targetURL = this.propertyLoader.loadProperty(&quot;BECustomer.inApp.push.url&quot;);</span>
        try {
<span class="nc" id="L173">            disableSslVerification();</span>
<span class="nc" id="L174">            URL targetUrl = new URL(targetURL);</span>

<span class="nc" id="L176">            HttpURLConnection httpConnection = (HttpURLConnection) targetUrl.openConnection();</span>
<span class="nc" id="L177">            httpConnection.setDoOutput(true);</span>
<span class="nc" id="L178">            httpConnection.setRequestMethod(&quot;POST&quot;);</span>
<span class="nc" id="L179">            httpConnection.setRequestProperty(&quot;Content-Type&quot;, &quot;application/json&quot;);</span>

<span class="nc" id="L181">            JSONObject data = new JSONObject();</span>
<span class="nc" id="L182">            data.put(&quot;message&quot;, customerNotificationMessage.getData().getMessage());</span>
<span class="nc" id="L183">            data.put(&quot;title&quot;, customerNotificationMessage.getData().getTitle());</span>
<span class="nc" id="L184">            data.put(&quot;messageId&quot;, customerNotificationMessage.getData().getMessageId());</span>

<span class="nc bnc" id="L186" title="All 2 branches missed.">            if(customerNotificationMessage.getData().getNavigateTo()!=null)</span>
<span class="nc" id="L187">                data.put(&quot;navigateTo&quot;, customerNotificationMessage.getData().getNavigateTo());</span>

<span class="nc" id="L189">            JSONArray platformsArray = new JSONArray();</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">            if(customerNotificationMessage.getPlatforms()!=null) {</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">                for(DevicePlatform devicePlatform: customerNotificationMessage.getPlatforms()) {</span>
<span class="nc" id="L192">                    JSONObject plateformElement = new JSONObject();</span>
<span class="nc" id="L193">                    JSONArray registrationArra = new JSONArray();</span>
<span class="nc" id="L194">                    plateformElement.put(&quot;platformName&quot;, devicePlatform.getPlatformName());</span>

<span class="nc bnc" id="L196" title="All 2 branches missed.">                    for(String regId: devicePlatform.getRegistrationIdsList()) {</span>
<span class="nc" id="L197">                        registrationArra.add(regId);</span>
<span class="nc" id="L198">                    }</span>
<span class="nc" id="L199">                    plateformElement.put(&quot;registrationIdsList&quot;, registrationArra);</span>
<span class="nc" id="L200">                    platformsArray.add(plateformElement);</span>
<span class="nc" id="L201">                }</span>
            }

<span class="nc" id="L204">            JSONObject fullMessage = new JSONObject();</span>
<span class="nc" id="L205">            fullMessage.put(&quot;data&quot;, data);</span>
<span class="nc" id="L206">            fullMessage.put(&quot;platforms&quot;, platformsArray);</span>
<span class="nc" id="L207">            String input= fullMessage.toJSONString();</span>
<span class="nc" id="L208">            System.out.println(&quot;INPUT : &quot;+input);</span>

<span class="nc" id="L210">            OutputStream outputStream = httpConnection.getOutputStream();</span>

<span class="nc" id="L212">            OutputStreamWriter outputStreamWriter = new OutputStreamWriter(httpConnection.getOutputStream());</span>
<span class="nc" id="L213">            outputStreamWriter.write(input);</span>
<span class="nc" id="L214">            outputStreamWriter.flush();</span>

<span class="nc" id="L216">            BufferedReader responseBuffer = new BufferedReader(new InputStreamReader((httpConnection.getInputStream())));</span>

            String output;
<span class="nc" id="L219">            System.out.println(&quot;Output from Server:\n&quot;);</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">            while ((output = responseBuffer.readLine()) != null) {</span>
<span class="nc" id="L221">                System.out.println(output);</span>
            }

<span class="nc" id="L224">        } catch (MalformedURLException e) {</span>

<span class="nc" id="L226">            e.printStackTrace();</span>

<span class="nc" id="L228">        } catch (IOException e) {</span>

<span class="nc" id="L230">            e.printStackTrace();</span>

<span class="nc" id="L232">        }</span>
<span class="nc" id="L233">    }</span>
    private void disableSslVerification() {
        try
        {
<span class="nc" id="L237">            TrustManager[] trustAllCerts = new TrustManager[] {new X509TrustManager() {</span>
                public X509Certificate[] getAcceptedIssuers() {
<span class="nc" id="L239">                    return null;</span>
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
<span class="nc" id="L242">                }</span>
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
<span class="nc" id="L244">                }</span>
            }
            };
<span class="nc" id="L247">            SSLContext sc = SSLContext.getInstance(&quot;SSL&quot;);</span>
<span class="nc" id="L248">            sc.init(null, trustAllCerts, new java.security.SecureRandom());</span>
<span class="nc" id="L249">            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());</span>
<span class="nc" id="L250">            HostnameVerifier allHostsValid = new HostnameVerifier() {</span>
                public boolean verify(String hostname, SSLSession session) {
<span class="nc" id="L252">                    return true;</span>
                }
            };
<span class="nc" id="L255">            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);</span>
<span class="nc" id="L256">        } catch (NoSuchAlgorithmException e) {</span>
<span class="nc" id="L257">            e.printStackTrace();</span>
<span class="nc" id="L258">        } catch (KeyManagementException e) {</span>
<span class="nc" id="L259">            e.printStackTrace();</span>
<span class="nc" id="L260">        }</span>
<span class="nc" id="L261">    }</span>


<span class="nc" id="L264">    public class Data {</span>
        private String message;
        private String title;
        private String navigateTo;
        private Long messageId;

        public String getMessage() {
<span class="nc" id="L271">            return message;</span>
        }

        public void setMessage(String message) {
<span class="nc" id="L275">            this.message = message;</span>
<span class="nc" id="L276">        }</span>

        public String getTitle() {
<span class="nc" id="L279">            return title;</span>
        }

        public void setTitle(String title) {
<span class="nc" id="L283">            this.title = title;</span>
<span class="nc" id="L284">        }</span>

        public String getNavigateTo() {
<span class="nc" id="L287">            return navigateTo;</span>
        }

        public void setNavigateTo(String navigateTo) {
<span class="nc" id="L291">            this.navigateTo = navigateTo;</span>
<span class="nc" id="L292">        }</span>

        public Long getMessageId() {
<span class="nc" id="L295">            return messageId;</span>
        }

        public void setMessageId(Long messageId) {
<span class="nc" id="L299">            this.messageId = messageId;</span>
<span class="nc" id="L300">        }</span>

        @Override
        public String toString() {
<span class="nc" id="L304">            return &quot;Data [message=&quot; + message + &quot;, title=&quot; + title + &quot;, navigateTo=&quot; + navigateTo + &quot;, messageId=&quot;</span>
                    + messageId + &quot;]&quot;;
        }

    }
<span class="nc" id="L309">    public class CustomerNotificationMessage implements Serializable {</span>

        private Data data;
        private ArrayList&lt;DevicePlatform&gt; platforms;

        public Data getData() {
<span class="nc" id="L315">            return data;</span>
        }

        public void setData(Data data) {
<span class="nc" id="L319">            this.data = data;</span>
<span class="nc" id="L320">        }</span>

        public ArrayList&lt;DevicePlatform&gt; getPlatforms() {
<span class="nc" id="L323">            return platforms;</span>
        }

        public void setPlatforms(ArrayList&lt;DevicePlatform&gt; platforms) {
<span class="nc" id="L327">            this.platforms = platforms;</span>
<span class="nc" id="L328">        }</span>

        @Override
        public String toString() {
<span class="nc" id="L332">            return &quot;CustomerMessage [data=&quot; + data + &quot;, platforms=&quot; + platforms + &quot;]&quot;;</span>
        }

    }
<span class="nc" id="L336">    public class DevicePlatform {</span>
        private String platformName;
        private ArrayList&lt;String&gt; registrationIdsList;

        public String getPlatformName() {
<span class="nc" id="L341">            return platformName;</span>
        }

        public void setPlatformName(String platformName) {
<span class="nc" id="L345">            this.platformName = platformName;</span>
<span class="nc" id="L346">        }</span>

        public ArrayList&lt;String&gt; getRegistrationIdsList() {
<span class="nc" id="L349">            return registrationIdsList;</span>
        }

        public void setRegistrationIdsList(ArrayList&lt;String&gt; registrationIdsList) {
<span class="nc" id="L353">            this.registrationIdsList = registrationIdsList;</span>
<span class="nc" id="L354">        }</span>

        @Override
        public String toString() {
<span class="nc" id="L358">            return &quot;DevicePlatform [platformName=&quot; + platformName + &quot;, registrationIdsList=&quot; + registrationIdsList + &quot;]&quot;;</span>
        }

    }
	
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>