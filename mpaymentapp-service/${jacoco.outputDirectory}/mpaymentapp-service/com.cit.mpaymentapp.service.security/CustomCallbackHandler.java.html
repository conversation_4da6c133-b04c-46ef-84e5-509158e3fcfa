<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomCallbackHandler.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">mpaymentapp-service</a> &gt; <a href="index.source.html" class="el_package">com.cit.mpaymentapp.service.security</a> &gt; <span class="el_source">CustomCallbackHandler.java</span></div><h1>CustomCallbackHandler.java</h1><pre class="source lang-java linenums">package com.cit.mpaymentapp.service.security;

import java.io.IOException;

import javax.security.auth.callback.Callback;
import javax.security.auth.callback.NameCallback;
import javax.security.auth.callback.PasswordCallback;
import javax.security.auth.callback.UnsupportedCallbackException;

<span class="nc" id="L10">public class CustomCallbackHandler implements javax.security.auth.callback.CallbackHandler {</span>
<span class="nc" id="L11">	private String username = null;</span>
<span class="nc" id="L12">	private char[] password = null;</span>

	public void handle(Callback[] callbacks) throws IOException, UnsupportedCallbackException {
<span class="nc" id="L15">		prompt();</span>

<span class="nc bnc" id="L17" title="All 2 branches missed.">		for (int i = 0; i &lt; callbacks.length; i++) {</span>
<span class="nc" id="L18">			Callback callback = callbacks[i];</span>

<span class="nc bnc" id="L20" title="All 2 branches missed.">			if (callback instanceof NameCallback) {</span>
<span class="nc" id="L21">				NameCallback ncb = (NameCallback) callback;</span>
<span class="nc" id="L22">				ncb.setName(username);</span>
<span class="nc bnc" id="L23" title="All 2 branches missed.">			} else if (callback instanceof PasswordCallback) {</span>
<span class="nc" id="L24">				PasswordCallback pcb = (PasswordCallback) callback;</span>
<span class="nc" id="L25">				pcb.setPassword(password);</span>
<span class="nc" id="L26">			} else {</span>
<span class="nc" id="L27">				throw new UnsupportedCallbackException(callback);</span>
			}
		}
<span class="nc" id="L30">	}</span>

	private void prompt() {
<span class="nc" id="L33">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>