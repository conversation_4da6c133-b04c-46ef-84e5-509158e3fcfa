package com.cit.mpayment.atm.voucher.response;

import java.io.Serializable;

import com.cit.shared.error.exception.GeneralFailureException;

public class GetPaycodeStatusResponse extends CommonResponse implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String channel;
	private String token;
	private String code;
	private String description;
	private String paymentMethodCode;
	private String surcharge;
	private String paymentMethodIdentifier;
	private String paymentMethodTypeCode;
	private String tokenLifeTimeInMinutes;
	private String amount;
	private String subscriberId;
	private String settlementCode;
	private String status;
	private String frontEndPartner;
	private String transactionType;
	
	public String getTransactionType() {
		return transactionType;
	}
	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getPaymentMethodCode() {
		return paymentMethodCode;
	}
	public void setPaymentMethodCode(String paymentMethodCode) {
		this.paymentMethodCode = paymentMethodCode;
	}
	public String getSurcharge() {
		return surcharge;
	}
	public void setSurcharge(String surcharge) {
		this.surcharge = surcharge;
	}
	public String getPaymentMethodIdentifier() {
		return paymentMethodIdentifier;
	}
	public void setPaymentMethodIdentifier(String paymentMethodIdentifier) {
		this.paymentMethodIdentifier = paymentMethodIdentifier;
	}
	public String getPaymentMethodTypeCode() {
		return paymentMethodTypeCode;
	}
	public void setPaymentMethodTypeCode(String paymentMethodTypeCode) {
		this.paymentMethodTypeCode = paymentMethodTypeCode;
	}
	public String getTokenLifeTimeInMinutes() {
		return tokenLifeTimeInMinutes;
	}
	public void setTokenLifeTimeInMinutes(String tokenLifeTimeInMinutes) {
		this.tokenLifeTimeInMinutes = tokenLifeTimeInMinutes;
	}
	public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	public String getSubscriberId() {
		return subscriberId;
	}
	public void setSubscriberId(String subscriberId) {
		this.subscriberId = subscriberId;
	}
	public String getSettlementCode() {
		return settlementCode;
	}
	public void setSettlementCode(String settlementCode) {
		this.settlementCode = settlementCode;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getFrontEndPartner() {
		return frontEndPartner;
	}
	public void setFrontEndPartner(String frontEndPartner) {
		this.frontEndPartner = frontEndPartner;
	}
	
	@Override
	String errorCode() {
		return GeneralFailureException.GENERATE_VOUCHER_PAYCODE_INTEGRATION_ERROR;
	} 
	
}
