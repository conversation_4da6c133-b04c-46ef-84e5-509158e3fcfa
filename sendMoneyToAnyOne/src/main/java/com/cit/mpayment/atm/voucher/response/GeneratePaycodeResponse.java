package com.cit.mpayment.atm.voucher.response;

import com.cit.shared.error.exception.GeneralFailureException;

public class GeneratePaycodeResponse extends CommonResponse{

	private static final long serialVersionUID = 1L;
	
	private String subscriberId;
	private String payWithMobileToken;
	private String tokenLifeTimeInMinutes;
	
	public String getSubscriberId() {
		return subscriberId;
	}
	public void setSubscriberId(String subscriberId) {
		this.subscriberId = subscriberId;
	}
	public String getPayWithMobileToken() {
		return payWithMobileToken;
	}
	public void setPayWithMobileToken(String payWithMobileToken) {
		this.payWithMobileToken = payWithMobileToken;
	}
	public String getTokenLifeTimeInMinutes() {
		return tokenLifeTimeInMinutes;
	}
	public void setTokenLifeTimeInMinutes(String tokenLifeTimeInMinutes) {
		this.tokenLifeTimeInMinutes = tokenLifeTimeInMinutes;
	}
	
	@Override
	String errorCode() {
		return GeneralFailureException.GENERATE_VOUCHER_PAYCODE_INTEGRATION_ERROR;
	} 
		
}
