package com.cit.mpayment.atm.voucher.connector;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Map;
import java.util.Properties;
import java.util.Random;

import javax.net.ssl.SSLContext;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import com.google.common.io.CharStreams;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class GetVoucherPaycodeRequestISWConnector {
	 private Properties ISWConnectionProperties;
	public Properties getISWConnectionProperties() {
		return ISWConnectionProperties;
	}
	public void setISWConnectionProperties(Properties iSWConnectionProperties) {
		ISWConnectionProperties = iSWConnectionProperties;
	}
	
	@SuppressWarnings("rawtypes")
	public InputStream connectISW(String requestJSON,String subscriberPhoneNumber) throws NoSuchAlgorithmException, KeyManagementException, IOException{
		Long timestamp = System.currentTimeMillis() / 1000;
		String Nonce = getNonce();
		
		String client_id = ISWConnectionProperties.getProperty("client_id");
		String shared_secret_key = ISWConnectionProperties.getProperty("shared_secret_key");
		String generatePayCodeISWUrl = System.getenv("get_voucher_paycode_address");
		String Authorization = getAuthorization(client_id);
		String url = generatePayCodeISWUrl + subscriberPhoneNumber + "/tokens";
		String percentEncodeURL = encodeURL(url);
		String signtiure = getSigntiure(client_id, shared_secret_key, percentEncodeURL);
		String signtiureMethod = ISWConnectionProperties.getProperty("signtiureMethod");
		
		SSLContext context = SSLContext.getInstance("TLSv1.2");
		context.init(null, null, null);

		CloseableHttpClient client = HttpClients.custom().setSSLContext(context)
				.setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
		HttpPost httppost = new HttpPost(url);
		httppost.setHeader("Accept", "application/json");

		StringEntity params = new StringEntity(requestJSON);
		httppost.addHeader("content-type", "application/json");
		httppost.addHeader("Accept", "application/json");
		httppost.addHeader("Authorization", Authorization);
		httppost.addHeader("Nonce", Nonce);
		httppost.addHeader("timestamp", timestamp.toString());
		httppost.addHeader("signtiure", signtiure);
		httppost.addHeader("signtiureMethod", signtiureMethod);

		HttpResponse response = client.execute(httppost);
		InputStream in = null;
		if (response != null) {
			in = response.getEntity().getContent();
			return in;
		}
		return null;
 	}
 	
 	private String getNonce() {
		final Random random = new SecureRandom();
		int length = 64;
		String nonce = String.format("%" + length + "s",
				new BigInteger(length * 5, random).toString(32)).replace('\u0020', '0');
		return nonce;
	}
	 
	private String encodeURL(String URL) throws UnsupportedEncodingException{
	   String encode = URLEncoder.encode(URL,"UTF-8"); return encode; 
	}
	 
	private String getAuthorization(String clientID) {
		byte[] encodedBytes = Base64.encodeBase64(clientID.getBytes());
		String encodedClientID = new String(encodedBytes);
		System.out.println("encodedBytes clientID : " + new String(encodedBytes));
		return "InterswitchAuth " + encodedClientID;
	}

	private String getSigntiure(String client_id, String shared_secret_key,String percentEncodeURL) {
		String http_verb = ISWConnectionProperties.getProperty("http_verb");
		long timestamp = System.currentTimeMillis() / 1000;
 
		String baseStringToBeSigned = http_verb + "&" + percentEncodeURL + "&"+ timestamp + "&" + getNonce() + "&" + client_id + "&"
				+ shared_secret_key;
		String hasedbaseStringToBeSigned = DigestUtils.sha1Hex(baseStringToBeSigned);

		byte[] encodedBytes = Base64.encodeBase64(hasedbaseStringToBeSigned.getBytes());
		String stringToBeSigned = new String(encodedBytes);

		return stringToBeSigned;
	}    
}