<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
    http://www.springframework.org/schema/beans/spring-beans.xsd 
    http://www.springframework.org/schema/context 
    http://www.springframework.org/schema/context/spring-context.xsd
    http://www.springframework.org/schema/util 
    http://www.springframework.org/schema/util/spring-util.xsd">
          
		
		<bean id="placeTicketingOrderRequestGenerator" class="com.cit.mpaymentapp.components.PlaceTicketingOrderRequestGenerator"/>
		<bean id="placeTicketingOrderResponseValidator" class="com.cit.mpaymentapp.components.PlaceTicketingOrderResponseValidator"/>    
		
		<bean id="ticketingOrderDetailsRequestGenerator" class="com.cit.mpaymentapp.components.TicketingOrderDetailsRequestGenerator"/>    
		<bean id="ticketingOrderDetailsResponseValidator" class="com.cit.mpaymentapp.components.TicketingOrderDetailsResponseValidator"/>    
		
		<bean id="verifyTicketingOrderRequestGenerator" class="com.cit.mpaymentapp.components.VerifyTicketingOrderRequestGenerator"/>    
		<bean id="verifyTicketingOrderResponseValidator" class="com.cit.mpaymentapp.components.VerifyTicketingOrderResponseValidator"/>    
		    
		<bean id="ticketingEventDetailsRequestGenerator" class="com.cit.mpaymentapp.components.TicketingEventDetailsRequestGenerator"/>    
		<bean id="ticketingEventDetailsResponseValidator" class="com.cit.mpaymentapp.components.TicketingEventDetailsResponseValidator"/>    
		    
		<bean id="listTicketingEventsResponseValidator" class="com.cit.mpaymentapp.components.ListTicketingEventsResponseValidator"/>    
		
</beans>

