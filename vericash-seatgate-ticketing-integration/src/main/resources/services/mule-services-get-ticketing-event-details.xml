<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">
	
	<flow name="Get Ticketing Event Details Integration flow">
					   
				<jms:listener config-ref="JMS_Config" destination="${jms.ticketing.event.details.request}" />
				
				<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
				<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
				<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
				<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>			   
				
			   <logger level="INFO" message="----------------------Start Get Ticketing Event Details Integration flow --------------" doc:name="Logger"/>
			   <set-variable variableName="businessMessage" value="#[payload]" doc:name="Variable"/>
			   <set-variable value="#[correlationId]" variableName="correlationId" doc:name="Variable"/>
			   <set-variable value="${jms.ticketing.event.details.response}" variableName="responseQueue" doc:name="Variable"/>

				<java:invoke
					class="com.cit.mpaymentapp.components.TicketingEventDetailsRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="ticketingEventDetailsRequestGenerator"
					target="queryParameters">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>	
						   
			   <set-variable variableName="requestSOAPMessage" value="#[vars.queryParameters]" />				
				<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
			   
			   <set-payload value="#[&quot; &quot;]" doc:name="Set Payload"/>
				
				<set-variable value="${https_get_ticketing_event_details}" variableName="https_get_ticketing_event_details"/>
				<set-variable value="${uba_seatgate_token}" variableName="uba_seatgate_token"/>
			   <http:request method="GET" url="#[vars.https_get_ticketing_event_details ++ vars.uba_seatgate_token ++ vars.queryParameters]" responseTimeout="${COREBANKING.TIMEOUT}"/>
			   
				<set-variable variableName="responseSOAPMessage" value="#[payload]" />
				<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />		
			   	<logger level="INFO" message="########### Response  #[message.payload] #############"/>

				<java:invoke
					class="com.cit.mpaymentapp.components.TicketingEventDetailsResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.cit.mpaymentapp.ticketing.common.TicketingEventDetails)"
					instance="ticketingEventDetailsResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
		 	 				arg1: payload as Object {class: "com.cit.mpaymentapp.ticketing.common.TicketingEventDetails"}}]]]>
					</java:args>
				</java:invoke>			
				   
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
		
				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
					<jms:message correlationId="#[correlationId]">
						<jms:body>#[payload]</jms:body>
						<jms:properties>
							#[{
							MULE_CORRELATION_ID: vars.correlationId,
							MULE_CORRELATION_GROUP_SIZE: '-1',
							MULE_CORRELATION_SEQUENCE:'-1'
							}]</jms:properties>
					</jms:message>
				</jms:publish>	 
		
		<error-handler ref="TicketingExceptionStrategy"/>
		</flow>
		
	</mule>


	
