<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms"
	xmlns:spring="http://www.mulesoft.org/schema/mule/spring"
	xmlns:java="http://www.mulesoft.org/schema/mule/java"
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xsi:schemaLocation=
	"http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd 
	http://www.mulesoft.org/schema/mule/spring http://www.mulesoft.org/schema/mule/spring/current/mule-spring.xsd 
	http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">

 		<spring:config name="seatgateIntegrationBeans" files="services/mule-config-beans-ticketing.xml"/>
 		<configuration-properties file="properties/mule-seatgate-integration-ticketing.properties" />	
		<import file="services/mule-services-ticketing.xml"/>
		
		<error-handler name="TicketingExceptionStrategy" >
		
			<on-error-continue>
			<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>		
					<logger level="ERROR" message=" ------------------ Start TicketingExceptionStrategy ---------------------" doc:name="Logger"/>
					<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
					<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
					<async>
							  <flow-ref name="Handle Exception" />
					</async>
				   <logger level="ERROR" message=" ------------------ End TicketingExceptionStrategy ---------------------" doc:name="Logger"/>
<!--					<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="#[vars.responseQueue]" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--						<jms:message correlationId="#[correlationId]">-->
<!--							<jms:body>#[payload]</jms:body>-->
<!--							<jms:properties>-->
<!--								#[{-->
<!--								MULE_CORRELATION_ID: vars.correlationId,-->
<!--								MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--								MULE_CORRELATION_SEQUENCE:'-1'-->
<!--								}]</jms:properties>-->
<!--						</jms:message>-->
<!--					</jms:publish>	-->
			  </on-error-continue>
		</error-handler>
	</mule>


	
