package com.cit.mpaymentapp.components;

import java.text.DecimalFormat;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;

public class VerifyTicketingOrderRequestGenerator {

	private static final String EXTERNAL_TRANSACTION_NUMBER = "externalTransactionNumber";

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		
		StringBuilder queryParameters = new StringBuilder();
		try{
		queryParameters.append(businessMessage.getTicketing().getOrderDetails().getId());
		queryParameters.append("?status=" + "SUCCESS");
		queryParameters.append("&responseString=" + businessMessage.getSoftFields().get(EXTERNAL_TRANSACTION_NUMBER));
		queryParameters.append("&responseCode=00");
		queryParameters.append("&paymentRef=" +businessMessage.getTransactionInfo().getTransactionId());
		queryParameters.append("&transactionRef="+businessMessage.getSoftFields().get(EXTERNAL_TRANSACTION_NUMBER));
		DecimalFormat numberFormat = new DecimalFormat(".00");
		queryParameters.append("&transactionAmount="+numberFormat.format(businessMessage.getTransactionInfo().getTransactionAmount()));
		}catch(Exception e){
			e.printStackTrace();
			throw new GeneralFailureException(GeneralFailureException.INPUT_PARAMETER_IS_REQUIRED);
		}		
		return queryParameters.toString();
	}

}
