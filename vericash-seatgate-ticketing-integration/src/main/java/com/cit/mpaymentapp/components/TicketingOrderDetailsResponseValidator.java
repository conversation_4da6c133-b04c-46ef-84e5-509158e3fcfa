package com.cit.mpaymentapp.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.ticketing.common.TicketingOrderDetails;

public class TicketingOrderDetailsResponseValidator {
	public Object onCall(BusinessMessage businessMessage, TicketingOrderDetails orderDetails) throws Exception {
		
		if(orderDetails.getError() == null){
			businessMessage.getTicketing().setOrderDetails(orderDetails);
		}else{
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("Error");
			businessMessage.getStatus().setStatusMsg(orderDetails.getError());
		}
		
		return businessMessage;
	}
}
