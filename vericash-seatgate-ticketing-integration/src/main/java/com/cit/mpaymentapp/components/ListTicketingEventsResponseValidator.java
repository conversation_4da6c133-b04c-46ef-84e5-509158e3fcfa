package com.cit.mpaymentapp.components;

import java.util.List;

import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.type.TypeFactory;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.ticketing.common.Ticketing;
import com.cit.mpaymentapp.ticketing.common.TicketingError;
import com.cit.mpaymentapp.ticketing.common.TicketingEvent;
import com.cit.shared.error.exception.GeneralFailureException;

public class ListTicketingEventsResponseValidator {

	public Object onCall(BusinessMessage businessMessage, String responseString) throws Exception {
		ObjectMapper mapper = new ObjectMapper();
		List<TicketingEvent> events = null;
		TicketingError ticketingError= null;
		
		try{
			   events = mapper.readValue(responseString, TypeFactory.defaultInstance().constructCollectionType(List.class,  
				TicketingEvent.class));
		}catch(Exception e){
			try{
				 ticketingError = mapper.readValue(responseString, TicketingError.class); 	
			}catch(Exception ex){
				ex.printStackTrace();
				throw new GeneralFailureException("EXT00101");
			}
		}
		
		if( events!=null && businessMessage.getClientInfo().getPlatform().trim().toLowerCase().startsWith("ios")){
			events = TicketingUtil.adaptDatesForIOS(events);
		}
		
		if (events != null) {
			Ticketing ticketing = new Ticketing();
			businessMessage.setTicketing(ticketing);
			businessMessage.getTicketing().setEventsList(events);
		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("Error");
			businessMessage.getStatus().setStatusMsg(ticketingError.getError());
		}
		return businessMessage;
	}

}
