package com.cit.mpaymentapp.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;

public class TicketingOrderDetailsRequestGenerator {
	private final static String GENERAL_FAILURE_MESSAGE="SYS00005";

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		
		if (businessMessage.getTicketing().getOrderDetails() != null
				&& businessMessage.getTicketing().getOrderDetails().getId() != null) {

			return businessMessage.getTicketing().getOrderDetails().getId();
		}else{
			throw new GeneralFailureException(GENERAL_FAILURE_MESSAGE);
		}

	}
}
