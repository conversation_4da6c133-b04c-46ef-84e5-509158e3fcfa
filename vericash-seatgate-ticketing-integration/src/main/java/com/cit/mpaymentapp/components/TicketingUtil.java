package com.cit.mpaymentapp.components;

import java.util.List;

import com.cit.mpaymentapp.ticketing.common.TicketingEvent;
import com.cit.mpaymentapp.ticketing.common.TicketingEventDetails;

public class TicketingUtil {

	private static TicketingEvent adaptDatesForIOS(TicketingEvent ticketingEvent) {
		ticketingEvent.setStartDate(ticketingEvent.getStartDate().replace(" ", "T"));
		ticketingEvent.setEndDate(ticketingEvent.getEndDate().replace(" ", "T"));
		return ticketingEvent;
	}

	public static List<TicketingEvent> adaptDatesForIOS(List<TicketingEvent> events) {
		for (int i = 0; i < events.size(); i++) {
			events.set(i, adaptDatesForIOS(events.get(i)));
		}
		return events;
	}
	
	public static TicketingEventDetails adaptDatesForIOS(TicketingEventDetails ticketingEvent) {
		ticketingEvent.setStartDate(ticketingEvent.getStartDate().replace(" ", "T"));
		ticketingEvent.setEndDate(ticketingEvent.getEndDate().replace(" ", "T"));
		return ticketingEvent;
	}

}
