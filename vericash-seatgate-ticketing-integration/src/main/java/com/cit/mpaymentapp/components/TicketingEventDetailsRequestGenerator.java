package com.cit.mpaymentapp.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.exception.GeneralFailureException;

public class TicketingEventDetailsRequestGenerator {
	private final static String GENERAL_FAILURE_MESSAGE = "SYS00005";

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		if (businessMessage.getTicketing().getEventDetailsService() != null
				&& businessMessage.getTicketing().getEventDetailsService().getEventNumber() != null) {
			return businessMessage.getTicketing().getEventDetailsService().getEventNumber().toString();
		} else {
			throw new GeneralFailureException(GENERAL_FAILURE_MESSAGE);
		}

	}

}
