package com.cit.mpaymentapp.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.ticketing.common.PlaceOrderResponse;
import com.cit.mpaymentapp.ticketing.common.TicketingOrderDetails;

public class PlaceTicketingOrderResponseValidator{

	public Object onCall(BusinessMessage businessMessage, PlaceOrderResponse placeOrderResponse) throws Exception {
		
		if(placeOrderResponse.getError() == null){
			TicketingOrderDetails orderDetails = new TicketingOrderDetails();
			orderDetails.setId(placeOrderResponse.getId());
			businessMessage.getTicketing().setOrderDetails(orderDetails);
			
		}else{
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("Error");
			businessMessage.getStatus().setStatusMsg(placeOrderResponse.getError());
		}
		
		return businessMessage;
	}

}
