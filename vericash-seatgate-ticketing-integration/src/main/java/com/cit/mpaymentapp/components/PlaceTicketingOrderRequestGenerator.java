package com.cit.mpaymentapp.components;

import org.mule.api.transport.PropertyScope;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.ticketing.common.CustomerDetails;
import com.cit.mpaymentapp.ticketing.common.TicketType;
import com.cit.mpaymentapp.ticketing.common.TicketingEventDetails;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber;

public class PlaceTicketingOrderRequestGenerator{

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		StringBuilder queryParameters = new StringBuilder();

		TicketingEventDetails eventDetails = businessMessage.getTicketing().getEventDetails();
		CustomerDetails customerDetails = businessMessage.getTicketing().getCustomerDetails();
		
		queryParameters.append(eventDetails.getEventNumber());
		queryParameters.append("?email=" + customerDetails.getEmail());
		queryParameters.append("&firstname=" + customerDetails.getFirstname());
		queryParameters.append("&surname=" + customerDetails.getSurname());
		String mobileNumber = customerDetails.getMobile();
	    PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
	    PhoneNumber numberProto = phoneUtil.parse(mobileNumber, businessMessage.getWalletInfo().getCountryIso2());
	    String validMobileNumber = "0" + numberProto.getNationalNumber();

		queryParameters.append("&mobile=" + validMobileNumber);
		queryParameters.append("&tickettypes=");

		StringBuilder tickets = new StringBuilder();
		for (TicketType ticket : eventDetails.getTicketTypes()) {
			tickets.append(",");
			tickets.append(ticket.getId() + "-" + ticket.getQuantity());
		}

		queryParameters.append(tickets.toString().replaceFirst(",", ""));

		return queryParameters.toString();
	}

}
