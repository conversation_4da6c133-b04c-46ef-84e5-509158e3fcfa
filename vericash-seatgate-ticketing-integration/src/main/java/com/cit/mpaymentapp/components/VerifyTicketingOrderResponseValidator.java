package com.cit.mpaymentapp.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.ticketing.common.ConfirmOrderResponse;

public class VerifyTicketingOrderResponseValidator{

	public Object onCall(BusinessMessage businessMessage, ConfirmOrderResponse confirmOrderResponse) throws Exception {


		if (confirmOrderResponse.getError() == null && confirmOrderResponse.getSuccess() != null) {
			businessMessage.getServiceSuccessResponse().setSuccessShortDescription("Success");
			businessMessage.getServiceSuccessResponse().setSuccessDescription(confirmOrderResponse.getSuccess());
		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("Error");
			businessMessage.getStatus().setStatusMsg(confirmOrderResponse.getError());
		}

		return businessMessage;
	}

}
