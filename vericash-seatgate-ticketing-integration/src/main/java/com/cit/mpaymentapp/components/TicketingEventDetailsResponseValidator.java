package com.cit.mpaymentapp.components;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.ticketing.common.TicketingEventDetails;

public class TicketingEventDetailsResponseValidator{

	public Object onCall(BusinessMessage businessMessage, TicketingEventDetails eventDetails) throws Exception {

		if (eventDetails != null
				&& businessMessage.getClientInfo().getPlatform().trim().toLowerCase().startsWith("ios")) {
			eventDetails = TicketingUtil.adaptDatesForIOS(eventDetails);
		}

		if (eventDetails.getError() == null) {
			businessMessage.getTicketing().setEventDetails(eventDetails);
		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("Error");
			businessMessage.getStatus().setStatusMsg(eventDetails.getError());
		}
		return businessMessage;
	}

}
