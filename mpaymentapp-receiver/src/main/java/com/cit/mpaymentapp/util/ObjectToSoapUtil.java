package com.cit.mpaymentapp.util;

import java.io.ByteArrayOutputStream;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPMessage;

public class ObjectToSoapUtil {
	
	public static String convertObjectToSoapEnvelope(Object srcObj,Class  jaxbClass)throws Exception{
		String methodResponse=null;
	       try {
	           MessageFactory mf = MessageFactory.newInstance();
	           SOAPMessage message = mf.createMessage();
	           message.getSOAPPart().getEnvelope().setPrefix("soapenv");
	           message.getSOAPPart().getEnvelope().removeNamespaceDeclaration("SOAP-ENV");
	           message.getSOAPHeader().setPrefix("soapenv");
	           message.getSOAPBody().setPrefix("soapenv");
	           
	           message.getSOAPPart().getEnvelope().addNamespaceDeclaration("soapenv", "http://schemas.xmlsoap.org/soap/envelope/");


	           JAXBContext jc = JAXBContext.newInstance(jaxbClass);
	           Marshaller marshaller = jc.createMarshaller();
	           marshaller.marshal(srcObj,message.getSOAPBody());

	           ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
	           message.writeTo(outputStream);
	           String output = new String(outputStream.toByteArray());
	           System.out.println(output);
	           methodResponse = new String(outputStream.toByteArray());
	             
	       } catch (JAXBException e) {
	           e.printStackTrace();
	       }
	       return methodResponse;
	}
	
}
