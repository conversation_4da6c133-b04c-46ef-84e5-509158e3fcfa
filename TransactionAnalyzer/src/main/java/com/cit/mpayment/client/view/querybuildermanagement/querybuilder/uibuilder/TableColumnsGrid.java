package com.cit.mpayment.client.view.querybuildermanagement.querybuilder.uibuilder;

import java.util.ArrayList;
import java.util.Iterator;

import com.cit.mpayment.client.view.querybuildermanagement.Column;
import com.smartgwt.client.widgets.grid.ListGrid;
import com.smartgwt.client.widgets.grid.ListGridField;
import com.smartgwt.client.widgets.grid.ListGridRecord;

public class TableColumnsGrid {
	public TableColumnsGrid(ListGrid listGrid,ArrayList<Column> columns) {
		listGrid.setFields(createGridFields());
		listGrid.setData(getColumns(columns));
		listGrid.hideField("columnObj");
	}
	public TableColumnsGrid(ListGrid listGrid) {
		listGrid.setFields(createGridFields());
		listGrid.hideField("columnObj");
	}

	private ListGridRecord[] getColumns(ArrayList<Column> columns) {
		ListGridRecord[] records=new ListGridRecord[columns.size()];
		int i=0;
		for (Iterator<Column> iterator = columns.iterator(); iterator.hasNext();) {
			Column column = iterator.next();
			column.setDisplayName("");
			records[i]=new ListGridRecord();
			records[i].setAttribute("columnName", column.getColumnName());
			records[i].setAttribute("columnObj", column);
			i++;
		}
		return records;
	}

	private ListGridField[] createGridFields() {
		ListGridField columnField = new ListGridField("columnName","Column");
		ListGridField columnObjField = new ListGridField("columnObj","columnObj");
		columnObjField.setHidden(true);
		return new ListGridField[] {columnField,columnObjField};
	}

}
