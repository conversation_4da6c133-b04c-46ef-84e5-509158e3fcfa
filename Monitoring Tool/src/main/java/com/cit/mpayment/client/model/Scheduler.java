package com.cit.mpayment.client.model;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

public class Scheduler implements IsSerializable{
	private static final long serialVersionUID = 1L;
	
	private Long id=-1L;
	
	private Date startTime;
	
	private Date endTime;
	
	private Long repeatInterval;
	
	private Integer repeatCount;
	
	private boolean enabled;
	
	private Boolean startupScheduler;

	private MPJob mpJob;
	
	public Date getStartTime() {
		return startTime;
	}
	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public Long getRepeatInterval() {
		return repeatInterval;
	}
	public void setRepeatInterval(Long repeatInterval) {
		this.repeatInterval = repeatInterval;
	}
	public Integer getRepeatCount() {
		return repeatCount;
	}
	public void setRepeatCount(Integer repeatCount) {
		this.repeatCount = repeatCount;
	}
	
	private Long argument;
	
	private SchedulerStatus status;
	
	public enum SchedulerStatus{
		Created,
		Updated,
		Pending,
		Started,
		Completed
	};
	
	
	public void setId(Long id) {
		this.id = id;
	}
	public Long getId() {
		return id;
	}
	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}
	public boolean isEnabled() {
		return enabled;
	}
	
	public void setStatus(SchedulerStatus status) {
		this.status = status;
	}
	public SchedulerStatus getStatus() {
		return status;
	}
	public void setArgument(Long argument) {
		this.argument = argument;
	}
	public Long getArgument() {
		return argument;
	}
	public void setStartupScheduler(Boolean startupScheduler) {
		this.startupScheduler = startupScheduler;
	}
	public Boolean getStartupScheduler() {
		return startupScheduler;
	}
	public void setMpJob(MPJob mpJob) {
		this.mpJob = mpJob;
	}
	public MPJob getMpJob() {
		return mpJob;
	}

}
