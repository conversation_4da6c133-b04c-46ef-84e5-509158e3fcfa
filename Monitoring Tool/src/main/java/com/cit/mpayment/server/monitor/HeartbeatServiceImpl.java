package com.cit.mpayment.server.monitor;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;

import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;

import com.cit.mpayment.client.clientwrapper.Configuration;
import com.cit.mpayment.client.model.HeartbeatServices;
import com.cit.mpayment.client.model.MonitoringCheck;
import com.cit.mpayment.client.panelwrapper.LoginWrapper;
import com.cit.mpayment.client.view.monitor.service.HeartbeatService;
import com.cit.mpayment.server.helper.Constant;
import com.cit.mpayment.server.helper.ServiceLocator;
import com.google.gwt.user.server.rpc.RemoteServiceServlet;
import com.mongodb.Mongo;

public class HeartbeatServiceImpl extends RemoteServiceServlet implements HeartbeatService{

	private static final long serialVersionUID = 1L;
	public Context getContext() {
		Context ctx = null;
		try {
			Configuration configuration=LoginWrapper.getConfigurationForServerSide();

			 Properties props = new Properties();
			 props.setProperty("java.naming.provider.url", "remote://"+configuration.getJbossHostIP()+":4447");  
			 props.setProperty("java.naming.factory.url.pkgs", "org.jboss.ejb.client.naming");  
			 props.setProperty("java.naming.factory.initial","org.jboss.naming.remote.client.InitialContextFactory");
			 props.setProperty("jboss.naming.client.ejb.context","true");
			 ctx = new InitialContext(props);
			 
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return ctx;
	}
	public boolean isEjbAvailable()
	{
		boolean isEjbAvailable=false;
		try {
			Object object=getContext().lookup(Constant.RISK_SERVICE_MANAGER_REMOTE);
			if(object!=null){
				isEjbAvailable=true;
			}
		} catch (Exception e) {
			isEjbAvailable=false;
		}
		return isEjbAvailable;
	}
	public boolean isNoSqlAlive()
	{
		boolean isMongoAlive=false;
		try {
			Configuration configuration=LoginWrapper.getConfigurationForServerSide();
			Mongo m = new Mongo( configuration.getMongoHostIP() , 27017 );
            m.getConnector().getDBPortPool(m.getAddress()).get().ensureOpen();
			isMongoAlive=true;
		} catch (Exception e) {
			isMongoAlive=false;
			
		}
		return isMongoAlive;
	}
	public boolean isOracleDatabaseAlive()
	{
		boolean isOracleDatabaseAlive=false;
		Connection con=null;
		Configuration configuration=LoginWrapper.getConfigurationForServerSide();
		try {
			Class.forName("oracle.jdbc.OracleDriver");
			String userName=configuration.getRemoteOracleDatabaseUserName();
			String password=configuration.getRemoteOracleDatabasePassword();
			String url=configuration.getRemoteOracleUrlConnection();
			con=DriverManager.getConnection("jdbc:oracle:thin:"+userName+"/"+password+"@"+url);
			isOracleDatabaseAlive=true;
		} catch (ClassNotFoundException e) {
			isOracleDatabaseAlive=false;
		} catch (SQLException e) {
			isOracleDatabaseAlive=false;
		}
		return isOracleDatabaseAlive;
	}
	public boolean isMuleOneAlive()
	{
		boolean isMuleOneAlive=false;
		try {
			Configuration configuration=LoginWrapper.getConfigurationForServerSide();
			DefaultHttpClient httpClient = new DefaultHttpClient();
			HttpPost httppost = new HttpPost("http://"+configuration.getMuleOneHostIP()+":9696/mpaymentapp-mule-1/echo/service");
			httppost.setHeader("Accept", "application/json");
			httppost.setHeader("Content-type", "application/json");
			httpClient.execute(httppost);
			isMuleOneAlive=true;
		} catch (ClientProtocolException e) {
			isMuleOneAlive=false;
		} catch (IOException e) {
			isMuleOneAlive=false;
		}
		return isMuleOneAlive;
	}
	public boolean isSystemManagementPortalAlive()
	{
		boolean isSystemManagementPortalAlive=false;
		try {
			Configuration configuration=LoginWrapper.getConfigurationForServerSide();
			DefaultHttpClient httpClient = new DefaultHttpClient();
			HttpGet httpGet=new HttpGet("http://"+configuration.getJbossHostIP()+":"+configuration.getJbossPort()+"/vericash");
			HttpResponse response=httpClient.execute(httpGet);
			if(response.getStatusLine().getStatusCode()==404){ 
				isSystemManagementPortalAlive=false;
			}
			else
			{
				isSystemManagementPortalAlive=true;
			}
			
		} catch (ClientProtocolException e) {
			isSystemManagementPortalAlive=false;
		} catch (IOException e) {
			isSystemManagementPortalAlive=false;
		}
		return isSystemManagementPortalAlive;
	}
	public boolean isReportingPortalAlive()
	{
		boolean isReportingSystemAlive=false;
		try {
			Configuration configuration=LoginWrapper.getConfigurationForServerSide();
			DefaultHttpClient httpClient = new DefaultHttpClient();
			HttpGet httpGet=new HttpGet("http://"+configuration.getJbossHostIP()+":"+configuration.getJbossPort()+"/TransactionAnalyzer");
			HttpResponse response=httpClient.execute(httpGet);
			if(response.getStatusLine().getStatusCode()==404){ 
				isReportingSystemAlive=false;
			}
			else
			{
				isReportingSystemAlive=true;
			}
		} catch (ClientProtocolException e) {
			isReportingSystemAlive=false;
		} catch (IOException e) {
			isReportingSystemAlive=false;
		}
		return isReportingSystemAlive;
	}
	public boolean isMuleReceiverAlive()
	{
		boolean isMuleReceiverAlive=false;
		try {
			Configuration configuration=LoginWrapper.getConfigurationForServerSide();
			DefaultHttpClient httpClient = new DefaultHttpClient();
			HttpPost httppost = new HttpPost("http://"+configuration.getMuleReceiverHostIP()+":9797/mpaymentapp-receiver/echo/service");
			httppost.setHeader("Accept", "application/json");
			httppost.setHeader("Content-type", "application/json");
			httpClient.execute(httppost);
			isMuleReceiverAlive=true;
		} catch (ClientProtocolException e) {
			isMuleReceiverAlive=false;
		} catch (IOException e) {
			isMuleReceiverAlive=false;
		}
		return isMuleReceiverAlive;
	}
	public boolean isMssqlDatabaseAlive()
	{
		boolean isMssqlDatabaseAlive=false;
		Connection con=null;
		Configuration configuration=LoginWrapper.getConfigurationForServerSide();
		try {
			Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
			String userName=configuration.getRemoteMssqlDatabaseUserName();
			String password=configuration.getRemoteMssqlDatabasePassword();
			String databaseName=configuration.getRemoteMssqlDatabaseName();
			String serverIP=configuration.getRemoteMssqlServerIP();
			con=DriverManager.getConnection("jdbc:sqlserver://"+serverIP+":1433;databaseName="+databaseName+";username="+userName+";password="+password);
			isMssqlDatabaseAlive=true;
		} catch (ClassNotFoundException e) {
			isMssqlDatabaseAlive=false;
		} catch (SQLException e) {
			isMssqlDatabaseAlive=false;
		}
		return isMssqlDatabaseAlive;
	}
	public HeartbeatServices getHeartbeatServicesStatus(HeartbeatServices gwtHeartbeatServices,MonitoringCheck monitoringCheck) {
		HeartbeatServices heartbeatServices=new HeartbeatServices();
		if(gwtHeartbeatServices!=null){
			heartbeatServices.setFraudStatusAlive(gwtHeartbeatServices.isFraudStatusAlive());
			heartbeatServices.setRiskStatusAlive(gwtHeartbeatServices.isRiskStatusAlive());
			heartbeatServices.setTransactionStatusAlive(gwtHeartbeatServices.isTransactionStatusAlive());
			heartbeatServices.setSvaStatusAlive(gwtHeartbeatServices.isSvaStatusAlive());
			heartbeatServices.setFeesStatusAlive(gwtHeartbeatServices.isSvaStatusAlive());
			heartbeatServices.setReconciliationStatusAlive(gwtHeartbeatServices.isReconciliationStatusAlive());
			heartbeatServices.setSettlementStatusAlive(gwtHeartbeatServices.isSettlementStatusAlive());
			heartbeatServices.setVoucherManagementStatusAlive(gwtHeartbeatServices.isVoucherManagementStatusAlive());
			heartbeatServices.setWorkflowApprovalStatusAlive(gwtHeartbeatServices.isWorkflowApprovalStatusAlive());
			heartbeatServices.setSchedulersStatusAlive(gwtHeartbeatServices.isSchedulersStatusAlive());
			heartbeatServices.setSmsEmailAlive(gwtHeartbeatServices.isSmsEmailAlive());
			heartbeatServices.setMobileNetworkInterfaceAlive(true);
			heartbeatServices.setCommissionManagementAlive(gwtHeartbeatServices.isCommissionManagementAlive());
			heartbeatServices.setQuartzSchedulerAlive(gwtHeartbeatServices.isQuartzSchedulerAlive());
			heartbeatServices.setSystemManagementPortalAlive(gwtHeartbeatServices.isSystemManagementPortalAlive());
			heartbeatServices.setReportingSystemAlive(gwtHeartbeatServices.isReportingSystemAlive());
			heartbeatServices.setOracleDatabaseAlive(gwtHeartbeatServices.isOracleDatabaseAlive());
			heartbeatServices.setMssqlDatabaseAlive(gwtHeartbeatServices.isMssqlDatabaseAlive());
			heartbeatServices.setNosqlDatabaseAlive(gwtHeartbeatServices.isNosqlDatabaseAlive());
			heartbeatServices.setMuleReceivingAlive(gwtHeartbeatServices.isMuleReceivingAlive());
			heartbeatServices.setMuleOneAlive(gwtHeartbeatServices.isMuleOneAlive());
		}
		boolean isEjbAvailable=isEjbAvailable();
		if(monitoringCheck.isCheckedFraudStatus()){
			heartbeatServices.setFraudStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedRiskStatus())
		{
			heartbeatServices.setRiskStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedTransactionStatus()){
			heartbeatServices.setTransactionStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedSvaStatus()){
			heartbeatServices.setSvaStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedFeesStatus()){
			heartbeatServices.setFeesStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedReconciliationStatus()){
			heartbeatServices.setReconciliationStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedSettlementStatus()){
			heartbeatServices.setSettlementStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedVoucherManagementStatus()){
			heartbeatServices.setVoucherManagementStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedWorkflowApprovalStatus()){
			heartbeatServices.setWorkflowApprovalStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedSchedulersStatus()){
			heartbeatServices.setSchedulersStatusAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedSmsEmailStatus()){
			heartbeatServices.setSmsEmailAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedCommissionManagementStatus()){
			heartbeatServices.setCommissionManagementAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedQuartzSchedulerStatus()){
			heartbeatServices.setQuartzSchedulerAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedQuartzSchedulerStatus()){
			heartbeatServices.setQuartzSchedulerAlive(isEjbAvailable);
		}
		if(monitoringCheck.isCheckedNosqlDatabaseStatus()){
			heartbeatServices.setNosqlDatabaseAlive(isNoSqlAlive());
		}
		if(monitoringCheck.isCheckedOracleDatabaseStatus()){
			heartbeatServices.setOracleDatabaseAlive(isOracleDatabaseAlive());
		}
		if(monitoringCheck.isCheckedMssqlDatabaseStatus()){
			heartbeatServices.setMssqlDatabaseAlive(isMssqlDatabaseAlive());
		}
		if(monitoringCheck.isCheckedMuleOneStatus()){
			heartbeatServices.setMuleOneAlive(isMuleOneAlive());
		}
		if(monitoringCheck.isCheckedMuleReceivingStatus()){
			heartbeatServices.setMuleReceivingAlive(isMuleReceiverAlive());
		}
		if(monitoringCheck.isCheckedMobileNetworkInterfaceStatus()){
			heartbeatServices.setMobileNetworkInterfaceAlive(true);
		}
		if(monitoringCheck.isCheckedSystemManagementPortalStatus()){
			heartbeatServices.setSystemManagementPortalAlive(isSystemManagementPortalAlive());
		}
		if(monitoringCheck.isCheckedReportingSystemStatus()){
			heartbeatServices.setReportingSystemAlive(isReportingPortalAlive());
		}
		return heartbeatServices;
	}

}
