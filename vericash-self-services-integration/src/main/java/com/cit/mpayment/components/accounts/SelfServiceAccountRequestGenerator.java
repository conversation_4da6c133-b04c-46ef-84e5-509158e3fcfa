package com.cit.mpayment.components.accounts;

import java.util.Properties;

import com.cit.mpayment.complaints.ServiceRequestsRequest;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.self.services.complaints.AccountTransaction;
import com.cit.shared.error.exception.CustomerException;
import com.cit.shared.error.exception.GeneralFailureException;
import com.google.gson.Gson;

public class SelfServiceAccountRequestGenerator{
	private Properties selfServiceProperties;
	private final String REQUESTS = "Requests";
	
	public Object onCall(BusinessMessage businessMessage) throws Exception {
		ServiceRequestsRequest createAccountRequest = new ServiceRequestsRequest();
		
		if(businessMessage.getPrimarySenderInfo().getPersonalDetails().getIdentifier()==null){
			throw new GeneralFailureException(CustomerException.CUSTOMER_BANK_NUMBER_IS_NULL);
		}
		
		if(businessMessage.getSelfServices().getBvn()!=null && !businessMessage.getSelfServices().getBvn().isEmpty()){
			createAccountRequest.setBvn(businessMessage.getSelfServices().getBvn());
		}
		
		createAccountRequest.setCustomerid(businessMessage.getPrimarySenderInfo().getPersonalDetails().getIdentifier());
		createAccountRequest.setCountry(businessMessage.getWalletInfo().getCountryIso2());
		createAccountRequest.setDescription(businessMessage.getSelfServices().getDescription());
		createAccountRequest.setOption(0);
		createAccountRequest.setServicerequest(14); 
		
		createAccountRequest.setRequesttype(REQUESTS);
		createAccountRequest.setAccounttype("");
		createAccountRequest.setSchemetypedescription("");
		AccountTransaction accountTransaction = new AccountTransaction();
		accountTransaction.setAmount(null);
		createAccountRequest.setTransaction(accountTransaction);
		
		Gson gson = new Gson();
		return gson.toJson(createAccountRequest);
	}
	
	public Properties getSelfServiceProperties() {
		return selfServiceProperties;
	}
	public void setSelfServiceProperties(Properties selfServiceProperties) {
		this.selfServiceProperties = selfServiceProperties;
	}

	
}
