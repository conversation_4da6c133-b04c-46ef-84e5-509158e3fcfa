package com.cit.mpayment.components.accounts;

import com.cit.mpayment.complaints.ServiceRequestsResponse;
import com.cit.mpaymentapp.common.message.BusinessMessage;

public class SelfServiceAccountResponseValidator{

	private final String SUCCESS_MESSAGE="Your account has been created successfully. You can go ahead and operate your new savings account ";
	private final String SUCCESS="Success";
	private final String SUCCESS_TICKET_MESSAGE = "Your request was submitted successfully ,please save the following number for follow up ";

	
	public Object onCall(BusinessMessage businessMessage, ServiceRequestsResponse accountResponse) throws Exception {

		if(accountResponse.getIsError()!=null && accountResponse.getAccountno()!=null && !accountResponse.getAccountno().isEmpty()){
		    businessMessage.getServiceSuccessResponse().setSuccessDescription(SUCCESS_MESSAGE+accountResponse.getAccountno()+".");
		    businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
		}else if(accountResponse.getTicketid()!=null && !accountResponse.getTicketid().isEmpty()){
			businessMessage.getServiceSuccessResponse().setSuccessDescription(SUCCESS_TICKET_MESSAGE+accountResponse.getTicketid()+".");
			businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
		}else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("EXT00810");
		}
		return businessMessage;
	}


}
