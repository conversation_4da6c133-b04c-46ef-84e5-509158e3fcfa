package com.cit.mpayment.statesandbranches;

import com.cit.mpayment.common.self.services.statesandbranches.State;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.shared.error.util.ExceptionResolver;

public class StatesAndBranchesResponseValidator  {

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {
		ExceptionResolver exception = null;


		if (!(payload instanceof StatesAndBranchesResponse)) {
			StatusType status = new StatusType();
			status.setErrorFlag(true);
			status.setStatusCode("EXT00810");
			businessMessage.setStatus(status);
		} else {
			StatesAndBranchesResponse statesAndBranchesResponse = (StatesAndBranchesResponse) payload;
			State[] states = statesAndBranchesResponse.getStates();
			businessMessage.getSelfServices().getStatesAndBranches().setStates(states);
		}

		return businessMessage;
	}

}
