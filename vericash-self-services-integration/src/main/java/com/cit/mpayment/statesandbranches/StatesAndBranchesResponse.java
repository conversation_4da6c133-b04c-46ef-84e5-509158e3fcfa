package com.cit.mpayment.statesandbranches;

import java.io.Serializable;

import com.cit.mpayment.common.self.services.statesandbranches.State;

public class StatesAndBranchesResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	private State[] states;

	public State[] getStates() {
		return states;
	}

	public void setStates(State[] states) {
		this.states = states;
	}

}
