package com.cit.mpayment;

import java.util.Properties;

import org.apache.commons.codec.binary.Base64;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class SelfServicesBasicAuthenticationComponent {

	Properties selfServiceProperties;
	private String SELF_SERVICES_AUTH_USER="self.services.auth.username";
	private String SELF_SERVICES_AUTH_PASSWORD="self.services.auth.password";
	private static String selfServicesAuth = null;

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		if(selfServicesAuth==null){
			String user = selfServiceProperties.getProperty(SELF_SERVICES_AUTH_USER);
		    String password = selfServiceProperties.getProperty(SELF_SERVICES_AUTH_PASSWORD);
		
		    selfServicesAuth= "Basic "+Base64.encodeBase64String(new String(user +":"+password).getBytes());
		
		}
		
		return selfServicesAuth;
	}
	
	public Properties getSelfServiceProperties() {
		return selfServiceProperties;
	}
	public void setSelfServiceProperties(Properties selfServiceProperties) {
		this.selfServiceProperties = selfServiceProperties;
	}

}
