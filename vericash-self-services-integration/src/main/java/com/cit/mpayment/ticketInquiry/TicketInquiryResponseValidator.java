package com.cit.mpayment.ticketInquiry;

import java.util.List;

import com.cit.mpayment.common.self.services.ticketInquiry.ResolutionHistory;
import com.cit.mpaymentapp.common.message.BusinessMessage;

public class TicketInquiryResponseValidator {

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {

		TicketInquiryResponse ticketInquiryResponse = (TicketInquiryResponse) payload;
		List<ResolutionHistory> resolutionHistories = ticketInquiryResponse.getResolutionhistory();

		if (resolutionHistories == null) {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("EXT00810");
		} else {
			businessMessage.getSelfServices().getTicketInquiry().setResolutionhistory(resolutionHistories);
		}

		return businessMessage;
	}

}
