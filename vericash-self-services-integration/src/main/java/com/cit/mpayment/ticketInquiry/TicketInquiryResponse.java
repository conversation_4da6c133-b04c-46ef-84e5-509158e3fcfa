package com.cit.mpayment.ticketInquiry;

import java.util.List;

import org.codehaus.jackson.annotate.JsonProperty;

import com.cit.mpayment.common.self.services.ticketInquiry.ResolutionHistory;

public class TicketInquiryResponse {

	@JsonProperty("resolutionhistory")
	List<ResolutionHistory> resolutionhistory;
	private String message;

	public List<ResolutionHistory> getResolutionhistory() {
		return resolutionhistory;
	}

	public void setResolutionhistory(List<ResolutionHistory> resolutionhistory) {
		this.resolutionhistory = resolutionhistory;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

}
