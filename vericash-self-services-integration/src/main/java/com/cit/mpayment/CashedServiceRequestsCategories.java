package com.cit.mpayment;

import java.util.Map;

import com.cit.mpaymentapp.common.self.services.complaints.ServiceCategoryTypes;

public class CashedServiceRequestsCategories {
	private static Map<String,ServiceCategoryTypes> cashedServiceRequests;

	public static Map<String, ServiceCategoryTypes> getCashedServiceRequests() {
		return cashedServiceRequests;
	}

	public static void setCashedServiceRequests(Map<String, ServiceCategoryTypes> cashedServiceRequests) {
		CashedServiceRequestsCategories.cashedServiceRequests = cashedServiceRequests;
	}
	
	public boolean isExist(String key){
				
		if (cashedServiceRequests != null && cashedServiceRequests.get(key) != null) {
			return true;
		}
		
		return false;
	}
	
	public ServiceCategoryTypes getServiceCategoryTypesByKey(String key){
		if (cashedServiceRequests != null && cashedServiceRequests.get(key) != null) {
			return cashedServiceRequests.get(key);
		}
		return null;
	}
	
}
