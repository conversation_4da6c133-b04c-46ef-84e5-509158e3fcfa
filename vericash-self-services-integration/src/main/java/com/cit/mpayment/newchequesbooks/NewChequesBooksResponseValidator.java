package com.cit.mpayment.newchequesbooks;

import com.cit.mpayment.complaints.ServiceRequestsResponse;
import com.cit.mpaymentapp.common.message.BusinessMessage;

public class NewChequesBooksResponseValidator {
	private final String SUCCESS = "Success";
	private final String SUCCESS_MESSAGE = "Your request was submitted successfully ,please save the following number for follow up ";

	public Object onCall(BusinessMessage businessMessage, ServiceRequestsResponse newChequeBookResponse) throws Exception {

		if (newChequeBookResponse.getTicketid() != null) {
			businessMessage.getServiceSuccessResponse()
					.setSuccessDescription(SUCCESS_MESSAGE + newChequeBookResponse.getTicketid());
			businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("EXT00810");
		}
		return businessMessage;
	}

}
