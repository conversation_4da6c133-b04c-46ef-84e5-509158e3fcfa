package com.cit.mpayment.newchequesbooks;

import java.text.SimpleDateFormat;

import com.cit.mpayment.complaints.ServiceRequestsRequest;
import com.cit.mpayment.customer.CustomerDetails;
import com.cit.mpaymentapp.cheque.managment.cheque.ServiceRequestsFullRequest;
import com.cit.mpaymentapp.common.accounts.CustomerAccount;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.PersonalDetails;
import com.cit.mpaymentapp.common.self.services.complaints.AccountTransaction;
import com.google.gson.Gson;

public class NewChequesBooksRequestGenerator{
	
	private final String REQUESTS = "Requests";
	private final String CUSTOMER_TYPE = "Retail";
	private NewChequesBooksRequestBody newChequesBooksRequestBody;
	private SimpleDateFormat simpleDateFormat;
	private Branch branch;
	private CustomerDetails customerDetails;
	private ServiceRequestsRequest createAccountRequest;
	private ServiceRequestsFullRequest fullRequest;

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		
		CustomerAccount customerAccount = businessMessage.getSelfServices().getCustomerAccount();
		PersonalDetails personalDetails = businessMessage.getPrimarySenderInfo().getPersonalDetails();
		createAccountRequest.setAccountno(customerAccount.getAccountno());
		createAccountRequest.setAccounttype(customerAccount.getAccounttype());
		createAccountRequest.setBranch(customerAccount.getBranchcode());
		createAccountRequest.setCountry(businessMessage.getWalletInfo().getCountryIso2());
		createAccountRequest.setCustomeremail(customerAccount.getEmail());
		createAccountRequest.setCustomername(customerAccount.getFirstname() + " " + customerAccount.getLastname());
		createAccountRequest.setCustomerphone(businessMessage.getPrimarySenderInfo().getMsisdn());
		createAccountRequest.setCustomertype(CUSTOMER_TYPE);
		createAccountRequest.setDescription(businessMessage.getSelfServices().getDescription());
		createAccountRequest.setFirstname(customerAccount.getFirstname());
		createAccountRequest.setHomeaddress1(personalDetails.getCityName());
		createAccountRequest.setHomeaddress2(personalDetails.getCityName());
		createAccountRequest.setLastname(customerAccount.getLastname());
		createAccountRequest.setMiddlename(customerAccount.getMiddlename());
		createAccountRequest.setOption(0);
		createAccountRequest.setServicerequest(13); 
		createAccountRequest.setPostalcode("");
		createAccountRequest.setRequesttype(REQUESTS);
		createAccountRequest.setSchemetype(customerAccount.getSchemecode());
		createAccountRequest.setSchemetypedescription(customerAccount.getAccounttype());
		createAccountRequest.setState(customerAccount.getStatecode());
		createAccountRequest.setFilename("");
		AccountTransaction accountTransaction = new AccountTransaction();
		createAccountRequest.setTransaction(accountTransaction);
		fullRequest.setBody(createAccountRequest);
		branch.setAddress(businessMessage.getSelfServices().getNewcheque().getCustomer().getBranch().getAddress());
		branch.setSolid(businessMessage.getSelfServices().getNewcheque().getCustomer().getBranch().getSolid());

		customerDetails.setAccountno(businessMessage.getSelfServices().getCustomerAccount().getAccountno());
		customerDetails.setBranch(branch);

		newChequesBooksRequestBody.setNo(businessMessage.getSelfServices().getNewcheque().getNoBooks());
		newChequesBooksRequestBody.setLeaves(businessMessage.getSelfServices().getNewcheque().getNoOfLeaves());
		newChequesBooksRequestBody.setChequetypeid(businessMessage.getSelfServices().getNewcheque().getChequetypeid());
		newChequesBooksRequestBody.setCustomer(customerDetails);
		fullRequest.setNewcheque(newChequesBooksRequestBody);
		
		Gson gson = new Gson();
		return gson.toJson(fullRequest);
	}

	public NewChequesBooksRequestBody getNewChequesBooksRequestBody() {
		return newChequesBooksRequestBody;
	}

	public void setNewChequesBooksRequestBody(NewChequesBooksRequestBody newChequesBooksRequestBody) {
		this.newChequesBooksRequestBody = newChequesBooksRequestBody;
	}

	public SimpleDateFormat getSimpleDateFormat() {
		return simpleDateFormat;
	}

	public void setSimpleDateFormat(SimpleDateFormat simpleDateFormat) {
		this.simpleDateFormat = simpleDateFormat;
	}
	public Branch getBranch() {
		return branch;
	}

	public void setBranch(Branch branch) {
		this.branch = branch;
	}

	public ServiceRequestsRequest getCreateAccountRequest() {
		return createAccountRequest;
	}

	public void setCreateAccountRequest(ServiceRequestsRequest createAccountRequest) {
		this.createAccountRequest = createAccountRequest;
	}

	public ServiceRequestsFullRequest getFullRequest() {
		return fullRequest;
	}

	public void setFullRequest(ServiceRequestsFullRequest fullRequest) {
		this.fullRequest = fullRequest;
	}

	public CustomerDetails getCustomerDetails() {
		return customerDetails;
	}

	public void setCustomerDetails(CustomerDetails customerDetails) {
		this.customerDetails = customerDetails;
	}
	
}
