package com.cit.mpayment.newchequesbooks;

import java.io.Serializable;



import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpayment.customer.CustomerDetails;


@JsonAutoDetect
public class NewChequesBooksRequestBody implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String no;
	private String leaves;
	private String Chequetypeid;
	private CustomerDetails customer;
	
	
	public String getNo() {
		return no;
	}
	public void setNo(String no) {
		this.no = no;
	}
	public String getLeaves() {
		return leaves;
	}
	public void setLeaves(String leaves) {
		this.leaves = leaves;
	}

	public String getChequetypeid() {
		return Chequetypeid;
	}
	public void setChequetypeid(String chequetypeid) {
		Chequetypeid = chequetypeid;
	}
	public CustomerDetails getCustomer() {
		return customer;
	}
	public void setCustomer(CustomerDetails customer) {
		this.customer = customer;
	}
		
	
	
}
