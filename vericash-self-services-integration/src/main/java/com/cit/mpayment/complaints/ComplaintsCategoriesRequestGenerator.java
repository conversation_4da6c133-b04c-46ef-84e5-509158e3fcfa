package com.cit.mpayment.complaints;

import com.cit.mpayment.CashedServiceRequestsCategories;
import com.cit.mpaymentapp.common.message.BusinessMessage;

public class ComplaintsCategoriesRequestGenerator {

	private CashedServiceRequestsCategories cashedServiceRequestsCategories;

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		StringBuilder queryParameters = new StringBuilder();

		String serviceRequestsKey = businessMessage.getWalletInfo().getCountryIso2();

		queryParameters.append("/" + businessMessage.getWalletInfo().getCountryIso2());
		queryParameters.append("/servicerequests");
		queryParameters.append("?accountype=");
		if (businessMessage.getSelfServices().getComplaints() != null
				&& businessMessage.getSelfServices().getComplaints().getComplainAccount() != null
				&& businessMessage.getSelfServices().getComplaints().getComplainAccount().getAccounttype() != null) {
			queryParameters
					.append(businessMessage.getSelfServices().getComplaints().getComplainAccount().getAccounttype());
			serviceRequestsKey
					.concat(businessMessage.getSelfServices().getComplaints().getComplainAccount().getAccounttype());
		} else if (businessMessage.getSelfServices().getCustomerAccount() != null
				&& businessMessage.getSelfServices().getCustomerAccount().getAccounttype() != null) {
			queryParameters.append(businessMessage.getSelfServices().getCustomerAccount().getAccounttype());
			serviceRequestsKey.concat(businessMessage.getSelfServices().getCustomerAccount().getAccounttype());
		} else {
			queryParameters.append("current");
			serviceRequestsKey.concat("current");
		}

		Boolean requireIntegration = true;

		if (cashedServiceRequestsCategories.isExist(serviceRequestsKey)) {
			businessMessage.getSelfServices().getComplaints().setServiceCategoryTypes(cashedServiceRequestsCategories.getServiceCategoryTypesByKey(serviceRequestsKey));
			requireIntegration = false;
			businessMessage.getSoftFields().put("requireIntegration", requireIntegration);
			return businessMessage;
		}
		businessMessage.getSoftFields().put("requireIntegration", requireIntegration);
		businessMessage.getSoftFields().put("queryParameters", queryParameters);
		return businessMessage;
	}

	public CashedServiceRequestsCategories getCashedServiceRequestsCategories() {
		return cashedServiceRequestsCategories;
	}

	public void setCashedServiceRequestsCategories(CashedServiceRequestsCategories cashedServiceRequestsCategories) {
		this.cashedServiceRequestsCategories = cashedServiceRequestsCategories;
	}
}
