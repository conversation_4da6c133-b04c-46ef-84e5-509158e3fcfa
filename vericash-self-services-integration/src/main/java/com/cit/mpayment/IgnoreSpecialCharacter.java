package com.cit.mpayment;

import com.cit.mpayment.statesandbranches.StatesAndBranchesResponse;
import com.google.gson.Gson;

public class IgnoreSpecialCharacter {

	public Object onCall(String payload) throws Exception {
		String bodyWithSpecialCharacter = null;
		String bodyWithoutSpecialCharacter = null;
		if (payload instanceof String) {
			bodyWithSpecialCharacter = payload;
			bodyWithoutSpecialCharacter = bodyWithSpecialCharacter.replaceAll("[^\\x20-\\x7E]", "");
		} else {
			return payload;
		}
		Gson gson = new Gson();
		return gson.fromJson(bodyWithoutSpecialCharacter, StatesAndBranchesResponse.class);
	}

}
