package com.cit.mpaymentapp.cardmngt.cards;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.common.cards.CardType;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.shared.error.util.ExceptionResolver;

public class CardTypesResponseValidator{

	private final String CARD_TYPES_URL = "card_types_images_url";

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {
		List<CardType> cardTypesList = new ArrayList<CardType>();
		ExceptionResolver exception = null;

		if (!(payload instanceof CardTypesResponse)) {
			StatusType status = new StatusType();
			status.setErrorFlag(true);
			status.setStatusCode("EXT00810");
			businessMessage.setStatus(status);
		} else {
			CardTypesResponse cardTypes = (CardTypesResponse) payload;
			cardTypesList = cardTypes.getCardtypes();
			
			for (CardType cardType : cardTypesList){
				cardType.setImag(System.getenv(CARD_TYPES_URL) + cardType.getType() + ".png");
			}
			
			businessMessage.getSelfServices().setCardtypes(cardTypesList);
		}

		return businessMessage;
	}

}
