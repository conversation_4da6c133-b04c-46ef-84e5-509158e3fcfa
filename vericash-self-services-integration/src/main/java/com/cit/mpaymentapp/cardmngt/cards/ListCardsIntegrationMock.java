package com.cit.mpaymentapp.cardmngt.cards;

import java.util.ArrayList;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.common.cards.AccountCard;
import com.cit.mpaymentapp.common.message.BusinessMessage;

@JsonAutoDetect
public class ListCardsIntegrationMock{

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		List<AccountCard> cards = new ArrayList<AccountCard>();

		AccountCard card1 = new AccountCard();
		card1.setCode("**********");
		card1.setName("Customer Card 1");
		card1.setProductname("UBA_INSTANT_VERVE");
		card1.setType("VERVE");
		card1.setPan("1234");
		card1.setId("123123");
		card1.setAccountno("**********");

		AccountCard card2 = new AccountCard();
		card2.setCode("**********");
		card2.setName("Customer Card 2");
		card2.setProductname("UBA_INSTANT_MASTERCARD");
		card2.setType("MASTERCARD");
		card2.setPan("1234");
		card2.setId("123123");
		card2.setAccountno("**********");

		cards.add(card1);
		cards.add(card2);

		businessMessage.getSelfServices().setCards(cards);

		return businessMessage;
	}

}
