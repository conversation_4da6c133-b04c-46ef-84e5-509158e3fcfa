package com.cit.mpaymentapp.cardmngt.accounts;

import java.util.List;

import com.cit.mpaymentapp.common.accounts.CustomerAccount;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.mpaymentapp.common.self.services.SelfServices;
import com.cit.shared.error.util.ExceptionResolver;

public class ListAccountsResponseValidator {

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {
		ExceptionResolver exception = null;


		if (!(payload instanceof ListAccountsResponse)) {
			StatusType status = new StatusType();
			status.setErrorFlag(true);
			status.setStatusCode("EXT00810");
			businessMessage.setStatus(status);
		} else {
			ListAccountsResponse listAccountsResponse = (ListAccountsResponse) payload;
			List<CustomerAccount> customerAccounts = listAccountsResponse.getAccounts();
			if (businessMessage.getSelfServices() == null) {
				SelfServices selfServices = new SelfServices();
				selfServices.setAccounts(customerAccounts);
				businessMessage.setSelfServices(selfServices);
				return businessMessage;
			}
			
			businessMessage.getSelfServices().setAccounts(customerAccounts);
		}

		return businessMessage;
	}

}
