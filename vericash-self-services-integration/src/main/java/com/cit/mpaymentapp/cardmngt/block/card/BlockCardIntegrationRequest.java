package com.cit.mpaymentapp.cardmngt.block.card;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class BlockCardIntegrationRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	private BlockCardRequest card;

	public BlockCardRequest getCard() {
		return card;
	}

	public void setCard(BlockCardRequest card) {
		this.card = card;
	}

}
