package com.cit.mpaymentapp.cardmngt.cards;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.common.accounts.CustomerAccount;
import com.cit.mpaymentapp.common.cards.Account;
import com.cit.mpaymentapp.common.message.BusinessMessage;

public class ListCardsRequestGenerator{

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		List<Account> accounts = new ArrayList<Account>();
		List<CustomerAccount> customerAccounts = businessMessage.getSelfServices().getAccounts();
		for (CustomerAccount customerAccount : customerAccounts) {
			Account account = new Account();
			account.setNo(customerAccount.getAccountno());
			accounts.add(account);
		}

		ListCardsRequest listCardsRequest = new ListCardsRequest();

		listCardsRequest.setAccounts(accounts);
		return listCardsRequest;
	}

}
