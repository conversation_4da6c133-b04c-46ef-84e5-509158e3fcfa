package com.cit.mpaymentapp.cardmngt.block.card;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.common.block.card.BlockCardReason;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.self.services.SelfServices;

public class BlockCardReasonMock  {

	private SelfServices selfServices;

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		List<BlockCardReason> blockCardReason = new ArrayList<BlockCardReason>();

		BlockCardReason cardReason_1 = new BlockCardReason();
		cardReason_1.setCode("S2218");
		cardReason_1.setReason("Lost / Misplaced Card");

		BlockCardReason cardReason_2 = new BlockCardReason();
		cardReason_2.setCode("S2220");
		cardReason_2.setReason("Fraud / Suspicious Transactions");

		BlockCardReason cardReason_3 = new BlockCardReason();
		cardReason_3.setCode("S2219");
		cardReason_3.setReason("Stolen (Robbery / Mugging) Card");

		blockCardReason.add(cardReason_1);
		blockCardReason.add(cardReason_2);
		blockCardReason.add(cardReason_3);

		businessMessage.setSelfServices(selfServices);
		businessMessage.getSelfServices().setBlockCardReason(blockCardReason);

		return businessMessage;

	}

	public SelfServices getSelfServices() {
		return selfServices;
	}

	public void setSelfServices(SelfServices selfServices) {
		this.selfServices = selfServices;
	}

}
