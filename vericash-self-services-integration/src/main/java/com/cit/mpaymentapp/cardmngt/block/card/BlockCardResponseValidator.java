package com.cit.mpaymentapp.cardmngt.block.card;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class BlockCardResponseValidator {

	private final String SUCCESS = "Success";
	private final String SUCCESS_MESSAGE = "Your request was submitted successfully, please save the following number for follow up ";

	public Object onCall(BusinessMessage businessMessage, BlockCardResponse blockCardResponse) throws Exception {

		if (blockCardResponse.getTicketid() != null) {
			businessMessage.getServiceSuccessResponse()
					.setSuccessDescription(SUCCESS_MESSAGE + blockCardResponse.getTicketid());
			businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("EXT00810");
		}

		return businessMessage;

	}

}
