package com.cit.mpaymentapp.cardmngt.cards;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpayment.complaints.ServiceRequestsRequest;
import com.cit.mpaymentapp.common.cards.CardDetails;
import com.cit.mpaymentapp.common.cards.CardType;

@JsonAutoDetect
public class NewCardRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	private CardType cardtype;
	private ServiceRequestsRequest body;
	private CardDetails customer;

	public CardType getCardtype() {
		return cardtype;
	}

	public void setCardtype(CardType cardtype) {
		this.cardtype = cardtype;
	}

	public ServiceRequestsRequest getBody() {
		return body;
	}

	public void setBody(ServiceRequestsRequest body) {
		this.body = body;
	}

	public CardDetails getCustomer() {
		return customer;
	}

	public void setCustomer(CardDetails customer) {
		this.customer = customer;
	}

}
