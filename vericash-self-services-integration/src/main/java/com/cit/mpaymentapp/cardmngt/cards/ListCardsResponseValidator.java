package com.cit.mpaymentapp.cardmngt.cards;

import java.util.List;

import com.cit.mpaymentapp.common.cards.AccountCard;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.shared.error.util.ExceptionResolver;

public class ListCardsResponseValidator{

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {
		ExceptionResolver exception = null;


		if (!(payload instanceof ListCardsResponse)) {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("EXT00810");
		} else {
			ListCardsResponse listCardsResponse = (ListCardsResponse) payload;
			List<AccountCard> accountCards = listCardsResponse.getCards();
			businessMessage.getSelfServices().setCards(accountCards);
		}

		return businessMessage;
	}

}
