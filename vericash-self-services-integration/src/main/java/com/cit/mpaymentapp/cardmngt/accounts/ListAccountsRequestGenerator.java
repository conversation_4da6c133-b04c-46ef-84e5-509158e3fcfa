package com.cit.mpaymentapp.cardmngt.accounts;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.common.accounts.Customer;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.PersonalDetails;
import com.google.gson.Gson;

public class ListAccountsRequestGenerator{


	public Object onCall(BusinessMessage businessMessage) throws Exception {
		PersonalDetails personalDetails = businessMessage.getPrimarySenderInfo().getPersonalDetails();
		ListAccountsRequest listAccountsRequest = new ListAccountsRequest();

		List<Customer> customers = new ArrayList<Customer>();
		Customer customer = new Customer();
		customer.setId(personalDetails.getIdentifier());
		customers.add(customer);

		listAccountsRequest.setCustomers(customers);

		Gson gson = new Gson();
		return gson.toJson(listAccountsRequest);		
	}

}
