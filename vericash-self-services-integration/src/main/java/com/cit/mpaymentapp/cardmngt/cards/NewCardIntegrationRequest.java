package com.cit.mpaymentapp.cardmngt.cards;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

@JsonAutoDetect
public class NewCardIntegrationRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	private NewCardRequest newcard;
	public NewCardRequest getNewcard() {
		return newcard;
	}
	public void setNewcard(NewCardRequest newcard) {
		this.newcard = newcard;
	}
	
	

}
