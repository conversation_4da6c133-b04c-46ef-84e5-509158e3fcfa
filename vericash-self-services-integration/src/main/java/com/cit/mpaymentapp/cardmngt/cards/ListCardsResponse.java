package com.cit.mpaymentapp.cardmngt.cards;

import java.io.Serializable;
import java.util.List;

import com.cit.mpaymentapp.common.cards.AccountCard;

public class ListCardsResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	List<AccountCard> cards;

	public List<AccountCard> getCards() {
		return cards;
	}

	public void setCards(List<AccountCard> cards) {
		this.cards = cards;
	}

}
