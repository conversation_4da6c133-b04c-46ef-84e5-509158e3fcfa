package com.cit.mpaymentapp.cardmngt.cards;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class NewCardResponseValidator {

	private final String SUCCESS = "Success";
	private final String SUCCESS_MESSAGE = "Your request was submitted successfully, please save the following number for follow up ";

	public Object onCall(BusinessMessage businessMessage, NewCardResponse newCardResponse) throws Exception {

		if (newCardResponse.getTicketid() != null) {
			businessMessage.getServiceSuccessResponse()
					.setSuccessDescription(SUCCESS_MESSAGE + newCardResponse.getTicketid());
			businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
		} else {
			businessMessage.getStatus().setErrorFlag(true);
			businessMessage.getStatus().setStatusCode("EXT00810");
		}
		
		return businessMessage;
	}

}
