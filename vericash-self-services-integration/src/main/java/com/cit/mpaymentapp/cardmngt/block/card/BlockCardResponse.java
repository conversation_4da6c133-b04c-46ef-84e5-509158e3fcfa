package com.cit.mpaymentapp.cardmngt.block.card;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;
import org.codehaus.jackson.annotate.JsonProperty;

@JsonAutoDetect
public class BlockCardResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	private String ticketid;
	private String status;
	@JsonProperty()
	private String TAT;
	private boolean isError;
	private String message;

	public String getTicketid() {
		return ticketid;
	}

	public void setTicketid(String ticketid) {
		this.ticketid = ticketid;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getTAT() {
		return TAT;
	}

	public void setTAT(String tAT) {
		TAT = tAT;
	}

	public boolean getIsError() {
		return isError;
	}

	public void setIsError(boolean isError) {
		this.isError = isError;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

}
