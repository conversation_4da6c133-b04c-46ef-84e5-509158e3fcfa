package com.cit.mpaymentapp.cardmngt.cards;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.common.cards.CardType;

@JsonAutoDetect
public class CardTypesResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	private List<CardType> cardtypes;

	public List<CardType> getCardtypes() {
		return cardtypes;
	}

	public void setCardtypes(List<CardType> cardtypes) {
		this.cardtypes = cardtypes;
	}

}
