package com.cit.mpaymentapp.cardmngt.cards;

import com.cit.mpayment.complaints.ServiceRequestsRequest;
import com.cit.mpaymentapp.common.accounts.CustomerAccount;
import com.cit.mpaymentapp.common.cards.CardBranch;
import com.cit.mpaymentapp.common.cards.CardDetails;
import com.cit.mpaymentapp.common.cards.CardType;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.PersonalDetails;
import com.cit.mpaymentapp.common.self.services.complaints.AccountTransaction;
import com.google.gson.Gson;

public class NewCardRequestGenerator {

	private final String REQUEST = "Request";
	private final String CUSTOMER_TYPE = "Retail";

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		
		CustomerAccount customerAccount = businessMessage.getSelfServices().getCustomerAccount();
		PersonalDetails personalDetails = businessMessage.getPrimarySenderInfo().getPersonalDetails();
		NewCardRequest newcardRequest = new NewCardRequest();

		CardType cardtype = businessMessage.getSelfServices().getNewCardSelfService().getCardtype();
		cardtype.setAccounttocredit(cardtype.getAccountcreditprefix() + "{SOL}" 
		+ cardtype.getAccountcreditprefix());
		
		ServiceRequestsRequest body = new ServiceRequestsRequest();
		body.setAccountno(customerAccount.getAccountno());
		body.setBranch(customerAccount.getBranchcode());
		body.setState(customerAccount.getStatecode());
		body.setServicerequest(2);
		body.setOption(0);
		body.setCustomername(customerAccount.getLastname() + " " + customerAccount.getFirstname());
		body.setLastname(customerAccount.getLastname());
		body.setFirstname(customerAccount.getFirstname());
		body.setMiddlename(customerAccount.getMiddlename());
		body.setPostalcode("");
		body.setSchemetype(customerAccount.getSchemecode());
		body.setSchemetypedescription(customerAccount.getAccounttype());
		body.setAccounttype(customerAccount.getAccounttype());
		body.setRequesttype(REQUEST);
		body.setCountry(businessMessage.getWalletInfo().getCountryIso2());
		body.setCustomeremail(customerAccount.getEmail());
		body.setHomeaddress1(personalDetails.getCityName());
		body.setHomeaddress2(personalDetails.getCityName());
		body.setCustomerphone(businessMessage.getPrimarySenderInfo().getMsisdn());
		body.setCustomertype(CUSTOMER_TYPE);
		body.setDescription("Request NEW CARD");
		body.setTransaction(new AccountTransaction());
		
		
		CardBranch cardBranch = new CardBranch();
		cardBranch.setSolid(businessMessage.getSelfServices().getNewCardSelfService().getBranchCode());
		cardBranch.setAddress(businessMessage.getSelfServices().getNewCardSelfService().getStateCode());
		CardDetails customer = new CardDetails();
		customer.setAccountno(customerAccount.getAccountno());
		customer.setCardname(businessMessage.getSelfServices().getNewCardSelfService().getCardname());
		customer.setBranch(cardBranch);
		
		newcardRequest.setCardtype(cardtype);
		newcardRequest.setBody(body);
		newcardRequest.setCustomer(customer);
		
		NewCardIntegrationRequest newcard = new NewCardIntegrationRequest();
		newcard.setNewcard(newcardRequest);
		
		Gson gson = new Gson();
		return gson.toJson(newcardRequest);
	}

}
