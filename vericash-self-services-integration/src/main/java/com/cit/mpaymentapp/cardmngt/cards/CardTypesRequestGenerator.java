package com.cit.mpaymentapp.cardmngt.cards;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class CardTypesRequestGenerator{

	public Object onCall(BusinessMessage businessMessage) throws Exception {

		String schemeCode = businessMessage.getSelfServices().getCustomerAccount().getSchemecode();
		String branch = null;
		if (businessMessage.getSelfServices().getCustomerAccount().getBranchcode() != null && 
				!businessMessage.getSelfServices().getCustomerAccount().getBranchcode().isEmpty())
			branch = businessMessage.getSelfServices().getCustomerAccount().getBranchcode();

		StringBuilder queryParameters = new StringBuilder();
		queryParameters.append(schemeCode);
		queryParameters.append("/?branch=" + branch);


		return branch;
	}

}
