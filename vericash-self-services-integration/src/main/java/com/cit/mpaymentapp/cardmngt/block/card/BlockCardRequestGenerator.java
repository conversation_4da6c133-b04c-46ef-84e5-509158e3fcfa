package com.cit.mpaymentapp.cardmngt.block.card;

import java.util.List;

import com.cit.mpaymentapp.common.accounts.CustomerAccount;
import com.cit.mpaymentapp.common.block.card.BlockCardSelfService;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.google.gson.Gson;

public class BlockCardRequestGenerator  {

	private final String REQUEST = "Request";
	private final String CUSTOMER_TYPE = "Retail";

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		List<CustomerAccount> customerAccounts = businessMessage.getSelfServices().getAccounts();
		String customerAccountNumber = businessMessage.getSelfServices().getBlockCardSelfService().getAccountno();
		BlockCardSelfService blockCardSelfService = businessMessage.getSelfServices().getBlockCardSelfService();
		BlockCardRequest blockCardRequest = new BlockCardRequest();

		try {
			for (CustomerAccount account : customerAccounts) {
				if (account.getAccountno().equals(customerAccountNumber)) {

					blockCardRequest.setCode(blockCardSelfService.getCode());
					blockCardRequest.setName(blockCardSelfService.getName());
					blockCardRequest.setProductname(blockCardSelfService.getProductname());
					blockCardRequest.setType(blockCardSelfService.getType());
					blockCardRequest.setId(blockCardSelfService.getId());
					blockCardRequest.setAccountno(account.getAccountno());
					blockCardRequest.setAccounttype(account.getAccounttype());
					blockCardRequest.setBranch(account.getBranchcode());
					blockCardRequest.setCountry(businessMessage.getWalletInfo().getCountryIso2());
					blockCardRequest.setCustomeremail(account.getEmail());
					blockCardRequest.setCustomername(account.getLastname() + " " + account.getFirstname());
					blockCardRequest.setCustomerphone(account.getMobileno());
					blockCardRequest.setCustomertype(CUSTOMER_TYPE);
					blockCardRequest.setFirstname(account.getFirstname());
					blockCardRequest.setLastname(account.getLastname());
					blockCardRequest.setOption(0);
					blockCardRequest.setRequesttype(REQUEST);
					blockCardRequest.setServicerequest(1);
					blockCardRequest.setState(account.getStatecode());
					blockCardRequest.setBlockcode(blockCardSelfService.getBlockCardReason().getCode());
					break;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		BlockCardIntegrationRequest card = new BlockCardIntegrationRequest();
		card.setCard(blockCardRequest);
		Gson gson = new Gson();
		return gson.toJson(card);
	}

}
