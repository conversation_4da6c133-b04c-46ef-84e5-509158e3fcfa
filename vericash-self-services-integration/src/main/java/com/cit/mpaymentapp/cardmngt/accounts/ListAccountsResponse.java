package com.cit.mpaymentapp.cardmngt.accounts;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.common.accounts.Customer;
import com.cit.mpaymentapp.common.accounts.CustomerAccount;

@JsonAutoDetect
public class ListAccountsResponse implements Serializable {

	private static final long serialVersionUID = 1L;
	private String customerid;
	private String customername;
	private String customertype;
	private List<CustomerAccount> accounts;

	public String getCustomerid() {
		return customerid;
	}

	public void setCustomerid(String customerid) {
		this.customerid = customerid;
	}

	public String getCustomername() {
		return customername;
	}

	public void setCustomername(String customername) {
		this.customername = customername;
	}

	public String getCustomertype() {
		return customertype;
	}

	public void setCustomertype(String customertype) {
		this.customertype = customertype;
	}

	public List<CustomerAccount> getAccounts() {
		return accounts;
	}

	public void setAccounts(List<CustomerAccount> accounts) {
		this.accounts = accounts;
	}

}
