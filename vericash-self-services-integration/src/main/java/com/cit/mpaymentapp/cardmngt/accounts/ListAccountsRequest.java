package com.cit.mpaymentapp.cardmngt.accounts;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.common.accounts.Customer;

@JsonAutoDetect
public class ListAccountsRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	private List<Customer> customers;

	public List<Customer> getCustomers() {
		return customers;
	}

	public void setCustomers(List<Customer> customers) {
		this.customers = customers;
	}

}
