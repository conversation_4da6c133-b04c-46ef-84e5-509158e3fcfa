package com.cit.mpaymentapp.cheque.managment.type;

import java.util.ArrayList;
import java.util.List;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.mpaymentapp.common.self.services.SelfServices;
import com.cit.mpaymentapp.common.type.ChequeTypeResponse;
import com.cit.shared.error.util.ExceptionResolver;

public class ChequeTypeResponseValidator{

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {
		List<ChequeTypeResponse> chequeTypeList = new ArrayList<ChequeTypeResponse>();
		ExceptionResolver exception = null;

		if (!(payload instanceof ChequeTypes)) {
			StatusType status = new StatusType();
			status.setErrorFlag(true);
			status.setStatusCode("EXT00810");
			businessMessage.setStatus(status);
		} else {
			ChequeTypes chequeTypes = (ChequeTypes) payload;
			chequeTypeList = chequeTypes.getChequetypes();
			SelfServices selfServices = new SelfServices();
			selfServices.setChequetypes(chequeTypeList);
			businessMessage.setSelfServices(selfServices);
		}

		return businessMessage;
	}

}
