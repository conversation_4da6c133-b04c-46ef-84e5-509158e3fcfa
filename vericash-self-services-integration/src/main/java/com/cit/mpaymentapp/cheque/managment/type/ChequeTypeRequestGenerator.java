package com.cit.mpaymentapp.cheque.managment.type;

import com.cit.mpaymentapp.common.message.BusinessMessage;

public class ChequeTypeRequestGenerator{

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		String customerAccount = null;
		if (businessMessage != null && businessMessage.getSelfServices() != null
				&& businessMessage.getSelfServices().getChequetype() != null
				&& businessMessage.getSelfServices().getChequetype().getCustacct() != null) {
			customerAccount = businessMessage.getSelfServices().getChequetype().getCustacct();

		}
		return customerAccount;
	}
}
