package com.cit.mpaymentapp.cheque.managment.stop;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.cheque.managment.cheque.ChequeManagment;

@JsonAutoDetect
public class StopChequeNodeElementRequest implements Serializable,ChequeManagment{
	private static final long serialVersionUID = 1L;
	private StopChequeRequest cheque;
	public StopChequeRequest getCheque() {
		return cheque;
	}
	public void setCheque(StopChequeRequest cheque) {
		this.cheque = cheque;
	}
	@Override
	public String toString() {
		return "StopChequeNodeElementRequest [cheque=" + cheque + "]";
	}
}
