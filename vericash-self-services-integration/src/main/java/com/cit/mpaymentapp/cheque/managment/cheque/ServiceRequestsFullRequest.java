package com.cit.mpaymentapp.cheque.managment.cheque;
import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpayment.complaints.ServiceRequestsRequest;
import com.cit.mpayment.newchequesbooks.NewChequesBooksRequestBody;
import com.cit.mpaymentapp.cheque.managment.cheque.ChequeManagment;
@JsonAutoDetect
public class ServiceRequestsFullRequest implements Serializable{
	private static final long serialVersionUID = 1L;
	private ServiceRequestsRequest body;
	private List<ChequeManagment> cheques;
	private NewChequesBooksRequestBody newcheque;
		
	public ServiceRequestsRequest getBody() {
		return body;
	}
	public void setBody(ServiceRequestsRequest body) {
		this.body = body;
	}
	
	public List<ChequeManagment> getCheques() {
		return cheques;
	}
	public void setCheques(List<ChequeManagment> cheques) {
		this.cheques = cheques;
	}
	public NewChequesBooksRequestBody getNewcheque() {
		return newcheque;
	}
	public void setNewcheque(NewChequesBooksRequestBody newcheque) {
		this.newcheque = newcheque;
	}

	
	
}
