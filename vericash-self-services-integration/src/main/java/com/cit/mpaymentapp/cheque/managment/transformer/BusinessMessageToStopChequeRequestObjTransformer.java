package com.cit.mpaymentapp.cheque.managment.transformer;

import java.util.ArrayList;
import java.util.List;


import com.cit.mpayment.complaints.ServiceRequestsFullRequest;
import com.cit.mpayment.complaints.ServiceRequestsRequest;
import com.cit.mpaymentapp.cheque.managment.cheque.ChequeManagment;
import com.cit.mpaymentapp.cheque.managment.stop.StopChequeNodeElementRequest;
import com.cit.mpaymentapp.cheque.managment.stop.StopChequeRequest;
import com.cit.mpaymentapp.common.accounts.CustomerAccount;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.PersonalDetails;
import com.cit.mpaymentapp.common.self.services.complaints.AccountTransaction;
import com.google.gson.Gson;

public class BusinessMessageToStopChequeRequestObjTransformer {
	private final String REQUESTS = "Requests";
	private final String CUSTOMER_TYPE = "Retail";

	public Object doTransform(BusinessMessage businessMessage) {
		StopChequeNodeElementRequest stopChequeNodeElementRequest = new StopChequeNodeElementRequest();
		StopChequeRequest cheque = new StopChequeRequest();
		ServiceRequestsFullRequest serviceRequestsFullRequest = new ServiceRequestsFullRequest();
		CustomerAccount customerAccount = businessMessage.getSelfServices().getCustomerAccount();
		PersonalDetails personalDetails = businessMessage.getPrimarySenderInfo().getPersonalDetails();
		ServiceRequestsRequest createAccountRequest = new ServiceRequestsRequest();

		createAccountRequest.setAccountno(customerAccount.getAccountno());
		createAccountRequest.setAccounttype(customerAccount.getAccounttype());
		createAccountRequest.setBranch(customerAccount.getBranchcode());
		createAccountRequest.setCountry(businessMessage.getWalletInfo().getCountryIso2());
		createAccountRequest.setCustomeremail(customerAccount.getEmail());
		createAccountRequest.setCustomername(customerAccount.getFirstname() + " " + customerAccount.getLastname());
		createAccountRequest.setCustomerphone(businessMessage.getPrimarySenderInfo().getMsisdn());
		createAccountRequest.setCustomertype(CUSTOMER_TYPE);
		createAccountRequest.setDescription(businessMessage.getSelfServices().getDescription());
		createAccountRequest.setFirstname(customerAccount.getFirstname());
		createAccountRequest.setHomeaddress1(personalDetails.getCityName());
		createAccountRequest.setHomeaddress2(personalDetails.getCityName());
		createAccountRequest.setLastname(customerAccount.getLastname());
		createAccountRequest.setMiddlename(customerAccount.getMiddlename());
		createAccountRequest.setOption(0);
		createAccountRequest.setServicerequest(11); 
		
		createAccountRequest.setPostalcode("");
		createAccountRequest.setRequesttype(REQUESTS);
		createAccountRequest.setSchemetype(customerAccount.getSchemecode());
		createAccountRequest.setSchemetypedescription(customerAccount.getAccounttype());
		createAccountRequest.setState(customerAccount.getStatecode());
		createAccountRequest.setFilename("");
		AccountTransaction accountTransaction = new AccountTransaction();
		accountTransaction.setAmount("0");
		createAccountRequest.setTransaction(accountTransaction);
		
		cheque.setAmount(businessMessage.getSelfServices().getStopChequeSelfService().getAmount());
		cheque.setBeneficiary(businessMessage.getSelfServices().getStopChequeSelfService().getBeneficiary());
		cheque.setChequeno(businessMessage.getSelfServices().getStopChequeSelfService().getChequeno());
		cheque.setDateissued(businessMessage.getSelfServices().getStopChequeSelfService().getDateissued());
		cheque.setReason(businessMessage.getSelfServices().getStopChequeSelfService().getReason());
		
		stopChequeNodeElementRequest.setCheque(cheque);
		
		List<ChequeManagment> cheques = new ArrayList<ChequeManagment>();
		
		cheques.add(stopChequeNodeElementRequest);
		
		serviceRequestsFullRequest.setBody(createAccountRequest);
		serviceRequestsFullRequest.setCheques(cheques);
		
		Gson gson = new Gson();
		return gson.toJson(serviceRequestsFullRequest);
	}

}
