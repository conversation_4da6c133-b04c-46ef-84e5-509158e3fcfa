package com.cit.mpaymentapp.cheque.managment.stop;

import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.self.services.SelfServices;
import com.cit.mpaymentapp.common.stop.cheque.StopChequeReason;

public class StopChequeReasonsMock{

	private SelfServices selfServices;
	

	public Object onCall(BusinessMessage businessMessage) throws Exception {
		StopChequeReason[] stopChequeReasons  = new StopChequeReason[7];
		
		StopChequeReason reason1 = new StopChequeReason();
		reason1.setReason("Stolen Cheque");
		reason1.setReasonId("01");
		
		StopChequeReason reason2 = new StopChequeReason();
		reason2.setReason("Account Attached");
		reason2.setReasonId("02");
		
		StopChequeReason reason3 = new StopChequeReason();
		reason3.setReason("Missing Cheque");
		reason3.setReasonId("03");
		
		StopChequeReason reason4 = new StopChequeReason();
		reason4.setReason("Stopped By Drawer");
		reason4.setReasonId("04");
		
		StopChequeReason reason5 = new StopChequeReason();
		reason5.setReason("Contract Not Executed");
		reason5.setReasonId("05");
		
		StopChequeReason reason6 = new StopChequeReason();
		reason6.setReason("Cheques Mutilated");
		reason6.setReasonId("06");
		
		StopChequeReason reason7 = new StopChequeReason();
		reason7.setReason("Others");
		reason7.setReasonId("07");
		
		stopChequeReasons[0] = reason1;
		stopChequeReasons[1] = reason2;
		stopChequeReasons[2] = reason3;
		stopChequeReasons[3] = reason4;
		stopChequeReasons[4] = reason5;
		stopChequeReasons[5] = reason6;
		stopChequeReasons[6] = reason7;

		businessMessage.setSelfServices(selfServices);
		businessMessage.getSelfServices().setStopChequeReasons(stopChequeReasons);
		
		return businessMessage;
		
	}

	public SelfServices getSelfServices() {
		return selfServices;
	}

	public void setSelfServices(SelfServices selfServices) {
		this.selfServices = selfServices;
	}

}
