package com.cit.mpaymentapp.cheque.managment.transformer;

import com.cit.mpaymentapp.cheque.managment.stop.StopChequeResponse;
import com.cit.mpaymentapp.common.message.BusinessMessage;
import com.cit.mpaymentapp.common.message.StatusType;
import com.cit.shared.error.util.ExceptionResolver;
import com.cit.shared.error.util.ExceptionUtil;

public class StopChequeResponseTransformer {
	private final String SUCCESS = "Success";
	private final String SUCCESS_MESSAGE = "Your request was submitted successfully ,please save the following number for follow up ";

	public Object onCall(BusinessMessage businessMessage, Object payload) throws Exception {

		ExceptionResolver exception = null;
		String errorCode = "EXT";

		if (!(payload instanceof StopChequeResponse)) {
			StatusType status = new StatusType();
			status.setErrorFlag(true);
			status.setStatusCode("EXT00810");
			businessMessage.setStatus(status);
			exception = ExceptionUtil.handle(errorCode);
		} else {
			StopChequeResponse stopChequeResponse = (StopChequeResponse) payload;
			if (stopChequeResponse.getSuccessmessage() == null && stopChequeResponse.getFailedmessage() != null) {
				StatusType status = new StatusType();
				status.setErrorFlag(true);
				status.setStatusMsg(stopChequeResponse.getFailedmessage());
				businessMessage.setStatus(status);
				exception = ExceptionUtil.handle(errorCode);
			} else if (stopChequeResponse.getSuccessmessage() != null) {
				businessMessage.getServiceSuccessResponse()
						.setSuccessDescription(stopChequeResponse.getSuccessmessage()+" "+stopChequeResponse.getTicketid());
				businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
			} else if (stopChequeResponse.getTicketid() != null) {
				businessMessage.getServiceSuccessResponse()
						.setSuccessDescription(SUCCESS_MESSAGE + stopChequeResponse.getTicketid());
				businessMessage.getServiceSuccessResponse().setSuccessShortDescription(SUCCESS);
			} else {
				businessMessage.getStatus().setErrorFlag(true);
				businessMessage.getStatus().setStatusCode("EXT00810");
			}
		}

		return businessMessage;
	}

}
