package com.cit.mpaymentapp.cheque.managment.type;

import java.io.Serializable;
import java.util.List;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.common.type.ChequeTypeResponse;

@JsonAutoDetect
public class ChequeTypes implements Serializable {

	private static final long serialVersionUID = 1L;
	List<ChequeTypeResponse> chequetypes;

	public List<ChequeTypeResponse> getChequetypes() {
		return chequetypes;
	}

	public void setChequetypes(List<ChequeTypeResponse> chequetypes) {
		this.chequetypes = chequetypes;
	}

}
