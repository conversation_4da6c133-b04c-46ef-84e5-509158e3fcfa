package com.cit.mpaymentapp.cheque.managment.stop;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.cheque.managment.cheque.ChequeManagment;

@JsonAutoDetect
public class StopChequeRequest implements Serializable,ChequeManagment{
	private static final long serialVersionUID = 1L;
	private String amount;
	private String beneficiary;
	private String reason;
	private String dateissued;
	private String chequeno;


	public String getAmount() {
		return amount;
	}


	public void setAmount(String amount) {
		this.amount = amount;
	}


	public String getBeneficiary() {
		return beneficiary;
	}


	public void setBeneficiary(String beneficiary) {
		this.beneficiary = beneficiary;
	}


	public String getReason() {
		return reason;
	}


	public void setReason(String reason) {
		this.reason = reason;
	}


	public String getDateissued() {
		return dateissued;
	}


	public void setDateissued(String dateissued) {
		this.dateissued = dateissued;
	}


	public String getChequeno() {
		return chequeno;
	}


	public void setChequeno(String chequeno) {
		this.chequeno = chequeno;
	}


	@Override
	public String toString() {
		return "StopChequeRequest [amount=" + amount + ", beneficiary="
				+ beneficiary + ", reason=" + reason + ", dateissued="
				+ dateissued + ", chequeno=" + chequeno + "]";
	}
}
