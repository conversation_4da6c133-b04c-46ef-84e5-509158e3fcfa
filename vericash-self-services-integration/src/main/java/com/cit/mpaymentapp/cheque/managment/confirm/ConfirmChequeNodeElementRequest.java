package com.cit.mpaymentapp.cheque.managment.confirm;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;

import com.cit.mpaymentapp.cheque.managment.cheque.ChequeManagment;

@JsonAutoDetect
public class ConfirmChequeNodeElementRequest implements Serializable,ChequeManagment{
	private static final long serialVersionUID = 1L;
	private ConfirmChequeRequest cheque;
	public ConfirmChequeRequest getCheque() {
		return cheque;
	}
	public void setCheque(ConfirmChequeRequest cheque) {
		this.cheque = cheque;
	}
	
}
