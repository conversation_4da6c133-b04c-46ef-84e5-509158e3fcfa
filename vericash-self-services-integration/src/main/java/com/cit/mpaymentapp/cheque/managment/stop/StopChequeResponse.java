package com.cit.mpaymentapp.cheque.managment.stop;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonAutoDetect;
import org.codehaus.jackson.annotate.JsonProperty;

@JsonAutoDetect
public class StopChequeResponse implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String ticketid;
	private String status;
	private String tat;
	private String successmessage;
	private String failedmessage;
	private String message;
	
	public String getTicketid() {
		return ticketid;
	}
	public void setTicketid(String ticketid) {
		this.ticketid = ticketid;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getTat() {
		return tat;
	}
	public void setTat(String tat) {
		this.tat = tat;
	}
	public String getSuccessmessage() {
		return successmessage;
	}
	public void setSuccessmessage(String successmessage) {
		this.successmessage = successmessage;
	}
	public String getFailedmessage() {
		return failedmessage;
	}
	public void setFailedmessage(String failedmessage) {
		this.failedmessage = failedmessage;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	
	

}
