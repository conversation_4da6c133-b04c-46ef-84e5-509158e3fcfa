<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">
		
		<flow name="Corebanking Get Account Complaints Transactions">

		<jms:listener config-ref="JMS_Config" destination="${jms.self.service.complaints.account.transactions.inbound}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>			   
			   <logger level="INFO" message="----------------------Start Corebanking Get Account Complaints Transactions --------------" doc:name="Logger"/>
			   <set-variable variableName="businessMessage" value="#[payload]" doc:name="Variable"/>
			   <set-variable value="#[correlationId]" variableName="correlationId" doc:name="Variable"/>
			   
				<java:invoke
					class="com.cit.mpayment.complaints.AccountTransactionsRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="accountTransactionsRequestGenerator">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>
			   
			   	<java:invoke
					class="com.cit.mpayment.SelfServicesBasicAuthenticationComponent"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="selfServicesBasicAuthenticationComponent"
					target="Authorization">
					<java:args><![CDATA[#[{ arg0: vars.businessMessage  }]]]></java:args>
				</java:invoke>	
			   
			   <set-variable variableName="queryParameters" value="#[payload]" doc:name="Variable" />
			   <set-payload value="#[&quot; &quot;]" doc:name="Set Payload"/>

			   <set-variable variableName="self_services_channel" value="${self_services_channel}"/>
			<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
			<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />		 
			<set-variable value="${http_get_account_transactions_by_channel}" variableName="http_get_account_transactions_by_channel"/>  
			<http:request method="GET" url="#[vars.http_get_account_transactions_by_channel ++ vars.queryParameters]" responseTimeout="${COREBANKING.TIMEOUT}">
				<http:headers ><![CDATA[#[output application/java
										---
										{
											Authorization : vars.Authorization,
											"channel" : vars.self_services_channel,
											"Content-Type" : "application/json"
										}]]]>
				</http:headers>
	
			</http:request>

		<set-variable variableName="responseSOAPMessage" value="#[payload]" />
		<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
				<java:invoke
					class="com.cit.mpayment.complaints.AccountTransactionsResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.cit.mpayment.complaints.AccountTransactionsResponse)"
					instance="accountTransactionsResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
		 	 				arg1: payload as Object {class: "com.cit.mpayment.complaints.AccountTransactionsResponse"}}]]]>
					</java:args>
				</java:invoke>					   
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
				
				<logger level="INFO" message="------------------ End Corebanking Get Account Complaints Transactions is : #[payload] -------------------------" doc:name="Logger"/>
	 
				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.complaints.account.transactions.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
					<jms:message correlationId="#[correlationId]">
						<jms:body>#[payload]</jms:body>
						<jms:properties>
							#[{
							MULE_CORRELATION_ID: vars.correlationId,
							MULE_CORRELATION_GROUP_SIZE: '-1',
							MULE_CORRELATION_SEQUENCE:'-1'
							}]</jms:properties>
					</jms:message>
				</jms:publish>			
		<error-handler ref="GetAccountTransactionsExceptionStrategy" doc:name="Reference Exception Strategy"/>
		</flow>
		
		<error-handler name="GetAccountTransactionsExceptionStrategy" >
			<on-error-continue>
				<logger level="ERROR" message=" ------------------ Start GetAccountTransactionsExceptionStrategy ---------------------" doc:name="Logger"/>
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
				<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
			   
			   <logger level="ERROR" message=" ------------------ End GetAccountTransactionsExceptionStrategy ---------------------" doc:name="Logger"/>

<!--				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.complaints.account.transactions.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--					<jms:message correlationId="#[correlationId]">-->
<!--						<jms:body>#[payload]</jms:body>-->
<!--						<jms:properties>-->
<!--							#[{-->
<!--							MULE_CORRELATION_ID: vars.correlationId,-->
<!--							MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--							MULE_CORRELATION_SEQUENCE:'-1'-->
<!--							}]</jms:properties>-->
<!--					</jms:message>-->
<!--				</jms:publish>	-->
				</on-error-continue>				   
		</error-handler>
		
		<flow name="Corebanking Send Complaint Request">
			   
			<jms:listener config-ref="JMS_Config" destination="${jms.self.service.complaint.request.inbound}" />
		
			<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
			<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
			<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
			<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>					   
			   <logger level="INFO" message="----------------------Start Send Complaint Request Flow --------------" doc:name="Logger"/>
			   <set-variable variableName="businessMessage" value="#[message.payload]" doc:name="Variable"/>
			   <set-variable value="#[correlationId]" variableName="correlationId" doc:name="Variable"/>

				<java:invoke
					class="com.cit.mpayment.complaints.ComplaintRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="complaintRequestGenerator">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>				   

			   
			<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
			<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />				
			   
				<java:invoke
					class="com.cit.mpayment.SelfServicesBasicAuthenticationComponent"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="selfServicesBasicAuthenticationComponent"
					target="Authorization">
					<java:args><![CDATA[#[{ arg0: vars.businessMessage  }]]]></java:args>
				</java:invoke>	
			   <set-variable value="${complaint_channelId}" variableName="complaint_channelId"/>

			<http:request method="POST" url="${http_send_complain_request}" responseTimeout="${COREBANKING.TIMEOUT}">
				<http:headers ><![CDATA[#[output application/java
										---
										{
											Authorization : vars.Authorization,
											"channel" : vars.complaint_channelId,
											"Content-Type" : "application/json"
										}]]]>
				</http:headers>
	
			</http:request>			   

			   <logger level="INFO" message="----- Complaint Response #[payload] --------------" doc:name="Logger"/>
				
		<set-variable variableName="responseSOAPMessage" value="#[payload]" />
		<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />			

				<java:invoke
					class="com.cit.mpayment.complaints.ComplaintResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.cit.mpayment.complaints.ServiceRequestsResponse)"
					instance="complaintResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
		 	 				arg1: payload as Object {class: "com.cit.mpayment.complaints.ServiceRequestsResponse"}}]]]>
					</java:args>
				</java:invoke>						
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
				
				
				
				<logger level="INFO" message="------------------ Corebanking Send Complaint Request is : #[payload] -------------------------" doc:name="Logger"/>
 
				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.complaint.request.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
					<jms:message correlationId="#[correlationId]">
						<jms:body>#[payload]</jms:body>
						<jms:properties>
							#[{
							MULE_CORRELATION_ID: vars.correlationId,
							MULE_CORRELATION_GROUP_SIZE: '-1',
							MULE_CORRELATION_SEQUENCE:'-1'
							}]</jms:properties>
					</jms:message>
				</jms:publish>			
		<error-handler ref="SendCompalintRequestExceptionStrategy" doc:name="Reference Exception Strategy"/>
		</flow>
		
		<error-handler name="SendCompalintRequestExceptionStrategy" >
			<on-error-continue>
				<logger level="ERROR" message=" ------------------ Start SendCompalintRequestExceptionStrategy ---------------------" doc:name="Logger"/>
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
				<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
				<logger level="ERROR" message=" ------------------ End SendCompalintRequestExceptionStrategy ---------------------" doc:name="Logger"/>
<!--				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.complaint.request.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--					<jms:message correlationId="#[correlationId]">-->
<!--						<jms:body>#[payload]</jms:body>-->
<!--						<jms:properties>-->
<!--							#[{-->
<!--							MULE_CORRELATION_ID: vars.correlationId,-->
<!--							MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--							MULE_CORRELATION_SEQUENCE:'-1'-->
<!--							}]</jms:properties>-->
<!--					</jms:message>-->
<!--				</jms:publish>	-->
			   </on-error-continue>
		</error-handler>
		
		<flow name="Get Requests and Complaints Categories Flow">
			   
			<jms:listener config-ref="JMS_Config" destination="${jms.self.service.get.complaint.categories.inbound}" />
		
			<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
			<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
			<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
			<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>	
							   
			   <logger level="INFO" message="----------------------Start Get Requests and Complaints Categories Flow --------------" doc:name="Logger"/>
			   <set-variable variableName="businessMessage" value="#[message.payload]" doc:name="Variable"/>
			   <set-variable value="#[correlationId]" variableName="correlationId" doc:name="Variable"/>

				<java:invoke
					class="com.cit.mpayment.complaints.ComplaintsCategoriesRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="complaintsCategoriesRequestGenerator">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>				   
			   <choice >
			   		<when expression="#[payload.softFields.'requireIntegration' == true]">
			   		
					   <set-variable variableName="queryParameters" value="#[payload.softFields.'queryParameters']" doc:name="Variable" />
					   			   			   
				<java:invoke
					class="com.cit.mpayment.SelfServicesBasicAuthenticationComponent"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="selfServicesBasicAuthenticationComponent"
					target="Authorization">
					<java:args><![CDATA[#[{ arg0: vars.businessMessage  }]]]></java:args>
				</java:invoke>	
				<set-variable variableName="requestSOAPMessage" value="#[message.payload]" />
			<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />					   
					   <set-payload value="#[&quot; &quot;]" doc:name="Set Payload"/>
		<set-variable value="${service_types_categories_address}" variableName="service_types_categories_address"/>
		<http:request method="GET" url="#[vars.service_types_categories_address ++ vars.queryParameters]" responseTimeout="${COREBANKING.TIMEOUT}">
			<http:headers ><![CDATA[#[output application/java
									---
									{
										Authorization : vars.complaint_channelId,
										"channel" : vars.self_services_channel
									}]]]>
			</http:headers>
		</http:request>					   

					   <logger level="INFO" message="----- Complaints Categories Response #[payload] --------------" doc:name="Logger"/>
						
		<set-variable variableName="responseSOAPMessage" value="#[payload]" />
		<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />			

				<java:invoke
					class="com.cit.mpayment.complaints.ComplaintsCategoriesResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.cit.mpaymentapp.common.self.services.complaints.ServiceCategoryTypes)"
					instance="complaintsCategoriesResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
		 	 				arg1: payload as Object {class: "com.cit.mpaymentapp.common.self.services.complaints.ServiceCategoryTypes"}}]]]>
					</java:args>
				</java:invoke>								
						<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
					</when>
			   		<otherwise>
			   			<logger level="INFO" message="----------------------End Get Requests and Complaints Categories Flow --------------" doc:name="Logger"/>
			   		</otherwise>
			   </choice>	
 
				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.get.complaint.categories.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
					<jms:message correlationId="#[correlationId]">
						<jms:body>#[payload]</jms:body>
						<jms:properties>
							#[{
							MULE_CORRELATION_ID: vars.correlationId,
							MULE_CORRELATION_GROUP_SIZE: '-1',
							MULE_CORRELATION_SEQUENCE:'-1'
							}]</jms:properties>
					</jms:message>
				</jms:publish>		
		<error-handler ref="GetComplaintsCategoriesStrategy" doc:name="Reference Exception Strategy"/>
		</flow>
		
		<error-handler name="GetComplaintsCategoriesStrategy" >
			<on-error-continue>
				<logger level="ERROR" message=" ------------------ Start GetComplaintsCategoriesStrategy ---------------------" doc:name="Logger"/>
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
				<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
				<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
				<logger level="ERROR" message=" ------------------ End GetComplaintsCategoriesStrategy ---------------------" doc:name="Logger"/>

<!--				<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.get.complaint.categories.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--					<jms:message correlationId="#[correlationId]">-->
<!--						<jms:body>#[payload]</jms:body>-->
<!--						<jms:properties>-->
<!--							#[{-->
<!--							MULE_CORRELATION_ID: vars.correlationId,-->
<!--							MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--							MULE_CORRELATION_SEQUENCE:'-1'-->
<!--							}]</jms:properties>-->
<!--					</jms:message>-->
<!--				</jms:publish>			   -->
				</on-error-continue>
		</error-handler>
	
	</mule>


	
