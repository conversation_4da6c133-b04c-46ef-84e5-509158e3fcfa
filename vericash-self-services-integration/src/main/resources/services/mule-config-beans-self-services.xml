<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
    http://www.springframework.org/schema/beans/spring-beans.xsd 
    http://www.springframework.org/schema/context 
    http://www.springframework.org/schema/context/spring-context.xsd
    http://www.springframework.org/schema/util 
    http://www.springframework.org/schema/util/spring-util.xsd">

	<bean id="jndiConfiguration"
		class="org.springframework.jndi.JndiTemplate">
		<property name="environment">
			<props>
				<prop key="java.naming.factory.url.pkgs">org.jboss.ejb.client.naming
				</prop>
			</props>
		</property>
	</bean>
	
	<bean id="selfServiceAccountRequestGenerator" class="com.cit.mpayment.components.accounts.SelfServiceAccountRequestGenerator">
		<property name="selfServiceProperties">
            <util:properties location="classpath:properties/self-services.properties" />
        </property>
	</bean>

	<bean id="selfServiceAccountResponseValidator" class="com.cit.mpayment.components.accounts.SelfServiceAccountResponseValidator" />

	<bean id="selfServiceConfirmChequeRequestGenerator" class="com.cit.mpayment.components.confirm.SelfServiceConfirmChequeRequestGenerator" />


	<bean id="selfServiceConfirmChequeResponseValidator" class="com.cit.mpayment.components.confirm.SelfServiceConfirmChequeResponseValidator" />


	<bean name="businessMessageToStopChequeRequestObjTransformer" class="com.cit.mpaymentapp.cheque.managment.transformer.BusinessMessageToStopChequeRequestObjTransformer" />

	<bean name="stopChequeResponseTransformerComponent" class="com.cit.mpaymentapp.cheque.managment.transformer.StopChequeResponseTransformer" />

	<bean id="transactionHistoryChannelMapperManager" class="org.springframework.ejb.access.SimpleRemoteStatelessSessionProxyFactoryBean">
		<property name="jndiName" value="ejb:mpaymentapp-ear/vericash-self-services-service-1.0.0.0//TransactionHistoryChannelMapperManagerImpl!com.cit.mpaymentapp.common.TransactionHistoryChannelMapperRemote" />
		<property name="businessInterface" value="com.cit.mpaymentapp.common.TransactionHistoryChannelMapperManager" />
		<property name="jndiTemplate" ref="jndiConfiguration" />
	</bean>

	<bean id="accountTransactionsResponseValidator" class="com.cit.mpayment.complaints.AccountTransactionsResponseValidator" />
	<bean id="accountTransactionsRequestGenerator" class="com.cit.mpayment.complaints.AccountTransactionsRequestGenerator">
		<property name="transactionHistoryChannelMapperManager" ref="transactionHistoryChannelMapperManager" />
	</bean>
	<bean id="complaintRequestGenerator" class="com.cit.mpayment.complaints.ComplaintRequestGenerator" />
	<bean id="complaintResponseValidator" class="com.cit.mpayment.complaints.ComplaintResponseValidator" />
	<bean id="ticketInquiryRequestGenerator" class="com.cit.mpayment.ticketInquiry.TicketInquiryRequestGenerator" />
	<bean id="ticketInquiryResponseValidator" class="com.cit.mpayment.ticketInquiry.TicketInquiryResponseValidator" />
	<bean id="chequeTypeRequestGenerator" class="com.cit.mpaymentapp.cheque.managment.type.ChequeTypeRequestGenerator" />
	<bean id="chequeTypeResponseValidator" class="com.cit.mpaymentapp.cheque.managment.type.ChequeTypeResponseValidator" />

	<bean id="stopChequeReasonsMock" class="com.cit.mpaymentapp.cheque.managment.stop.StopChequeReasonsMock">
		<property name="selfServices">
			<bean class="com.cit.mpaymentapp.common.self.services.SelfServices" />
		</property>
	</bean>

	<bean id="listAccountsResponseValidator" class="com.cit.mpaymentapp.cardmngt.accounts.ListAccountsResponseValidator" />

	<bean id="listAccountsRequestGenerator" class="com.cit.mpaymentapp.cardmngt.accounts.ListAccountsRequestGenerator" />

	<bean id="listCardsResponseValidator" class="com.cit.mpaymentapp.cardmngt.cards.ListCardsResponseValidator" />

	<bean id="listCardsRequestGenerator" class="com.cit.mpaymentapp.cardmngt.cards.ListCardsRequestGenerator" />

	<bean id="blockCardReasonMock" class="com.cit.mpaymentapp.cardmngt.block.card.BlockCardReasonMock">
		<property name="selfServices">
			<bean class="com.cit.mpaymentapp.common.self.services.SelfServices" />
		</property>
	</bean>


	<bean id="blockCardRequestGenerator" class="com.cit.mpaymentapp.cardmngt.block.card.BlockCardRequestGenerator">
	</bean>

	<bean id="blockCardResponseValidator" class="com.cit.mpaymentapp.cardmngt.block.card.BlockCardResponseValidator" />

	<bean id="selfServicesBasicAuthenticationComponent" class="com.cit.mpayment.SelfServicesBasicAuthenticationComponent">
		<property name="selfServiceProperties">
            <util:properties location="classpath:properties/self-services.properties" />
        </property>
	</bean>

	<bean id="complaintsCategoriesRequestGenerator" class="com.cit.mpayment.complaints.ComplaintsCategoriesRequestGenerator">
		<property name="cashedServiceRequestsCategories" ref="cashedServiceRequestsCategories" />
	</bean>

	<bean id="complaintsCategoriesResponseValidator" class="com.cit.mpayment.complaints.ComplaintsCategoriesResponseValidator">
		<property name="cashedServiceRequestsCategories" ref="cashedServiceRequestsCategories" />
	</bean>

	<bean id="newChequesBooksRequestGenerator" class="com.cit.mpayment.newchequesbooks.NewChequesBooksRequestGenerator">
		<property name="newChequesBooksRequestBody" ref="newChequesBooksRequestBody" />
		<property name="branch" ref="branch" />
		<property name="customerDetails" ref="customerDetails" />
		<property name="createAccountRequest" ref="createAccountRequest" />
		<property name="fullRequest" ref="fullRequest" />
		<property name="simpleDateFormat">
			<bean class="java.text.SimpleDateFormat">
				<constructor-arg value="dd-MM-yyyy" />
			</bean>
		</property>
	</bean>

	<bean id="newChequesBooksRequestBody" class="com.cit.mpayment.newchequesbooks.NewChequesBooksRequestBody"></bean>
	<bean id="branch" class="com.cit.mpayment.newchequesbooks.Branch"></bean>
	<bean id="customerDetails" class="com.cit.mpayment.customer.CustomerDetails"></bean>
	<bean id="createAccountRequest" class="com.cit.mpayment.complaints.ServiceRequestsRequest"></bean>
	<bean id="fullRequest" class="com.cit.mpaymentapp.cheque.managment.cheque.ServiceRequestsFullRequest"></bean>


	<bean id="newChequesBooksResponseValidator" class="com.cit.mpayment.newchequesbooks.NewChequesBooksResponseValidator" />
	<bean id="statesAndBranchesRequestGenerator" class="com.cit.mpayment.statesandbranches.StatesAndBranchesRequestGenerator" />
	<bean id="statesAndBranchesResponseValidator" class="com.cit.mpayment.statesandbranches.StatesAndBranchesResponseValidator" />

	<bean id="cashedServiceRequestsCategories" class="com.cit.mpayment.CashedServiceRequestsCategories" />
	<bean id="cardTypesRequestGenerator" class="com.cit.mpaymentapp.cardmngt.cards.CardTypesRequestGenerator" />
	<bean id="cardTypesResponseValidator" class="com.cit.mpaymentapp.cardmngt.cards.CardTypesResponseValidator" />
	<bean id="newCardRequestGenerator" class="com.cit.mpaymentapp.cardmngt.cards.NewCardRequestGenerator" />
	<bean id="newCardResponseValidator" class="com.cit.mpaymentapp.cardmngt.cards.NewCardResponseValidator" />
	<bean id="ignoreSpecialCharacter" class="com.cit.mpayment.IgnoreSpecialCharacter" />


</beans>
