<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

	<flow name="Corebanking new cheques books flow">

		<jms:listener config-ref="JMS_Config" destination="${jms.self.service.newChequesBooks.request.inbound}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>					
		<logger level="INFO" message="----------------------Start new cheques books Flow --------------"  />
		<set-variable variableName="businessMessage" value="#[payload]" />
		<set-variable value="#[correlationId]" variableName="correlationId" />

				<java:invoke
					class="com.cit.mpayment.newchequesbooks.NewChequesBooksRequestGenerator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="newChequesBooksRequestGenerator">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>	        

		
		<set-variable variableName="requestSOAPMessage" value="#[payload]" />		
		<set-variable variableName="requestSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
				<java:invoke
					class="com.cit.mpayment.SelfServicesBasicAuthenticationComponent"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="selfServicesBasicAuthenticationComponent"
					target="Authorization">
					<java:args><![CDATA[#[{ arg0: vars.businessMessage  }]]]></java:args>
				</java:invoke>	

		<set-variable variableName="self_services_channel" value="${self_services_channel}"/>
		<http:request method="POST" url="${http_send_new_cheques_books_request}" responseTimeout="${COREBANKING.TIMEOUT}">
			<http:headers ><![CDATA[#[output application/java
									---
									{
										Authorization : vars.Authorization,
										"channel" : vars.self_services_channel,
										"Content-Type" : "application/json"
									}]]]>
			</http:headers>
		</http:request>		
		<set-variable variableName="responseSOAPMessage" value="#[payload]" />
		<set-variable variableName="responseSOAPMessageDate" value="#[now() as String {format: &quot;dd-MMM-yyyy HH:mm:ss:SSS&quot;}]" />
		<logger level="INFO" message="----- newChequesBooks Response #[payload] --------------"/>

				<java:invoke
					class="com.cit.mpayment.newchequesbooks.NewChequesBooksResponseValidator"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage,com.cit.mpayment.complaints.ServiceRequestsResponse)"
					instance="newChequesBooksResponseValidator">
					<java:args>
						<![CDATA[#[{ arg0: vars.businessMessage,
		 	 				arg1: payload as Object {class: "com.cit.mpayment.complaints.ServiceRequestsResponse"}}]]]>
					</java:args>
				</java:invoke>	
		<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>

		<logger level="INFO" message="------------------ Corebanking newChequesBooks Response is : #[payload] -------------------------"/>

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.newChequesBooks.request.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="newChequesBooksFlowExceptionStrategy"/>
	</flow>

	<error-handler name="newChequesBooksFlowExceptionStrategy">
	<on-error-continue>
		<logger level="ERROR" message=" ------------------ Start newChequesBooksFlowExceptionStrategy ---------------------"/>
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
		<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
		<flow-ref name="Invoke_Integration_ErrorHandler_Component" />

		<logger level="ERROR" message=" ------------------ End newChequesBooksFlowExceptionStrategy ---------------------"/>

<!--		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.newChequesBooks.request.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--			<jms:message correlationId="#[correlationId]">-->
<!--				<jms:body>#[payload]</jms:body>-->
<!--				<jms:properties>-->
<!--					#[{-->
<!--					MULE_CORRELATION_ID: vars.correlationId,-->
<!--					MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--					MULE_CORRELATION_SEQUENCE:'-1'-->
<!--					}]</jms:properties>-->
<!--			</jms:message>-->
<!--		</jms:publish>			-->
		</on-error-continue>
	</error-handler>

</mule>


	
