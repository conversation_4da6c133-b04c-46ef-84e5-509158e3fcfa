<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" 
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:jms="http://www.mulesoft.org/schema/mule/jms" 
	xmlns:java="http://www.mulesoft.org/schema/mule/java" 
	xmlns:script="http://www.mulesoft.org/schema/mule/scripting" 
	xmlns:wsc="http://www.mulesoft.org/schema/mule/wsc"
	xmlns:http="http://www.mulesoft.org/schema/mule/http" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
	 http://www.mulesoft.org/schema/mule/jms http://www.mulesoft.org/schema/mule/jms/current/mule-jms.xsd
	 http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd
	 http://www.mulesoft.org/schema/mule/scripting http://www.mulesoft.org/schema/mule/scripting/current/mule-scripting.xsd
	 http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
	 http://www.mulesoft.org/schema/mule/wsc http://www.mulesoft.org/schema/mule/wsc/current/mule-wsc.xsd">

	<flow name="Corebanking stop cheque reasons Request">

		<jms:listener config-ref="JMS_Config" destination="${jms.self.service.stopChequeReasons.request.inbound}" />
		
		<set-variable value="#[attributes.properties.all.serviceStackId]" variableName="serviceStackId"/>
		<set-variable value="#[attributes.properties.all.reversedServiceId]" variableName="reversedServiceId"/>
		<set-variable value="#[attributes.properties.all.currentStep]" variableName="currentStep"/>
		<set-variable value="#[attributes.properties.all.stepOrder]" variableName="stepOrder"/>					
		<logger level="INFO" message="----------------------Start Send stop cheque reasons Request Flow --------------" />
		<set-variable variableName="businessMessage" value="#[payload]" />
		<set-variable value="#[correlationId]" variableName="correlationId" />

				<java:invoke
					class="com.cit.mpaymentapp.cheque.managment.stop.StopChequeReasonsMock"
					method="onCall(com.cit.mpaymentapp.common.message.BusinessMessage)"
					instance="stopChequeReasonsMock">
					<java:args><![CDATA[#[{ arg0: payload }]]]></java:args>
				</java:invoke>	

		<logger level="INFO" message="----- stop cheque reasons Response #[payload] --------------"/>

		<set-variable variableName="responseSOAPMessage" value="#[payload]" />

		<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>

		<logger level="INFO" message="------------------ Corebanking stop cheque reasons Response is : #[payload] -------------------------"/>

		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.stopChequeReasons.request.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">
			<jms:message correlationId="#[correlationId]">
				<jms:body>#[payload]</jms:body>
				<jms:properties>
					#[{
					MULE_CORRELATION_ID: vars.correlationId,
					MULE_CORRELATION_GROUP_SIZE: '-1',
					MULE_CORRELATION_SEQUENCE:'-1'
					}]</jms:properties>
			</jms:message>
		</jms:publish>
		<error-handler ref="SendStopChequeReasonsRequestExceptionStrategy"/>
	</flow>

	<error-handler name="SendStopChequeReasonsRequestExceptionStrategy">
	<on-error-continue>
		<logger level="ERROR" message=" ------------------ Start SendStopChequeReasonsRequestExceptionStrategy ---------------------"  />
		<choice>
			<when expression="#[error.errorType.identifier == 'TIMEOUT' or error.cause == 'java.net.SocketTimeoutException']">
				<set-variable value="true" variableName="isTimeoutException" /> 
			</when>
		</choice>
		<flow-ref name="Invoke_External_SOAP_Messages_Manager_SQL_Component"/>
		<flow-ref name="Invoke_Integration_ErrorHandler_Component" />
		<logger level="ERROR" message=" ------------------ End SendStopChequeReasonsRequestExceptionStrategy ---------------------"  />

<!--		<jms:publish timeToLive="${mule.step.timeToLive}" timeToLiveUnit="MILLISECONDS" destination="${jms.self.service.stopChequeReasons.request.outbound}" config-ref="JMS_Config" sendCorrelationId="ALWAYS">-->
<!--			<jms:message correlationId="#[correlationId]">-->
<!--				<jms:body>#[payload]</jms:body>-->
<!--				<jms:properties>-->
<!--					#[{-->
<!--					MULE_CORRELATION_ID: vars.correlationId,-->
<!--					MULE_CORRELATION_GROUP_SIZE: '-1',-->
<!--					MULE_CORRELATION_SEQUENCE:'-1'-->
<!--					}]</jms:properties>-->
<!--			</jms:message>-->
<!--		</jms:publish>			-->
		</on-error-continue>
	</error-handler>

</mule>


	
