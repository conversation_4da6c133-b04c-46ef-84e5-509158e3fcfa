<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Request">
    <xs:complexType>
      <xs:sequence>
        <xs:element type="xs:string" name="PrincipalID"/>
        <xs:element type="xs:string" name="BankID"/>
        <xs:element type="xs:string" name="CustomerID"/>
        <xs:element type="xs:string" name="BVN"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
