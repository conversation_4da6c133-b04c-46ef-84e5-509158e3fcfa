
package confirmchequeservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "res"
})
@XmlRootElement(name = "processResponse")
public class ProcessResponse {

    @XmlElement(required = true, nillable = true)
    protected String res;

    public String getRes() {
        return res;
    }

    public void setRes(String value) {
        this.res = value;
    }

}
