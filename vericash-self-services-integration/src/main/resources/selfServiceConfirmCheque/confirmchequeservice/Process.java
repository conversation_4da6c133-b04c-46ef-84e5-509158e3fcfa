
package confirmchequeservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "req"
})
@XmlRootElement(name = "process")
public class Process {

    @XmlElement(required = true, nillable = true)
    protected String req;

    public String getReq() {
        return req;
    }

    public void setReq(String value) {
        this.req = value;
    }

}
