
package confirmchequeservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


@WebService(name = "ConfirmChequeService", targetNamespace = "http://ConfirmChequeService")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface ConfirmChequeService {


    @WebMethod(action = "http://ConfirmChequeService/process")
    @WebResult(name = "res", targetNamespace = "")
    @RequestWrapper(localName = "process", targetNamespace = "http://ConfirmChequeService", className = "confirmchequeservice.Process")
    @ResponseWrapper(localName = "processResponse", targetNamespace = "http://ConfirmChequeService", className = "confirmchequeservice.ProcessResponse")
    public String process(
        @WebParam(name = "req", targetNamespace = "")
        String req);

}
