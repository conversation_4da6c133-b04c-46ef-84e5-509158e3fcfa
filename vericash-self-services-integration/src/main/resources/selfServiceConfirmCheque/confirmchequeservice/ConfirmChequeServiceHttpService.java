
package confirmchequeservice;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.logging.Logger;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;


@WebServiceClient(name = "ConfirmChequeServiceHttpService", targetNamespace = "http://ConfirmChequeService", wsdlLocation = "classpath:selfServiceConfirmCheque/ConfirmChequeService_wsdl.wsdl")
public class ConfirmChequeServiceHttpService
    extends Service
{

    private final static URL CONFIRMCHEQUESERVICEHTTPSERVICE_WSDL_LOCATION;
    private final static Logger logger = Logger.getLogger(confirmchequeservice.ConfirmChequeServiceHttpService.class.getName());

    static {
        URL url = null;
        try {
            URL baseUrl;
            baseUrl = confirmchequeservice.ConfirmChequeServiceHttpService.class.getResource(".");
            url = ConfirmChequeServiceHttpService.class.getClassLoader().getResource("selfServiceConfirmCheque/ConfirmChequeService_wsdl.wsdl")
        } catch (MalformedURLException e) {
            logger.warning("Failed to create URL for the wsdl Location: 'selfServiceConfirmCheque/ConfirmChequeService_wsdl.wsdl', retrying as a local file");
            logger.warning(e.getMessage());
        }
        CONFIRMCHEQUESERVICEHTTPSERVICE_WSDL_LOCATION = url;
    }

    public ConfirmChequeServiceHttpService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ConfirmChequeServiceHttpService() {
        super(CONFIRMCHEQUESERVICEHTTPSERVICE_WSDL_LOCATION, new QName("http://ConfirmChequeService", "ConfirmChequeServiceHttpService"));
    }

    @WebEndpoint(name = "ConfirmChequePort")
    public ConfirmChequeService getConfirmChequePort() {
        return super.getPort(new QName("http://ConfirmChequeService", "ConfirmChequePort"), ConfirmChequeService.class);
    }

    @WebEndpoint(name = "ConfirmChequePort")
    public ConfirmChequeService getConfirmChequePort(WebServiceFeature... features) {
        return super.getPort(new QName("http://ConfirmChequeService", "ConfirmChequePort"), ConfirmChequeService.class, features);
    }

}
