<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Request">
    <xs:complexType>
      <xs:sequence>
        <xs:element type="xs:string" name="PrincipalID"/>
        <xs:element type="xs:string" name="AccountNumber"/>
        <xs:element type="xs:string" name="Amount"/>
        <xs:element type="xs:string" name="BeneficiaryName"/>
        <xs:element type="xs:string" name="ChequeDate"/>
        <xs:element type="xs:string" name="ChequeNumber"/>
        <xs:element type="xs:string" name="RequestingSol"/>
        <xs:element type="xs:string" name="BankID"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>