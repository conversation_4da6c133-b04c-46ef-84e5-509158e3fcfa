<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
	name="ConfirmChequeService" targetNamespace="http://ConfirmChequeService"
	xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
	xmlns:tns="http://ConfirmChequeService" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsdl:documentation>
		<wsdl:appinfo source="WMQI_APPINFO">
			<MRWSDLAppInfo imported="true">
				<binding hasEncoding="false" imported="true"
					name="ConfirmChequeServiceHttpBinding" originalBindingStyle="document" />
				<generatedXSD location="ConfirmChequeService_InlineSchema1.xsd" />
			</MRWSDLAppInfo>
		</wsdl:appinfo>
	</wsdl:documentation>


	<wsdl:types>
        <xsd:schema targetNamespace="http://ConfirmChequeService" xmlns:ibmSchExtn="http://www.ibm.com/schema/extensions">
      <xsd:include schemaLocation="ConfirmChequeService.xsd"/>
    </xsd:schema>
    </wsdl:types>
	<wsdl:message name="processRequestMsg">
		<wsdl:part element="tns:process" name="processParameters" />
	</wsdl:message>
	<wsdl:message name="processResponseMsg">
		<wsdl:part element="tns:processResponse" name="processResult" />
	</wsdl:message>
	<wsdl:portType name="ConfirmChequeService">
		<wsdl:operation name="process">
			<wsdl:input message="tns:processRequestMsg" name="processRequest" />
			<wsdl:output message="tns:processResponseMsg" name="processResponse" />
		</wsdl:operation>
	</wsdl:portType>

	<wsdl:binding name="ConfirmChequeServiceHttpBinding"
		type="tns:ConfirmChequeService">
		<soap:binding style="document"
			transport="http://schemas.xmlsoap.org/soap/http" />
		<wsdl:operation name="process">
			<soap:operation soapAction="http://ConfirmChequeService/process" />
			<wsdl:input name="processRequest">
				<soap:body use="literal" />
			</wsdl:input>
			<wsdl:output name="processResponse">
				<soap:body use="literal" />
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>

	<wsdl:service name="ConfirmChequeServiceHttpService">
		<wsdl:port binding="tns:ConfirmChequeServiceHttpBinding"
			name="ConfirmChequePort">
			<soap:address location="http://************:7808/ConfirmChequeService/Process" />
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>