############### request new account ################
jms.self.service.request.new.account.inbound=jms/self/service/request/new/account/inbound
jms.self.service.request.new.account.outbound=jms/self/service/request/new/account/outbound
REQUEST.NEW.ACCOUNT.COREBANKING.CLIENTCLASS=com.cit.mpayment.accounts.service.API.accountopeningservice.AccountOpeningServiceHttpService
REQUEST.NEW.ACCOUNT.COREBANKING.PORT=AccountOpeningServiceHttpPort
REQUEST.NEW.ACCOUNT.COREBANKING.OPERATION=process
############### request Cheque Managment ################
jms.service.stop.cheque.request=jms/queue/stopChequeServiceInputQueue
jms.service.stop.cheque.response=jms/queue/stopChequeServiceOutputQueue
jms.service.confirm.cheque.request=jms/queue/confirmChequeServiceInputQueue
jms.service.confirm.cheque.response=jms/queue/confirmChequeServiceOutputQueue
jms.service.cheque.type.request=jms/queue/ChequeTypeServiceInputQueue
jms.service.cheque.type.response=jms/queue/ChequeTypeServiceOutputQueue
REQUEST.CONFIRM.CHEQUE.COREBANKING.CLIENTCLASS=com.cit.mpayment.confirm.cheque.service.API.confirmchequeservice.ConfirmChequeServiceHttpService
REQUEST.CONFIRM.CHEQUE.COREBANKING.PORT=ConfirmChequePort
REQUEST.CONFIRM.CHEQUE.COREBANKING.OPERATION=process
COREBANKING.TIMEOUT=40000
BankID=NG
PrincibleID=Test_User
####################### Complaints #####################
jms.self.service.complaints.account.transactions.inbound=jms/self/service/account/transactions/complaints/inbound
jms.self.service.complaints.account.transactions.outbound=jms/self/service/account/transactions/complaints/outbound
jms.self.service.complaint.request.inbound=jms/self/service/send/complaint/request/inbound
jms.self.service.complaint.request.outbound=jms/self/service/send/complaint/request/outbound
jms.self.service.get.complaint.categories.inbound=jms/self/service/get/complaint/categories/inbound
jms.self.service.get.complaint.categories.outbound=jms/self/service/get/complaint/categories/outbound
################# Block Card - List Accounts - List Cards#############
jms.service.list.accounts.request=jms/queue/listAccountsServiceInputQueue
jms.service.list.accounts.response=jms/queue/listAccountsServiceOutputQueue
jms.service.list.cards.request=jms/queue/listCardsServiceInputQueue
jms.service.list.cards.response=jms/queue/listCardsServiceOutputQueue
jms.service.block.card.request=jms/queue/blockCardServiceInputQueue
jms.service.block.card.response=jms/queue/blockCardServiceOutputQueue
jms.service.block.card.reasons.request=jms/queue/blockCardReasonServiceInputQueue
jms.service.block.card.reasons.response=jms/queue/blockCardReasonServiceOutputQueue
REQUEST.CARDMNGT.COREBANKING.CLIENTCLASS=com.cit.mpaymentapp.cardmngt.service.CardMngtHttpService
REQUEST.CARDMNGT.COREBANKING.PORT=CardMngtHttpPort
REQUEST.CARDMNGT.COREBANKING.OPERATION=execute
self_services_channel=2002
self_services_auth_token=Basic TUI6TUJfMTIzNDUu
#### Block Card Request - Default Values ####
HoldRespCode=43
MessageType=CARDUPDATE
HoldRspCode=43
CardStatus=9
####################### Ticket inquiry ################
jms.self.service.ticket.inquiry.request.inbound=jms/self/service/send/ticketInquiry/request/inbound
jms.self.service.ticket.inquiry.request.outbound=jms/self/service/send/ticketInquiry/request/outbound
##################### Stop cheque reasons #######################
jms.self.service.stopChequeReasons.request.inbound=jms/self/service/send/stopChequeReasons/request/inbound
jms.self.service.stopChequeReasons.request.outbound=jms/self/service/send/stopChequeReasons/request/outbound
#######################################################
self.services.auth.username=MB
self.services.auth.password=MB_12345.
###################### Get all branches of a country ################
jms.self.service.statesAndBranches.request.inbound=jms/self/service/send/statesAndBranches/request/inbound
jms.self.service.statesAndBranches.request.outbound=jms/self/service/send/statesAndBranches/request/outbound
##################### Create new cheque books #######################
jms.self.service.newChequesBooks.request.inbound=jms/self/service/send/newChequesBooks/request/inbound
jms.self.service.newChequesBooks.request.outbound=jms/self/service/send/newChequesBooks/request/outbound
####################  Authorization Paramater #################
Authorization=Basic TUI6TUJfMTIzNDUu
###################### Get Card Types ################
jms.service.card.types.request=jms/queue/cardTypesServiceInputQueue
jms.service.card.types.response=jms/queue/cardTypesServiceOutputQueue
###################### Request New Card ################
jms.service.new.card.request=jms/queue/newCardServiceInputQueue
jms.service.new.card.response=jms/queue/newCardServiceOutputQueue