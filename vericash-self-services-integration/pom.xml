<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.cit.mpaymentapp.vericash.self.services</groupId>
		<artifactId>vericash-self-services</artifactId>
		<version>1.0.0.0</version>
		<relativePath>../vericash-self-services</relativePath>

	</parent>

	<name>Vericash-self-services-Integration</name>
	<artifactId>vericash-self-services-integration</artifactId>

	<properties>
		<org.mule.mule-core.version>3.4.0</org.mule.mule-core.version>
		<springContextVersion>3.1.2.RELEASE</springContextVersion>
		<httpclientVersion>3.1</httpclientVersion>
		<munit.version>1.1.0</munit.version>
	</properties>

	<dependencies>
	 <dependency>
			<groupId>com.cit.mpaymentapp.vericash.self.services</groupId>
			<artifactId>vericash-self-services-common</artifactId>
			<version>${project.parent.version}</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.cit.mpaymentapp.vericash.self.services</groupId>
			<artifactId>vericash-self-services-model</artifactId>
			<version>${project.parent.version}</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.cit.mpaymentapp</groupId>
			<artifactId>mpaymentapp-common</artifactId>
			<version>1.0.3-SNAPSHOT</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.cit.shared</groupId>
			<artifactId>ErrorHandler</artifactId>
			<version>1.0.3-SNAPSHOT</version>
			<scope>provided</scope>
		</dependency>

		
    	<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-entitymanager</artifactId>
			<version>4.1.4.Final</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>4.1.4.Final</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.jboss.spec.javax.ejb</groupId>
			<artifactId>jboss-ejb-api_3.1_spec</artifactId>
			<version>1.0.1.Final</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>${httpclientVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.2.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.mvel</groupId>
			<artifactId>mvel2</artifactId>
			<version>2.0.16</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>2.6.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>me.prettyprint</groupId>
			<artifactId>hector-core</artifactId>
			<version>1.0-5</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>javax</groupId>
			<artifactId>javaee-api</artifactId>
			<version>6.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.geronimo.specs</groupId>
			<artifactId>geronimo-j2ee-connector_1.5_spec</artifactId>
			<version>1.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis</artifactId>
			<version>1.4</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>net.sf.dozer</groupId>
			<artifactId>dozer</artifactId>
			<version>5.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>31.1-android</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.3.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.3.5</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<version>3.0</version>
			<type>jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.hamcrest</groupId>
			<artifactId>hamcrest-core</artifactId>
			<version>1.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
			<version>${springContextVersion}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-core</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-api</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-frontend-jaxws</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-management</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-ws-security</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-local</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-databinding-aegis</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-databinding-jaxb</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-tools-common</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-ws-rm</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-wstx-msv-validation</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-databinding-jibx</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-transports-common</artifactId>
			<version>2.5.1</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-osgi</artifactId>
			<version>2.2.4</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.mule.tests</groupId>
			<artifactId>mule-tests-functional</artifactId>
			<version>3.4.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.mule.tools</groupId>
			<artifactId>mule-tools-anttasks</artifactId>
			<version>3.4.0</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.btm</groupId>
			<artifactId>btm</artifactId>
			<version>2.1.4</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.6.11</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.jboss</groupId>
			<artifactId>jboss-ejb-client</artifactId>
			<version>2.1.2.Final</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.mule</groupId>
			<artifactId>mule-core</artifactId>
			<version>${org.mule.mule-core.version}</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.mule.transports</groupId>
			<artifactId>mule-transport-quartz</artifactId>
			<version>${org.mule.mule-core.version}</version>
			<type>jar</type>
			<scope>provided</scope>
		</dependency>
		
		<dependency>
			<groupId>com.cit.shared</groupId>
			<artifactId>ErrorHandler</artifactId>
			<version>1.0.3-SNAPSHOT</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.9</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.2</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-engine</artifactId>
			<version>5.8</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bctsp-jdk14</artifactId>
			<version>1.46</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.2.2</version>
			<scope>provided</scope>
		</dependency>

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.6.1</version>
				<configuration>
					<source>11</source>
					<target>11</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
